import { <PERSON>Bus, ActionHandler } from '~/common/actions';
import { EventBus } from '~/common/events';
import { Provider } from '~/common/providers';
import { string } from '~/common/schema';
import { groupBy } from '~/common/utils/collection';
import { ExecuteTask } from './ai-actions';
import { AIAgent } from './ai-agent';
import { AIClient } from './ai-client';
import { Instructions } from './ai-instructions';
import { functionTool } from './ai-tools';

const instructions: Instructions[] = [
  'You are top-class AI agent orchestrator that can execute complex tasks using the tools (agents) provided.',
  {
    title: 'Instructions',
    items: [
      'Agents help you to solve subtasks. Distribute the subtasks to the appropriate agents and compose the final answer.',
      `Provide instructions to each agent as a natural language prompt (in English), keep it short and concise, but be precise (don't loose details).`,
      'Always provide the result of the previous tool to next tool.',
      'Respond in the same language as used in the initial user prompt.'
    ]
  }
];

/**
 * Orchestrator agent that can execute task using available actions (as sub-agents)
 */
@Provider()
export class AIOrchestrator {
  private agent?: AIAgent;

  constructor(
    private client: AIClient,
    private actionBus: ActionBus,
    private eventBus: EventBus
  ) {}

  /**
   * Execute task using available actions (as agents)
   */
  @ActionHandler(ExecuteTask)
  execute(prompt: string) {
    if (!this.agent) {
      this.agent = this.createAgent();
    }
    return this.agent.execute(prompt);
  }

  private createAgent() {
    const actionsByModule = groupBy(
      this.actionBus.getActions().filter((action) => Boolean(action.description)),
      (action) => action.name.split('.')[0].split(':')[0]
    );
    return this.client.createAgent({
      eventBus: this.eventBus.span('Orchestrator'),
      instructions,
      functions: Object.entries(actionsByModule).map(([module, actions]) => {
        const agent = this.client.createAgent({
          eventBus: this.eventBus.span(`${module}Agent`),
          instructions: [`You are a ${module} expert that can execute tasks using the tools provided.`],
          actions
        });
        return functionTool({
          name: `execute${module}Task`,
          description: `Execute task related to ${module.toLowerCase()} - ${actions.map((a) => a.description?.toLowerCase()).join(',')} (and nothing else)`,
          parameters: { instructions: string({ description: 'Task instructions that agent should execute' }) },
          execute: ({ instructions }) => agent.execute(instructions)
        });
      })
    });
  }
}

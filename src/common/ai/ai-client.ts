import { type ChatModel } from 'openai/resources';
import { type ResponseInputItem, type Tool } from 'openai/resources/responses/responses';
import { Action, ActionBus, ActionHandler } from '~/common/actions';
import { AzureOpenAIManager, AzureOpenAIModule } from '~/common/azure/openai';
import { InvalidStateError } from '~/common/errors';
import { EventBus } from '~/common/events';
import { Provider } from '~/common/providers';
import { object, Static, TSchema, TString } from '~/common/schema';
import { castArray } from '~/common/utils/array';
import { GetResponse } from './ai-actions';
import { type AIAgent } from './ai-agent';
import { createInstructions, Instructions } from './ai-instructions';
import { toolRules, type FunctionTool } from './ai-tools';

interface ResponseOptions<T extends TSchema = TString> {
  model?: ChatModel;
  instructions?: string | Instructions[];
  functions?: FunctionTool<any>[];
  actions?: Action[];
  output?: T;
  eventBus?: EventBus;
}

const generalRules: Instructions = {
  title: 'General rules',
  items: [
    `If you can't solve the task using your knowledge or provided tools, reject the task.`,
    'Do not validate the result, just execute the task.'
  ]
};

const jsonInstructions: Instructions = {
  title: 'JSON output',
  items: ['Read the JSON schema below the prompt.', 'Output JSON document only that EXACTLY matches the schema.']
};

@Provider({
  imports: [AzureOpenAIModule]
})
export class AIClient {
  constructor(
    private openai: AzureOpenAIManager,
    private actionBus: ActionBus,
    private eventBus: EventBus
  ) {}

  @ActionHandler(GetResponse)
  async getResponse<T extends TSchema = TString>(
    prompt: Instructions | Instructions[],
    options: ResponseOptions<T> = {}
  ) {
    const agent = this.createAgent(options);
    return agent.execute(prompt);
  }

  /**
   * Create agent that can respond to a given prompt with provided tools
   */
  createAgent<T extends TSchema = TString>(options: ResponseOptions<T> = {}): AIAgent<T> {
    const tools: Tool[] = [];
    const handlers = new Map<string, (args: any) => Promise<any>>();
    for (const { name, description, parameters, execute } of options.functions ?? []) {
      tools.push({ type: 'function', name, description, parameters: object(parameters ?? {}), strict: false });
      handlers.set(name, execute);
    }
    for (const action of options.actions ?? []) {
      const name = action.name.replace(/[\.:]/g, '_');
      const { description, properties: parameters } = action;
      tools.push({ type: 'function', name, description, parameters, strict: false });
      handlers.set(name, (args) => this.actionBus.execute(action.name, args));
    }

    const eventBus = options.eventBus ?? this.eventBus;
    const instructions = createInstructions([
      ...castArray(options.instructions ?? 'You are a helpful assistant.'),
      generalRules,
      ...(tools.length ? [toolRules] : []),
      ...(options.output ? [jsonInstructions] : [])
    ]);

    const input: ResponseInputItem[] = [{ role: 'system', content: instructions }];

    const execute = async (prompt: Instructions | Instructions[]): Promise<Static<T>> => {
      // add user input to messages
      if (prompt) {
        const content = createInstructions([
          ...castArray(prompt),
          ...(options.output ? [{ title: 'JSON schema', data: options.output }] : [])
        ]);
        eventBus.verbose('request', 'Sending request', { prompt: content, instructions });
        input.push({ role: 'user', content });
      }

      const sdk = this.openai.getClient(options.model ?? 'gpt-4o-mini');
      const { output, output_text } = await sdk.responses.create({
        model: options.model ?? 'gpt-4o-mini',
        input,
        tools,
        // @todo use structured outputs when supported by responses API in Azure
        // https://learn.microsoft.com/en-us/azure/ai-services/openai/how-to/responses?tabs=python-secure#model-support
        text: options.output ? { format: { type: 'json_object' } } : undefined,
        // temporary workaround for rate_limit_error
        // https://learn.microsoft.com/en-us/answers/questions/2236830/getting-rate-limit-error-when-accessing-gpt-4o-min
        max_output_tokens: 2048,
        store: false
      });

      const functionCalls = output.filter((item) => item.type === 'function_call');
      if (functionCalls.length) {
        for (const fn of functionCalls) {
          // parse and execute the tool
          const name = fn.name;
          const handler = handlers.get(name);
          if (!handler) {
            throw new Error(`Handler for function "${name}" not defined`);
          }

          const args = JSON.parse(fn.arguments);
          eventBus.verbose('function', 'Calling function', { name, args });
          const result = await handler(args);

          // add tool result to messages
          input.push(fn);
          input.push({
            type: 'function_call_output',
            call_id: fn.call_id,
            output: result ? JSON.stringify(result) : 'Executed successfully'
          });
        }

        return execute('');
      } else {
        const text = output_text;
        eventBus.verbose('result', 'Response received', { text });
        if (options.output) {
          try {
            const match = text.match(/```json([^`]+)```/);
            return JSON.parse(match ? match[1].trim() : text.trim());
          } catch {
            throw new InvalidStateError('Invalid JSON output in response');
          }
        } else {
          return text;
        }
      }
    };

    return { execute };
  }
}

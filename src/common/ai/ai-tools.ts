import { type Static, type TObject, type TProperties } from '~/common/schema';
import { Instructions } from './ai-instructions';

export interface FunctionTool<T extends TProperties> {
  name: string;
  description: string;
  parameters?: T;
  execute: (args: Static<TObject<T>>) => any;
}

export function functionTool<T extends TProperties>(tool: FunctionTool<T>) {
  return tool as FunctionTool<any>;
}

export const toolRules: Instructions = {
  title: 'Tool rules',
  items: [
    'Use the available tools to retrieve information relevant to the task.',
    'Do not modify the tool results (e.g prefix file paths with "sandbox:" etc.)'
  ]
};

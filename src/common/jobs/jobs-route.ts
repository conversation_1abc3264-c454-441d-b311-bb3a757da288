import { authMatch } from '~/common/ui/identity';
import { Route } from '~/common/ui/routing';
import { JobRead } from './job-permissions';

export const jobsRoute: Route = {
  path: 'jobs',
  canMatch: [authMatch],
  data: {
    title: 'Jobs',
    authorize: JobRead,
    menu: {
      icon: 'cluster'
    }
  },
  children: [
    {
      path: '',
      pathMatch: 'full',
      loadComponent: () => import('./queues-component').then((c) => c.QueuesComponent)
    }
  ]
};

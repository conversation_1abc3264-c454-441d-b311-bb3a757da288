import { readFile, unlink, writeFile } from 'fs-extra';
import { tmpdir } from 'os';
import { resolve } from 'path';
import { InvalidArgumentError } from '~/common/errors';
import { exec } from '~/common/process';
import { uniqBy } from '~/common/utils/array';
import { uniqueId } from '~/common/utils/string';

async function withTempFile<T>(fn: (file: string) => Promise<T>) {
  const file = resolve(tmpdir(), uniqueId());
  const result = await fn(file);
  await unlink(file);
  return result;
}

/**
 * Extract page(s) from PDF buffer
 */
export async function separatePdf(pdf: Buffer, pages: string | number) {
  return withTempFile(async (tmpFile) => {
    await writeFile(tmpFile, pdf);
    return separatePdfFile(tmpFile, pages);
  });
}

/**
 * Extract page(s) from PDF file (using QPDF)
 * Pages can be specified as:
 * - single page: 1
 * - multiple pages: 1,3,5
 * - range: 1-5
 * - first/last keywords: first => 1, first-3 => 1-3, last-2 => last two pages
 */
export async function separatePdfFile(file: string, pages: string | number) {
  const metadata = await getPdfFileMetadata(file);
  const ranges = createPageRanges(pages, metadata.pages);
  if (ranges) {
    return withTempFile(async (tmpFile) => {
      await exec(`qpdf --warning-exit-0 --empty --pages "${file}" ${ranges} -- "${tmpFile}" 2>/dev/null`);
      return readFile(tmpFile);
    });
  } else {
    return readFile(file);
  }
}

/**
 * Create and optimize page ranges for extraction
 * @example createPages('1-2,2-2,6-8,last', 8) => "1-2,6-8"
 * @example createPages('first-last', 5) => undefined (whole range)
 * @example createPages('last-2', 5) => "3-5"
 * @example createPages('2-1', 5) => "2"
 */
function createPageRanges(pages: string | number, total: number) {
  type Range = [number, number];
  const trim = (page: number) => (page <= 0 ? 1 : page > total ? total : page);
  const ranges: Range[] = uniqBy(
    String(pages)
      .replace(/\s/g, '')
      .split(',')
      .map((item) => {
        const [from, to] = item.split('-');
        if (from === 'first') {
          return [1, to === 'last' ? total : Number(to)];
        } else if (from === 'last') {
          return [to ? total - Number(to) : total, total];
        } else {
          return [Number(from), Number(to ?? from)];
        }
      })
      .map(([from, to]) => [trim(from), trim(from > to ? from : to)]),
    ([from, to]) => `${from}-${to}`
  );

  const result: Range[] = [];
  for (const [index, range] of ranges.entries()) {
    const exists = ranges.some(([from, to], key) => range[0] >= from && range[1] <= to && key !== index);
    if (!exists) {
      result.push(range);
    }
  }

  if (result.length === 1 && result[0][0] === 1 && result[0][1] === total) {
    // skip entire range
    return undefined;
  } else {
    // format final ranges
    return result.map(([from, to]) => (from === to ? from : `${from}-${to}`)).join(',');
  }
}

/**
 * Return metadata from PDF file
 */
export async function getPdfFileMetadata(file: string): Promise<{ pages: number }> {
  try {
    const result = await exec(`pdfinfo "${file}"`);
    const match = result.stdout.match(/Pages:\s+([0-9]+)/);
    if (match) {
      return { pages: parseInt(match[1]) };
    } else {
      throw new InvalidArgumentError('Failed to analyze PDF file', { file });
    }
  } catch (e: any) {
    throw e;
  }
}

/**
 * Convert PDF to text
 */
export async function pdfToText(file: string) {
  const result = await exec(`pdftotext "${file}" -`);
  return result.stdout.trim();
}

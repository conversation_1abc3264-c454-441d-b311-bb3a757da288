<nz-table
  #table
  [nzData]="data"
  [nzCustomColumn]="nzColumns"
  [nzShowPagination]="false"
  [nzFrontPagination]="false"
  (nzQueryParams)="this.params$.next($event)"
  [nzScroll]="(nzScroll$ | async) ?? { x: 'auto' }"
>
  <thead>
    <tr>
      @for (column of columns; track column.name) {
        <th
          [nzCellControl]="column.name"
          [nzColumnKey]="column.name"
          [nzShowSort]="!!column.sort"
          [nzSortFn]="column.sort ? true : null"
          [nzSortOrder]="column.sort ? (sort | nzSortOrder: column.name) : null"
          [nzSortDirections]="column.sort === 'desc' ? ['descend', 'ascend', null] : ['ascend', 'descend', null]"
          [class.text-center]="column.align === 'center'"
          [class.text-right]="column.align === 'right'"
        >
          {{ column.title }}
        </th>
      }
    </tr>
  </thead>
  <tbody>
    @for (item of table.data; track item) {
      <tr (click)="rowClick$.emit(item)" [class.cursor-pointer]="!!rowClick$.observers.length">
        @for (column of columns; track column.name) {
          <td
            [nzCellControl]="column.name"
            [ngClass]="column.classes"
            [ngStyle]="column.style"
            [class.text-center]="column.align === 'center'"
            [class.text-right]="column.align === 'right'"
            [nzEllipsis]="column.truncate"
          >
            <ng-container *ngTemplateOutlet="cell; context: { column, item }" />
          </td>
        }
      </tr>
    }
  </tbody>
  @if (sum && sum.length <= 2) {
    <tfoot nzSummary nzFixed>
      <tr>
        @for (column of columns; track column.name; let firstColumn = $first) {
          <td
            [nzCellControl]="column.name"
            [class.text-center]="column.align === 'center'"
            [class.text-right]="column.align === 'right'"
          >
            @for (row of sum; track row; let firstRow = $first) {
              <div>
                @if (row[column.name] !== undefined) {
                  <ng-container *ngTemplateOutlet="cell; context: { column, item: row }" />
                } @else if (firstColumn && firstRow) {
                  Total
                }
              </div>
            }
          </td>
        }
      </tr>
    </tfoot>
  }
</nz-table>

<ng-template #cell let-column="column" let-item="item">
  @if (templates?.[column.name]; as template) {
    <ng-container *ngTemplateOutlet="template; context: { $implicit: item, column }" />
  } @else if (column.actions) {
    @if (column.actions | async; as actions) {
      @if ($any(actions).length) {
        <action-buttons [actions]="$any(actions)" [context]="item" size="small" />
      }
    }
  } @else {
    <property-value [property]="column" [data]="item" />
  }
</ng-template>

import { authMatch } from '~/common/ui/identity';
import { Route } from '~/common/ui/routing';
import { SystemRead } from '~/services/system/system-permissions';

export const eventsRoute: Route = {
  path: 'events',
  canMatch: [authMatch],
  data: {
    title: 'Events',
    authorize: SystemRead,
    menu: {
      icon: 'container'
    }
  },
  children: [
    {
      path: '',
      pathMatch: 'full',
      loadComponent: () => import('./events-page-component').then((c) => c.EventsPageComponent)
    }
  ]
};

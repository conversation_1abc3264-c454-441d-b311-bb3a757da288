import { castArray } from '~/common/utils/array';
import { addMonths, formatDate, subMonths, toDate } from '~/common/utils/date';
import { padStart } from '~/common/utils/string';
import { isDefined } from '~/common/utils/value';
import { Cycle } from './cycle';
import { CycleConfig } from './cycle-config';
import { ValidInPeriod } from './valid-in-period';

/**
 * Check period validity
 * @param period
 */
export function isValidPeriod(period: string): boolean {
  const match = /^([0-9]{4})-([0-9]{2})$/.exec(String(period));
  if (match) {
    const year = parseInt(match[1], 10);
    const month = parseInt(match[2], 10);
    return year > 0 && month >= 1 && month <= 12;
  } else {
    return false;
  }
}

/**
 * Return current period
 */
export function getCurrentPeriod(): string {
  return dateToPeriod(new Date());
}

/**
 * Return previous period of provided period
 */
export function getPreviousPeriod(period = getCurrentPeriod(), offset: number = 1): string {
  return dateToPeriod(subMonths(periodToDate(period), offset));
}

/**
 * Return previous periods up to specified length as array
 * @param period
 * @param length
 */
export function getPreviousPeriods(period = getCurrentPeriod(), length: number): string[] {
  return Array.from(Array(length).keys())
    .map((offset) => getPreviousPeriod(period, offset + 1))
    .reverse();
}

/**
 * Return next period of provided period
 */
export function getNextPeriod(period = getCurrentPeriod(), offset: number = 1): string {
  return dateToPeriod(addMonths(periodToDate(period), offset));
}

/**
 * Convert value to period
 */
export function toPeriod(value: string, format: string): string {
  return dateToPeriod(toDate(value, format));
}

/**
 * Format period, "Month YYYY" by default (eg. "September 2020")
 */
export function formatPeriod(
  period: string,
  format: string = 'MMMM YYYY',
  options: { locale?: string; offset?: number } = {}
): string {
  const date = addMonths(periodToDate(period), options.offset ?? 0);
  return formatDate(date, format, { locale: options.locale });
}

/**
 * Convert period to date
 */
export function periodToDate(period: string): Date {
  return toDate(period, 'YYYY-MM');
}

/**
 * Convert date to period
 */
export function dateToPeriod(date: Date) {
  return `${date.getFullYear()}-${padStart(String(date.getMonth() + 1), 2, '0')}`;
}

/**
 * Generate periods
 */
export function generatePeriods(from: string, to: string = getCurrentPeriod()) {
  const periods: string[] = [];
  let current = from;
  while (current <= to) {
    periods.push(current);
    current = getNextPeriod(current);
  }
  return periods;
}

/**
 * Is period in allowed range?
 */
export function inPeriodRange(period: string, from: string, to: string) {
  return period >= from && period <= to;
}

/**
 * Is entity valid in a given period?
 */
export function isEntityValidInPeriod<T extends ValidInPeriod>(entity: T, period = getCurrentPeriod()) {
  if (entity.validFromPeriod && entity.validFromPeriod > period) {
    return false;
  } else if (entity.validToPeriod && entity.validToPeriod < period) {
    return false;
  } else {
    return true;
  }
}

/**
 * Return period by cycle start date
 */
export function getPeriodByCycleStart(cycleStart: Date, threshold?: number) {
  const period = dateToPeriod(cycleStart);
  return threshold && cycleStart.getUTCDate() >= threshold ? getNextPeriod(period) : period;
}

/**
 * Return cycle for a given period and entity
 */
export function getEntityPeriodCycle<T extends CycleConfig>(
  entity: T | T[],
  period: string = getCurrentPeriod()
): Cycle {
  const entities = castArray(entity);
  const startDay = entities.map((e) => e.cycleStartDay).find(isDefined) ?? 1;
  const duration = entities.map((e) => e.cycleDuration).find(isDefined) ?? 1;
  const threshold = entities.map((e) => e.periodThreshold).find(isDefined);

  const startDate = new Date(periodToDate(period ?? getCurrentPeriod()));
  startDate.setDate(startDay);

  // previous month period when start day above specified threshold
  if (threshold && startDay >= threshold) {
    startDate.setMonth(startDate.getMonth() - 1);
  }

  const endDate = new Date(startDate);
  endDate.setMonth(endDate.getMonth() + duration); // plus duration (X months)
  endDate.setDate(endDate.getDate() - 1); // minus one day

  return { startDate, endDate };
}

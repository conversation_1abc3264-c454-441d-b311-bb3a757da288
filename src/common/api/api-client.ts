import { HttpClient, HttpErrorResponse, HttpParams } from '@angular/common/http';
import { Injectable } from '@angular/core';
import {
  auditTime,
  catchError,
  defer,
  filter,
  map,
  merge,
  mergeMap,
  Observable,
  of,
  retryWhen,
  share,
  switchMap,
  tap,
  throwError,
  timer
} from 'rxjs';
import { EntityEventData, toEntityEventFilter } from '~/common/database/entity-event';
import { EntityOperation } from '~/common/database/entity-operation';
import { QueryConfigBuilder } from '~/common/database/query-config';
import { fromErrorPayload, isErrorPayload } from '~/common/errors';
import { createEventFilter } from '~/common/events/event-filter';
import { type RequestMethod } from '~/common/http/request-method';
import { isArray } from '~/common/utils/array';
import { plainToClass, type Class } from '~/common/utils/class';
import { getCollectionMetadata, withCollectionMetadata, type CollectionMetadata } from '~/common/utils/collection';
import { compactObject, isObject, omit, pickBy, sortKeys, withObjectMetadata } from '~/common/utils/object';
import { isDefined, isNil } from '~/common/utils/value';
import { EventsWsClient } from '~/common/ws/events/events-ws-client';
import { ApiClientCache } from './api-client-cache';
import { apiRoutePrefix } from './api-route-prefix';

function toParams(obj: any) {
  return Object.entries(compactObject(obj)).reduce((params, [key, value]) => {
    // arrays as arr[]=1&arr[]=2&arr[]=3
    if (isArray(value)) {
      value.forEach((val) => {
        params = params.append(`${key}[]`, val);
      });
      return params;
    } else {
      return isNil(value) ? params : params.set(key, value);
    }
  }, new HttpParams());
}

type RequestBody = Record<string, any> | FormData;
type RequestParams = Record<string, any>;

export interface ApiRequestPayload {
  body?: RequestBody;
  params?: RequestParams;
}

export interface ApiRequestOptions {
  // Body plain type to transform into class type
  bodyType?: Class<any>;

  // Response class type to transform plain data into
  responseType?: Class<any>;

  // Reload on successful action
  onAction?: boolean;

  // Reload on entity events
  onEntityEvents?: Class<any>[];
  onEntityEventsDebounceTime?: number;

  // Custom error message to set on 500, otherwise response error message or "API request failed"
  errorMessage?: string;

  // Cache config
  cache?: true | { key: string | object; ttl?: number };

  // Context message for the request (e.g. justification for a change via POST/PUT requests)
  contextMessage?: string;

  // Custom base URL
  baseUrl?: string;

  // Custom token
  token?: string;
}

const defaultErrorMessage = 'API request failed';

@Injectable({ providedIn: 'root' })
export class ApiClient {
  /**
   * Data cache
   */
  private readonly cache = new ApiClientCache();

  /**
   * Action events stream
   */
  private actionEvents$ = this.eventStream.select({ source: 'Insights:Actions', name: 'success' }).pipe(share());

  /**
   * Entity events stream
   */
  private entityEvents$ = this.eventStream.select<EntityEventData>(toEntityEventFilter(null)).pipe(share());

  constructor(
    private httpClient: HttpClient,
    private eventStream: EventsWsClient
  ) {}

  /**
   * Perform GET request
   */
  get<T, R extends RequestParams | undefined = object>(
    path: string,
    params?: R,
    options?: ApiRequestOptions
  ): Observable<T> {
    return this.request<T>('GET', path, { params }, options);
  }

  /**
   * Perform POST request
   */
  post<T, R extends RequestBody | undefined = object>(
    path: string,
    body?: R,
    options?: ApiRequestOptions
  ): Observable<T> {
    return this.request<T>('POST', path, { body }, options);
  }

  /**
   * Perform PATCH request
   */
  patch<T, R extends RequestBody | undefined = object>(
    path: string,
    body?: R,
    options?: ApiRequestOptions
  ): Observable<T> {
    return this.request<T>('PATCH', path, { body }, options);
  }

  /**
   * Perform DELETE request
   */
  delete<T = void, R extends RequestParams | undefined = object>(
    path: string,
    params?: R,
    options?: ApiRequestOptions
  ): Observable<T> {
    return this.request<T>('DELETE', path, { params }, options);
  }

  /**
   * Return values of requested property on a collection
   */
  values<T, R extends RequestParams | undefined = object>(
    path: string,
    propertyId: keyof T,
    params?: R,
    options?: ApiRequestOptions
  ): Observable<string[]> {
    return this.get<any[]>(path, { ...omit(params, [propertyId]), values: [propertyId], limit: 0 }, options).pipe(
      map((data) => getCollectionMetadata(data).values?.[propertyId] ?? [])
    );
  }

  /**
   * Perform entities query
   */
  query<T>(target: Class<T>) {
    const post = this.post.bind(this);
    class Query extends QueryConfigBuilder<T> {
      execute() {
        return post<T[]>('entities/query', this.build());
      }
    }
    return new Query(target);
  }

  /**
   * Return full API url
   */
  url(path: string): string {
    return `${apiRoutePrefix}/` + path.replace(new RegExp('^/'), '');
  }

  /**
   * Clear API cache
   */
  clearCache(pattern?: RegExp) {
    this.cache.clear(pattern);
  }

  /**
   * Perform a request
   */
  protected request<T>(
    method: RequestMethod,
    path: string,
    payload: ApiRequestPayload,
    options: ApiRequestOptions = {}
  ): Observable<T> {
    const {
      baseUrl,
      bodyType,
      cache,
      contextMessage,
      onAction,
      onEntityEvents,
      onEntityEventsDebounceTime,
      responseType,
      token
    } = options;
    const url = (baseUrl ?? '') + this.url(path);
    const cacheOptions = cache === true ? { key: payload } : cache;
    const cacheKey = cacheOptions
      ? `${url}.${typeof cacheOptions.key === 'object' ? JSON.stringify(sortKeys(cacheOptions.key)) : cacheOptions.key}`
      : null;

    // retrieve from cache for GET
    if (method === 'GET' && cacheKey && this.cache.has(cacheKey)) {
      return this.cache.get(cacheKey);
    }

    // perform a request
    const request$ = this.httpClient
      .request<{ data: T } & CollectionMetadata<T>>(method, url, {
        observe: 'response',
        responseType: 'json',
        params: payload.params ? toParams(payload.params) : undefined,
        body: payload.body && bodyType ? plainToClass(bodyType, payload.body) : payload.body,
        headers: pickBy(
          {
            Authorization: token ? `Bearer ${token}` : undefined,
            'X-Message': contextMessage
          },
          isDefined
        )
      })
      .pipe(
        map(({ body }) => {
          if (body === null) {
            return null;
          }

          let { data } = body;

          // transform to required class type
          if (responseType) {
            if (Array.isArray(data)) {
              data = data.map((item) => plainToClass(responseType, item)) as T;
            } else {
              data = plainToClass(responseType, data);
            }
          }

          if (Array.isArray(data)) {
            const metadata = omit(body, 'data') as Partial<CollectionMetadata<T>>;
            return withCollectionMetadata(data, { count: data.length, ...metadata });
          } else if (isObject(data) && Object.keys(body).length > 1) {
            const metadata = omit(body, 'data');
            return withObjectMetadata(data, metadata);
          } else {
            return data;
          }
        }),
        tap((data) => {
          if (cacheKey) {
            switch (method) {
              case 'DELETE':
                // remove from cache on DELETE
                this.cache.remove(cacheKey);
                break;

              default:
                // save to cache on GET/POST/PATCH
                this.cache.set(cacheKey, of(data), { ttl: cacheOptions?.ttl });
                break;
            }
          }
        }),

        // transform error response to application error
        catchError((response: HttpErrorResponse) => {
          const isPayload = isErrorPayload(response.error);
          const message = (() => {
            if (response.status === 500 && options.errorMessage) {
              return options.errorMessage;
            } else if (isPayload) {
              return response.error.message;
            } else {
              return response.message ?? defaultErrorMessage;
            }
          })();

          return throwError(() => (isPayload ? fromErrorPayload(response.error) : new Error(message)));
        }),

        // retry request on occasional proxy/network errors
        // perform two attempts - after 500ms and 1000ms
        retryWhen((errors) =>
          errors.pipe(
            mergeMap((error: Error, index) => {
              const attempt = index + 1;
              if (error.message === defaultErrorMessage && attempt <= 2) {
                return timer(500 * attempt);
              } else {
                return throwError(() => error);
              }
            })
          )
        )
      );

    const src$ = defer(() => {
      if (method === 'GET') {
        const triggers: Observable<any>[] = [];

        // reload on entity events
        if (onEntityEvents) {
          const events$ = this.getEntityStream(onEntityEvents).pipe(auditTime(onEntityEventsDebounceTime ?? 500));
          triggers.push(events$);
        }

        // reload on successful action
        if (onAction) {
          triggers.push(this.actionEvents$);
        }

        if (triggers.length) {
          return merge(
            // initial request trigger
            of(null),
            // additional change triggers (debounced)
            merge(...triggers).pipe(
              auditTime(200),
              tap(() => {
                // reset cache on change
                if (cacheKey) {
                  this.cache.remove(cacheKey);
                }
              })
            )
          ).pipe(switchMap(() => request$));
        }
      }

      return request$;
    });

    return cacheKey ? this.cache.set(cacheKey, src$) : src$;
  }

  /**
   * Create entity stream for given entity/entities and operations
   */
  private getEntityStream<T = any>(type: Class<T> | Class<any>[], options: { operations?: EntityOperation[] } = {}) {
    const types = isArray(type) ? type : [type];
    return merge(
      ...types.map((type) =>
        this.entityEvents$.pipe(
          filter(
            createEventFilter<EntityEventData<T>>({
              ...toEntityEventFilter(type, { operations: options.operations }),
              // override data filter to pair entity using hash code, because class names are mangled in production
              // @see src/common/database/entity-event-emitter.ts
              // @see src/modules/insights/shim/typeorm.ts
              data: (data) => data.code === (<any>type).code
            })
          ),
          map((event) => {
            event.data.entity = plainToClass(type, event.data.entity);
            if (event.data.previousEntity) {
              event.data.previousEntity = plainToClass(type, event.data.previousEntity);
            }
            return event;
          })
        )
      )
    );
  }
}

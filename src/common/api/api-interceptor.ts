import { <PERSON><PERSON><PERSON><PERSON>, ExecutionContext, Injectable, NestInterceptor } from '@nestjs/common';
import { map } from 'rxjs';
import { getCollectionMetadata, hasCollectionMetadata } from '~/common/utils/collection';
import { getObjectMetadata, hasObjectMetadata, isObject } from '~/common/utils/object';

@Injectable()
export class ApiInterceptor implements NestInterceptor {
  intercept(context: ExecutionContext, next: CallHandler) {
    return next.handle().pipe(
      map((data) => {
        if (Array.isArray(data)) {
          const metadata = hasCollectionMetadata(data) ? getCollectionMetadata(data) : {};
          return { count: data.length, ...metadata, data };
        } else if (isObject(data) && hasObjectMetadata(data)) {
          const metadata = hasObjectMetadata(data) ? getObjectMetadata(data) : {};
          return { ...metadata, data };
        } else {
          return { data };
        }
      })
    );
  }
}

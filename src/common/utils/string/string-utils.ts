import { kebabCase } from 'lodash';
import { customAlphabet } from 'nanoid';

export { default as levenshtein } from 'js-levenshtein-esm';
export { default as camelCase } from 'lodash/camelCase';
export { default as isString } from 'lodash/isString';
export { default as kebabCase } from 'lodash/kebabCase';
export { default as lowerFirst } from 'lodash/lowerFirst';
export { default as padStart } from 'lodash/padStart';
export { default as snakeCase } from 'lodash/snakeCase';
export { default as startCase } from 'lodash/startCase';
export { default as toLowerCase } from 'lodash/toLower';
export { default as toString } from 'lodash/toString';
export { default as toUpperCase } from 'lodash/toUpper';
export { default as trim } from 'lodash/trim';
export { default as upperFirst } from 'lodash/upperFirst';

/**
 * Slugify/webalize string
 * @example "TEM Insights" => "tem-insights"
 * @param value
 */
export function slugify(value: string) {
  return kebabCase(value);
}

/**
 * Replace CRLF with LF
 * @param value
 */
export function crlfToLf(value: string) {
  return value.replace(/\r\n/g, '\n');
}

/**
 * Generate unique ID
 * @param length
 * @param chars
 */
export function uniqueId(length: number = 8, chars: string = '1234567890abcdef') {
  return customAlphabet(chars, length)();
}

/**
 * Generates hash code for a string
 * @param value
 * @returns
 * @link https://stackoverflow.com/questions/7616461/generate-a-hash-from-string-in-javascript
 */
export function hashCode(value: string) {
  return value.split('').reduce((a, b) => {
    a = (a << 5) - a + b.charCodeAt(0);
    return a & a;
  }, 0);
}

/**
 * Convert stringified value to real-type value
 * @example "true" => true
 * @example "1.5" => 1.5
 */
export function stringToValue(str: string): any {
  try {
    return JSON.parse(str);
  } catch {
    return str;
  }
}

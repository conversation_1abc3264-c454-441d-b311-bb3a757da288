import mapKeys from 'lodash/mapKeys';
import omitBy from 'lodash/omitBy';
import pickBy from 'lodash/pickBy';
import { camelCase } from '~/common/utils/string';
import { isEqual, isNil } from '~/common/utils/value';
import { type KeyValue } from './object-types';

export { default as get } from 'lodash/get';
export { default as invert } from 'lodash/invert';
export { default as isPlainObject } from 'lodash/isPlainObject';
export { default as mapValues } from 'lodash/mapValues';
export { default as merge } from 'lodash/merge';
export { default as omit } from 'lodash/omit';
export { default as pick } from 'lodash/pick';
export { default as set } from 'lodash/set';
export { default as zipObject } from 'lodash/zipObject';
export { mapKeys, omitBy, pickBy };

/**
 * Is value an object? (either plain object or class instance)
 */
export function isObject<T extends object = KeyValue>(value: unknown, properties?: string[]): value is T {
  if (typeof value === 'object' && value !== null && !Array.isArray(value)) {
    return properties ? properties.every((prop) => prop in value) : true;
  } else {
    return false;
  }
}

/**
 * Create object checker with given properties
 */
export function objectChecker<T extends object>(properties?: Array<keyof T>) {
  return (value: unknown) => isObject<T>(value, properties as string[]);
}

/**
 * Compact object - remove all undefined/null properties
 * @param object
 */
export function compactObject<T extends Record<string, any>>(object: T): Partial<T> {
  return omitBy<T>(object, isNil);
}

/**
 * Compare two objects by properties and return difference
 * @param obj1
 * @param obj2
 * @param properties
 */
export function diffObjects(obj1: any, obj2: any, properties?: string[]) {
  return pickBy(obj1, (value, key) => {
    if (!isEqual(value, obj2[key])) {
      return properties ? properties.includes(key) : true;
    } else {
      return false;
    }
  });
}

/**
 * Transform object keys to camelcase
 */
export function camelCaseKeys(obj: any): any {
  return mapKeys(obj, (value, key) => camelCase(key));
}

/**
 * Transform object keys to lowercase
 */
export function lowerCaseKeys(obj: any): any {
  return mapKeys(obj, (value, key) => key.toLowerCase());
}

/**
 * Sort object keys
 */
export function sortKeys<T extends object, K extends keyof T>(obj: T): T {
  const result: any = {};
  const keys = Object.keys(obj).sort() as K[];
  for (const key of keys) {
    result[key] = obj[key];
  }
  return result;
}

/**
 * Flatten nested object into a one-level keys using dot notation
 * @example { "x": { "y": 1 } } => { "x.y": 1 }
 */
export function flattenObject(obj: object): Record<string, any> {
  function flatten(obj: object, prefix: string = '', res: Record<string, any> = {}) {
    for (const [key, value] of Object.entries(obj)) {
      const fullKey = prefix ? `${prefix}.${key}` : key;
      if (isObject(value)) {
        flatten(value, fullKey, res);
      } else {
        res[fullKey] = value;
      }
    }
    return res;
  }
  return flatten(obj);
}

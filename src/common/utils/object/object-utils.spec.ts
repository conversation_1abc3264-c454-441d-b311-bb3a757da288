import { expect, suite, test } from '~/common/testing';
import {
  camelCaseKeys,
  compactObject,
  diffObjects,
  flattenObject,
  isPlainObject,
  lowerCaseKeys,
  sortKeys
} from './object-utils';

suite('Object utils', () => {
  test('isObject', () => {
    const tests = [
      [{}, true],
      [{ key: 'value' }, true],
      [[], false],
      [null, false],
      [undefined, false],
      ['string', false],
      [123, false],
      [new Date(), false]
    ];
    for (const [value, expected] of tests) {
      expect(isPlainObject(value)).toBe(expected);
    }
  });

  test('compactObject', () => {
    const input = { a: 1, b: null, c: undefined, d: 'value' };
    const expected = { a: 1, d: 'value' };
    expect(compactObject(input)).toEqual(expected);
  });

  test('diffObjects', () => {
    const obj1 = { a: 1, b: 2, c: 3 };
    const obj2 = { a: 1, b: 3, d: 4 };
    const expected = { b: 2, c: 3 };
    expect(diffObjects(obj1, obj2)).toEqual(expected);

    const expectedWithProperties = { b: 2 };
    expect(diffObjects(obj1, obj2, ['b'])).toEqual(expectedWithProperties);
  });

  test('camelCaseKeys', () => {
    const input = { first_name: 'John', last_name: 'Doe' };
    const expected = { firstName: 'John', lastName: 'Doe' };
    expect(camelCaseKeys(input)).toEqual(expected);
  });

  test('lowerCaseKeys', () => {
    const input = { FirstName: 'John', LastName: 'Doe' };
    const expected = { firstname: 'John', lastname: 'Doe' };
    expect(lowerCaseKeys(input)).toEqual(expected);
  });

  test('sortKeys', () => {
    const input = { c: 3, a: 1, b: 2 };
    const expected = { a: 1, b: 2, c: 3 };
    expect(sortKeys(input)).toEqual(expected);
  });

  test('flattenObject', () => {
    const input = {
      a: 1,
      b: { c: 2, d: { e: 3 } },
      f: 4
    };
    const expected = {
      a: 1,
      'b.c': 2,
      'b.d.e': 3,
      f: 4
    };
    expect(flattenObject(input)).toEqual(expected);
  });
});

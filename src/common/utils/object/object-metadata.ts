export type ObjectMetadata = Omit<Record<string, any>, 'data'>;

const metadataKey = Symbol('Object metadata');

export function withObjectMetadata<T extends object>(obj: T, metadata: ObjectMetadata) {
  return new Proxy(obj, {
    get: (target, property) => (property === metadataKey ? metadata : Reflect.get(target, property)),
    has: (target, property) => (property === metadataKey ? true : Reflect.has(target, property))
  });
}

export function hasObjectMetadata<T extends object>(obj: T) {
  return metadataKey in obj;
}

export function getObjectMetadata<T extends object>(obj: T) {
  if (hasObjectMetadata(obj)) {
    return obj[metadataKey] as ObjectMetadata;
  } else {
    throw new Error('Object metadata not defined');
  }
}

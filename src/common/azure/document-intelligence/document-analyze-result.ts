import { DocumentFieldOutput } from '@azure-rest/ai-document-intelligence';
import { castArray } from '~/common/utils/array';
import { isValidDate, toDate } from '~/common/utils/date';
import { toNumber } from '~/common/utils/number';
import { mapValues, omit } from '~/common/utils/object';
import { isDefined } from '~/common/utils/value';

export type DocumentAnalyzeResult = Record<string, DocumentFieldOutput>;
type Aliases = Record<string, string>;

export interface DocumentAnalyzeOptions {
  aliases?: Aliases;
  transform?: {
    number?: (value: string) => string;
    date?: (value: string) => string;
  };
}

type Fields = string | string[];
type FieldOptions = { confidence?: number };

/**
 * Wrap low-level result from DI service into a handy getter
 */
export function createResultGetter(result: DocumentAnalyzeResult, options: DocumentAnalyzeOptions = {}) {
  const { aliases, transform } = options;

  function getOutput(fields: Fields, options: FieldOptions = {}): DocumentFieldOutput | undefined {
    return castArray(fields)
      .map((field) => {
        if (field.includes('.')) {
          // collection item, e.g. "Items.Amount"
          const [parent, name] = field.split('.');
          const collection = result[parent]?.valueArray;
          if (collection) {
            const object = collection.map((item) => item.valueObject).find(isDefined);
            return object?.[name];
          }
        } else {
          return result[aliases?.[field] ?? field];
        }
      })
      .find((item) => {
        if (!item) {
          return false;
        } else if (item.valueArray) {
          return true;
        } else {
          // at least some confidence and any "valueXXX" output
          return (
            (item?.confidence ?? 0) > (options.confidence ?? 0) &&
            Object.keys(item).some((key) => key.startsWith('value'))
          );
        }
      });
  }

  return {
    // raw fields output
    result: filterResult(result),

    // string getter
    string: (fields: Fields, options?: FieldOptions) => getOutput(fields, options)?.valueString,

    // number/amount getter
    number: (fields: Fields, options?: FieldOptions) => {
      const output = getOutput(fields, options);

      const amount = output?.valueCurrency?.amount;
      if (amount !== undefined) {
        return amount;
      }

      const number = output?.valueNumber;
      if (number !== undefined) {
        return number;
      }

      // try ty parse number from string
      let numberString = output?.valueString;
      if (numberString) {
        // custom transform
        if (transform?.number) {
          numberString = transform.number(numberString);
        }

        // convert to number
        const number = toNumber(numberString);
        if (!Number.isNaN(number)) {
          return number;
        }
      }

      return undefined;
    },

    // date getter
    date: (fields: Fields, options?: FieldOptions) => {
      const output = getOutput(fields, options);

      const date = output?.valueDate;
      if (date) {
        return new Date(date);
      }

      // string dates
      let dateString = output?.valueString;
      if (dateString) {
        // custom transform
        if (transform?.number) {
          dateString = transform.number(dateString);
        }

        const formats = {
          // 27th June 2024
          'DD MMMM YYYY': dateString.replace(/^([0-9]+)[a-z]+/i, '$1')
        };
        for (const [format, value] of Object.entries(formats)) {
          try {
            const date = toDate(value, format);
            if (isValidDate(date)) {
              return date;
            }
          } catch (e) {
            // skip invalid dates
          }
        }
      }

      return undefined;
    },

    // currency getter
    currency: (fields: Fields, options?: FieldOptions) => getOutput(fields, options)?.valueCurrency?.currencyCode,

    // collection (array) getter
    collection: (fields: Fields) =>
      (getOutput(fields)?.valueArray ?? [])
        .map((output) => output.valueObject)
        .filter(isDefined)
        .map((item) => createResultGetter(item))
  };
}

/**
 * Filter result by removing position properties (boundingRegions, spans)
 */
function filterResult(result: DocumentAnalyzeResult) {
  return mapValues(result, (item) => {
    const filter = (output: DocumentFieldOutput) => {
      if (output.valueArray) {
        output.valueArray = output.valueArray.map(filter);
      }
      if (output.valueObject) {
        output.valueObject = mapValues(output.valueObject, filter);
      }
      // filter useless information to reduce input tokens
      return omit(output, ['boundingRegions', 'spans']);
    };
    return filter(item);
  });
}

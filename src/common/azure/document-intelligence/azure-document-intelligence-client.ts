import createClient, {
  getLong<PERSON><PERSON>ning<PERSON>oller,
  isUnexpected,
  type AnalyzeDocumentRequest,
  type AnalyzeResultOperationOutput
} from '@azure-rest/ai-document-intelligence';
import { readFile } from 'fs-extra';
import { ActionHandler, type ActionOptions } from '~/common/actions';
import { AzureIdentity } from '~/common/azure/identity';
import { GeneralError } from '~/common/errors';
import { separatePdf } from '~/common/pdf';
import { Provider } from '~/common/providers';
import { uniq } from '~/common/utils/array';
import { mapValues, pickBy } from '~/common/utils/object';
import { camelCase, upperFirst } from '~/common/utils/string';
import { isDefined } from '~/common/utils/value';
import { AnalyzeDocument } from './azure-document-intelligence-actions';
import { AzureDocumentIntelligenceConfig } from './azure-document-intelligence-config';
import { createResultGetter, DocumentAnalyzeOptions } from './document-analyze-result';

// https://learn.microsoft.com/en-us/azure/ai-services/document-intelligence/concept-invoice?view=doc-intel-4.0.0#field-extraction
const defaultModel = 'invoice';

@Provider()
export class AzureDocumentIntelligenceClient {
  constructor(
    private config: AzureDocumentIntelligenceConfig,
    private identity: AzureIdentity
  ) {}

  /**
   * Analyze document using Document Intelligence
   * @see https://documentintelligence.ai.azure.com/
   */
  @ActionHandler(AnalyzeDocument)
  async analyzeDocument(
    document: string | Buffer,
    options: ActionOptions<typeof AnalyzeDocument> & DocumentAnalyzeOptions = {}
  ) {
    // convert aliases to camelCase (eg. "Daňový doklad" => "DanovyDoklad")
    const aliases = pickBy(
      mapValues(options.aliases ?? {}, (value) => (value ? upperFirst(camelCase(value)) : null)),
      isDefined
    );

    const { model, fields, locale, pages, url, key } = options;
    const client = this.createClient({ url, key });
    const response = await client.path('/documentModels/{modelId}:analyze', `prebuilt-${model ?? defaultModel}`).post({
      contentType: 'application/json',
      body: await this.getRequestBody(document, pages),
      queryParameters: {
        locale: locale ?? 'en-US',
        features: ['queryFields'],
        queryFields: uniq([...Object.values(aliases), ...(fields ?? [])].filter((value) => value.length > 1))
      }
    });

    if (isUnexpected(response)) {
      const { code, message, innererror, ...data } = response.body.error;
      throw new GeneralError(innererror?.message ?? message, data, {
        code: innererror?.code ?? code
      });
    }

    const poller = await getLongRunningPoller(client, response);
    const result = await poller.pollUntilDone();
    const body = result.body as AnalyzeResultOperationOutput;
    if (body.status === 'failed') {
      const cause = body.error;
      throw new GeneralError(cause?.message ?? 'Unexpected error', {}, { cause });
    }

    const output = body?.analyzeResult?.documents?.[0]?.fields ?? {};
    return createResultGetter(output, { aliases, transform: options.transform });
  }

  private createClient(options: { url?: string; key?: string } = {}) {
    const url = options.url ?? this.config.url;
    const key = options.key ?? this.config.key;
    return createClient(url, key ? { key } : this.identity.getCredential());
  }

  private async getRequestBody(document: string | Buffer, pages?: string): Promise<AnalyzeDocumentRequest> {
    if (Buffer.isBuffer(document)) {
      // extract requested page range from PDF to limit the size
      if (pages && document.subarray(0, 10).toString().includes('PDF')) {
        return this.getRequestBody(await separatePdf(document, pages));
      }
      return { base64Source: document.toString('base64') };
    } else if (document.startsWith('http')) {
      return { urlSource: document };
    } else {
      return this.getRequestBody(await readFile(document), pages);
    }
  }
}

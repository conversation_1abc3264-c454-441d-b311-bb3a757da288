import { AzureOpenAI } from 'openai';
import { type ChatModel } from 'openai/resources';
import { Provider } from '~/common/providers';
import { fromMap } from '~/common/utils/map';
import { AzureOpenAIConfig } from './azure-openai-config';

@Provider()
export class AzureOpenAIManager {
  private clients = new Map<ChatModel, AzureOpenAI>();

  constructor(private config: AzureOpenAIConfig) {}

  getClient(deployment: ChatModel) {
    return fromMap(
      this.clients,
      deployment,
      () => new AzureOpenAI({ ...this.config, deployment, apiVersion: '2025-03-01-preview' })
    );
  }
}

import {
  Hint,
  TEnum,
  Type,
  TypeGuard,
  type ObjectOptions,
  type Static,
  type StaticDecode,
  type TLiteralValue,
  type TNull,
  type TObject,
  type TransformFunction,
  type TSchema,
  type TUnion
} from '@sinclair/typebox/type';
import { Value } from '@sinclair/typebox/value';

export {
  type SchemaOptions,
  type Static,
  type StringOptions,
  type TObject,
  type TProperties,
  type TSchema,
  type TString,
  type TVoid
} from '@sinclair/typebox/type';

export function fromJsonSchema<T extends TSchema>(schema: TSchema, value: any): Static<T> {
  return Value.Parse(schema, value);
}

export function fromPartialJsonSchema<T extends TSchema>(schema: T, value: any) {
  return Value.Convert(schema, Value.Clean(schema, value)) as Partial<Static<T>>;
}

export const isJsonSchema = TypeGuard.IsSchema;

export const any = Type.Any;
export const string = Type.String;
export const isString = TypeGuard.IsString;
export const uuid = () => Type.String({ format: 'uuid' });
export const email = () => Type.String({ format: 'email' });
export const url = () => Type.String({ format: 'url' });
export const number = Type.Number;
export const isNumber = TypeGuard.IsNumber;
export const boolean = Type.Boolean;
export const isBoolean = TypeGuard.IsBoolean;
export const date = Type.Date;
export const isDate = TypeGuard.IsDate;
export const object = Type.Object;
export const isObject = TypeGuard.IsObject;
export type TObjectProperties<T extends TObject> = T['properties'];
export const getObjectProperties = <T extends TObject>(object: T) => object.properties as TObjectProperties<T>;
export const getObjectProperty = <T extends TObject, K extends keyof TObjectProperties<T>>(object: T, key: K) =>
  getObjectProperties(object)[key];
export const option = Type.Enum;
export const isOption = (s: unknown): s is TEnum<any> => (TypeGuard.IsUnion(s) ? s[Hint] === 'Enum' : false);
export const getOptions = <T extends Record<string, string | number>>(s: TEnum<T>) =>
  s.anyOf.reduce((e, o) => ({ ...e, [o.const]: o.const }), {} as T);
export const array = Type.Array;
export const isArray = TypeGuard.IsArray;
export const record = Type.Record;
export const isRecord = TypeGuard.IsRecord;
export const keyValue = (options?: ObjectOptions) => record(string(), any(), options);
export const ref = Type.Ref;
export const optional = Type.Optional;
export const isOptional = TypeGuard.IsOptional;
export const partial = Type.Partial;
export const union = Type.Union;
export const isUnion = TypeGuard.IsUnion;
export const unionOf = <S extends TSchema>(s: TUnion<S[]>) => s.anyOf[0];
export const tuple = Type.Tuple;
export const literal = Type.Literal;
export const isLiteral = TypeGuard.IsLiteral;
export const literals = <const T extends readonly TLiteralValue[]>(values: T) =>
  union(values.map((value) => literal<T[number]>(value)));
export const templateLiteral = Type.TemplateLiteral;
export const nullable = <T extends TSchema>(s: T, options?: SchemaOptions) =>
  union([s, Type.Null(options)], { default: null });
export const isNullable = (s: unknown): s is TUnion<[TSchema, TNull]> =>
  TypeGuard.IsUnion(s) && s.anyOf.some(TypeGuard.IsNull);
export const nullableOf = <S extends TSchema>(s: TUnion<[S, TNull]>) => s.anyOf.find((v) => !TypeGuard.IsNull(v)) as S;
export const none = Type.Void;

export const combine = Type.Composite;
export const recursive = Type.Recursive;
export const pick = Type.Pick;

export const transform = <S extends TSchema, U extends unknown>(
  schema: S,
  decode: TransformFunction<StaticDecode<S>, U>
) => {
  return Type.Transform(schema)
    .Decode(decode)
    .Encode((value) => value);
};

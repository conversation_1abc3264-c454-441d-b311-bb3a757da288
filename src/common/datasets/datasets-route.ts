import { sitesRoute } from '~/common/location/sites-route';
import { authMatch } from '~/common/ui/identity';
import { Route } from '~/common/ui/routing';
import { isDefined } from '~/common/utils/value';
import { DatasetWrite } from './dataset-permissions';
import { DatasetsMenuConfig } from './datasets-menu-config';

const sharedRoutes = {
  sites: sitesRoute
};

export function datasetsRoute(items: Array<keyof typeof sharedRoutes | Route>): Route {
  const routes = items
    .map((item) => (typeof item === 'string' ? sharedRoutes[item] : item))
    .filter((route) => isDefined(route.path));

  return {
    path: 'datasets',
    canMatch: [authMatch],
    children: routes,
    data: {
      title: 'Datasets',
      authorize: DatasetWrite,
      menu: {
        icon: 'database',
        config: DatasetsMenuConfig,
        context: routes.map(({ path }) => path)
      }
    }
  };
}

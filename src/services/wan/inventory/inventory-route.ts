import { authMatch } from '~/common/ui/identity';
import { Route } from '~/common/ui/routing';
import { CircuitRead } from './circuit-permissions';
import { CircuitStatus } from './circuit-status';

export const inventoryRoute: Route = {
  path: 'inventory',
  canMatch: [authMatch],
  data: {
    title: 'Inventory',
    authorize: CircuitRead,
    menu: {
      icon: 'deployment-unit',
      queryParams: { status: CircuitStatus.InProduction }
    }
  },
  children: [
    {
      path: '',
      pathMatch: 'full',
      loadComponent: () => import('./inventory-component').then((c) => c.InventoryComponent)
    }
  ]
};

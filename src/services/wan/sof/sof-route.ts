import { authMatch } from '~/common/ui/identity';
import { Route } from '~/common/ui/routing';
import { SofRead } from './sof-permissions';

export const sofRoute: Route = {
  path: 'sof',
  canMatch: [authMatch],
  data: {
    title: 'Service Order Forms',
    authorize: SofRead,
    menu: {
      icon: 'file-protect'
    }
  },
  children: [
    {
      path: '',
      pathMatch: 'full',
      loadComponent: () => import('./sofs-component').then((c) => c.SofsComponent)
    }
  ]
};

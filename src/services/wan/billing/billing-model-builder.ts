import { create, EntityManager } from '~/common/database';
import { EventBus } from '~/common/events';
import { Provider } from '~/common/providers';
import { mapValues } from '~/common/utils/object';
import { upperFirst } from '~/common/utils/string';
import { isEqual } from '~/common/utils/value';
import { BillingModelBuildHandler, BuildModel } from '~/modules/billing';
import { wanServiceId } from '~/services/wan/wan-service-id';
import { BillingItem } from './billing-item';

@Provider()
export class BillingModelBuilder {
  constructor(
    private entityManager: EntityManager,
    private eventBus: EventBus
  ) {}

  @BillingModelBuildHandler(wanServiceId)
  async buildModel({ vendorId, period }: typeof BuildModel) {
    await this.entityManager.transaction(async (em) => {
      await this.buildItems(vendorId, em);
      await this.buildCharges(vendorId, period, em);
    });
  }

  private async buildItems(vendorId: string, em: EntityManager) {
    this.eventBus.info('items', 'Building items', { vendorId });
    const [stored, current] = await Promise.all([
      em.findBy(BillingItem, { vendorId }),
      this.runQuery(
        em,
        `SELECT c.circuit_guid AS id,
                v.id AS "vendorId",
                c.circuit_id AS "refId",
                c.circuit_name AS name,
                c.circuit_type AS type,
                c.circuit_level AS level,
                c.site AS "siteCode",
                s.country_id AS "countryId",
                c.order_reference AS "orderReference",
                c.request_reference AS "requestReference",
                (CASE WHEN c.circuit_type IN ('Wan management charges') THEN 'other' ELSE 'circuit' END) AS kind
           FROM wan.v_circuit_inventory c
           JOIN vendors v ON v.ref_id = lower(replace(c.vendor, '_', '-'))
           LEFT JOIN sites s ON s.code = c.site
           `,
        [vendorId, 'v.id = :value']
      )
    ]);
    for (const data of current) {
      // trim values, empty strings as null
      const item = mapValues(data, (value) => {
        if (typeof value === 'string') {
          value = value.trim();
          if (!value.length) {
            return null;
          }
        }
        return value;
      });

      const existing = stored.find((c) => c.id === item.id);
      if (item.level) {
        item.level = upperFirst(item.level.toLowerCase());
      }
      if (existing && isEqual(existing, item)) {
        continue;
      } else {
        await em.save(create(BillingItem, item));
      }
    }
  }

  private async buildCharges(vendorId: string, period: string, em: EntityManager) {
    this.eventBus.info('charges', 'Building charges', { vendorId, period });
    await this.runQuery(
      em,
      `DELETE FROM wan.billing_charges`,
      [vendorId, 'vendor_id = :value'],
      [period, 'period = :value']
    );
    await this.runQuery(
      em,
      `INSERT INTO wan.billing_charges (
        vendor_id, item_id, period, service_id, service_line, type, description,
        total_amount, total_amount_local, tax_amount_local, currency, invoice_number, entity
       )
       SELECT v.id,
              i.id,
              to_char(c.period, 'YYYY-MM'),
              c.service_id,
              c.service_line,
              c.charge_type_description,
              c.charge_description,
              c.charge_amount,
              c.charge_amount_lc,
              c.tax_amount_lc,
              c.currency,
              c.bill_invoice_number,
              c.entity
         FROM wan.v_circuit_cost c
        INNER JOIN vendors v ON v.ref_id = lower(replace(c.vendor, '_', '-'))
         -- cost doesn't have to be assigned to a circuit
         LEFT JOIN wan.billing_items i ON i.id = c.circuit_guid
        WHERE c.period IS NOT NULL`,
      [vendorId, 'v.id = :value'],
      [period, `to_char(c.period, 'YYYY-MM') = :value`]
    );
  }

  /**
   * Run raw query with bound parameters
   */
  private runQuery(em: EntityManager, sql: string, ...conditions: [any, string][]) {
    const params = [];
    const where = [];
    for (const [value, condition] of conditions) {
      if (value) {
        where.push(condition.replace(':value', `$${where.length + 1}`));
        params.push(value);
      }
    }
    return em.query(
      sql + (where.length ? ` ${sql.includes('WHERE') ? 'AND' : 'WHERE'} ${where.join(' AND ')}` : ''),
      params
    );
  }
}

import { ManyToOne, OneToOne, Optional, PrimaryGenerated, Relation, Required, Table } from '~/common/database/entity';
import { Vendor } from '~/common/vendors/vendor';
import { BillingDocument } from '~/modules/billing/billing-document';
import { BillingItem } from './billing-item';

@Table('wan.billing_charges')
export class BillingCharge {
  @PrimaryGenerated('uuid')
  id: string;

  @Required()
  vendorId: string;

  @ManyToOne(() => Vendor, 'vendorId')
  vendor?: Relation<Vendor>;

  @Optional()
  itemId?: string;

  @ManyToOne(() => BillingItem, 'itemId')
  item?: Relation<BillingItem>;

  @Required()
  period: string;

  @Required()
  type: string;

  @Required()
  description: string;

  @Required()
  totalAmount: number;

  @Optional()
  totalAmountLocal?: number;

  @Optional()
  currency?: string;

  @Optional()
  serviceId?: string;

  @Optional()
  serviceLine?: string;

  @Optional()
  entity?: string;

  @Optional()
  invoiceNumber?: string;

  /**
   * Billing document reference
   */
  @OneToOne(() => BillingDocument, null)
  document?: Relation<BillingDocument>;
}

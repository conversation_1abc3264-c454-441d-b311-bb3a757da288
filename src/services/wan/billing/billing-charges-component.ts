import { CommonModule } from '@angular/common';
import { ChangeDetectionStrategy, Component, Input, OnChanges } from '@angular/core';
import { ReactiveFormsModule } from '@angular/forms';
import { PeriodPipe } from '~/common/periods/period-pipe';
import { DrawerService } from '~/common/ui/drawer';
import { NgZorroModule } from '~/common/ui/ng-zorro';
import { uniq } from '~/common/utils/array';
import { groupBy } from '~/common/utils/collection';
import { EntriesPipe } from '~/common/utils/object/entries-pipe';
import { isDefined } from '~/common/utils/value';
import { BillingDocument } from '~/modules/billing/billing-document';
import { openBillingDocument } from '~/modules/billing/billing-document-detail-component';
import { BillingCharge } from './billing-charge';

@Component({
  selector: 'billing-charges',
  imports: [CommonModule, ReactiveFormsModule, NgZorroModule, EntriesPipe, PeriodPipe],
  templateUrl: './billing-charges-component.html',
  styleUrl: './billing-charges-component.less',
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class BillingChargesComponent implements OnChanges {
  @Input({ required: true })
  charges: BillingCharge[];

  @Input()
  highlight?: string;

  chargesPerPeriod: Record<string, BillingCharge[]> = {};

  constructor(private drawerService: DrawerService) {}

  ngOnChanges() {
    this.chargesPerPeriod = groupBy(this.charges, 'period');
  }

  getTotal(charges: BillingCharge[]) {
    // no total for single charge
    if (charges.length <= 1) {
      return null;
    }

    const total: { amount: number; amountLocal?: number; currency?: string } = { amount: 0 };
    for (const charge of charges) {
      total.amount += charge.totalAmount;
      if (charge.totalAmountLocal && charge.currency !== 'USD') {
        total.currency = charge.currency!;
        total.amountLocal = (total.amountLocal ?? 0) + charge.totalAmountLocal;
      }
    }
    return total;
  }

  getInvoices(charges: BillingCharge[]) {
    const invoices = uniq(charges.map((charge) => charge.invoiceNumber).filter(isDefined));
    return invoices.length ? invoices : null;
  }

  getDocument(invoiceNumber: string, charges: BillingCharge[]) {
    return charges.find((charge) => charge.invoiceNumber === invoiceNumber)?.document;
  }

  openDocument(document: BillingDocument) {
    openBillingDocument(this.drawerService, { documentId: document.id });
  }
}

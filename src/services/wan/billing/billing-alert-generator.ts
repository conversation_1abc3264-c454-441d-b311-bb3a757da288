import { EntityManager, EntityOperation, EntityTrigger } from '~/common/database';
import { everyDayAt, Worker } from '~/common/jobs';
import { Site, SiteStatus } from '~/common/location';
import { Provider } from '~/common/providers';
import { uniqBy } from '~/common/utils/array';
import { toISODate } from '~/common/utils/date';
import { AlertsManager } from '~/modules/alerts';
import { BillingModelBuild, BillingModelBuildStatus } from '~/modules/billing';
import { Circuit } from '~/services/wan/inventory/circuit';
import { CircuitStatus } from '~/services/wan/inventory/circuit-status';
import { wanServiceId } from '~/services/wan/wan-service-id';
import { BillingCharge } from './billing-charge';
import { BillingItem } from './billing-item';
import { BillingMismatchAlertData, billingMismatchAlertTypes } from './billing-mismatch-alert-types';
import { billingUnmatchedCircuitAlertType } from './billing-unmatched-circuit-alert-type';

@Provider()
export class BillingAlertGenerator {
  constructor(
    private entityManager: EntityManager,
    private alertsManager: AlertsManager
  ) {}

  /**
   * Create billing alerts
   */
  @Worker({
    description: 'Create billing alerts',
    schedule: everyDayAt(4)
  })
  @EntityTrigger({
    description: 'Create billing alerts after billing model is built',
    after: [EntityOperation.Update],
    entity: BillingModelBuild,
    changed: ['status'],
    filter: ({ entity: b }) => b.serviceId === wanServiceId && b.status === BillingModelBuildStatus.Finished,
    toArgs: () => []
  })
  async createAlerts() {
    await this.createUnmatchedCircuitAlerts();
    await this.createBillingMismatchAlerts();
  }

  /**
   * Create unmatched circuit alerts
   */
  async createUnmatchedCircuitAlerts() {
    await this.alertsManager.createVendorAlerts(
      wanServiceId,
      billingUnmatchedCircuitAlertType,
      async ({ vendorId, period, create }) => {
        const canCreate = await this.canCreateAlerts(vendorId, period);
        if (!canCreate) {
          return false;
        }

        // get items with no circuit ID...
        const query = this.entityManager.createQueryBuilder(BillingItem, 'item');
        query
          .where('item.vendorId = :vendorId', { vendorId })
          .andWhere('item.kind = :kind', { kind: 'circuit' })
          .andWhere('item.circuit_id IS NULL')
          .andWhere(
            // ...that have non-zero charges for the period
            `EXISTS ${query
              .subQuery()
              .select('1')
              .from(BillingCharge, 'charge')
              .where('charge.itemId = item.id')
              .andWhere('charge.period = :period', { period })
              // multiple ceased BT circuits with zero charges are still reported in data => skip them
              .andWhere('charge.totalAmount > 0')
              .getQuery()}`
          )
          .orderBy('item.refId');

        const items = await query.getMany();
        for (const item of uniqBy(items, 'refId')) {
          await create({
            message: `Billed circuit "${item.refId}" not found in inventory`,
            data: { id: item.id, refId: item.refId },
            refId: item.id
          });
        }
      }
    );
  }

  /**
   * Create billing mismatch alerts
   */
  async createBillingMismatchAlerts() {
    for (const [id, type] of Object.entries(billingMismatchAlertTypes)) {
      await this.alertsManager.createVendorAlerts(wanServiceId, type, async ({ vendorId, period, create }) => {
        const canCreate = await this.canCreateAlerts(vendorId, period);
        if (!canCreate) {
          return false;
        }

        switch (id) {
          case 'mrc':
            for (const data of await this.getMrcMismatches(vendorId, period)) {
              await create({
                message: `Billed charge does not match expected MRC for circuit "${data.refId}"`,
                data,
                refId: data.circuitId
              });
            }
            break;

          case 'unbilled':
            for (const data of await this.getUnbilledActiveCircuits(vendorId, period)) {
              await create({
                message: `No charge for active circuit "${data.refId}"`,
                data,
                refId: data.circuitId
              });
            }
            break;

          case 'ceased':
            for (const data of await this.getCeasedCircuitCharges(vendorId, period)) {
              await create({
                message: `Unexpected charge for ceased circuit "${data.refId}"`,
                data,
                refId: data.circuitId
              });
            }
            break;

          case 'closed':
            for (const data of await this.getActiveCircuitsOnClosedSites(vendorId, period)) {
              await create({
                message: `Billed active circuit "${data.refId}" at the closed site "${data.site?.code}"`,
                data,
                refId: data.circuitId
              });
            }
            break;
        }
      });
    }
  }

  /**
   * Check general prerequisities for a given vendor and period
   */
  private async canCreateAlerts(vendorId: string, period: string) {
    // charges must exist for the period
    const charges = await this.entityManager.countBy(BillingCharge, { vendorId, period });
    if (!charges) {
      return false;
    }

    // billing model must be fully built (from all expected data files)
    const lastBuild = await this.entityManager.findOne(BillingModelBuild, {
      where: { vendorId, period },
      order: { finishDate: 'DESC' }
    });
    return lastBuild ? !lastBuild.isPartial : false;
  }

  /**
   * Get MRC mismatches
   * @todo use query builder instead of raw SQL
   */
  private getMrcMismatches(vendorId: string, period: string): Promise<BillingMismatchAlertData[]> {
    return this.entityManager.query<BillingMismatchAlertData[]>(
      `SELECT c.id AS "circuitId", c.ref_id AS "refId", x.amount, COALESCE(x.currency, 'USD') AS "currency",
              json_build_object('amount', c.total_mrc, 'currency', COALESCE(c.currency, 'USD')) AS "mrc"
         FROM (
            SELECT bi.circuit_id, currency, round(sum(coalesce(bc.total_amount_local, bc.total_amount)), 2) amount
              FROM wan.billing_items bi
              JOIN wan.billing_charges bc ON bc.item_id = bi.id
             WHERE bi.vendor_id = $1 AND bc.period = $2
               AND bi.circuit_id IS NOT NULL
             GROUP BY 1, 2
         ) x
         JOIN wan.circuits c ON c.id = x.circuit_id
        WHERE x.amount > 0 AND round(x.amount) != round(c.total_mrc)`,
      [vendorId, period]
    );
  }

  /**
   * Get unexpected charges for ceased circuits
   */
  private getCeasedCircuitCharges(vendorId: string, period: string): Promise<BillingMismatchAlertData[]> {
    const amount = 'sum(charge.totalAmount)';
    return this.entityManager
      .createQueryBuilder(BillingItem, 'item')
      .innerJoin('item.charges', 'charge')
      .innerJoin('item.circuit', 'circuit')
      .select('circuit.id', 'circuitId')
      .addSelect('circuit.refId', 'refId')
      .addSelect(`COALESCE(charge.currency, 'USD')`, 'currency')
      .addSelect(amount, 'amount')
      .where('item.vendorId = :vendorId', { vendorId })
      .andWhere('charge.period = :period', { period })
      .andWhere('charge.totalAmount > 0')
      .andWhere('circuit.status = :status', { status: CircuitStatus.Ceased })
      .groupBy('1')
      .addGroupBy('2')
      .addGroupBy('3')
      .having(`${amount} > 0`)
      .getRawMany();
  }

  /**
   * Get active circuits with no charges
   */
  private async getUnbilledActiveCircuits(vendorId: string, period: string): Promise<BillingMismatchAlertData[]> {
    const query = this.entityManager.createQueryBuilder(Circuit, 'circuit');
    query
      .where('circuit.vendorId = :vendorId', { vendorId })
      .andWhere('circuit.status = :status', { status: CircuitStatus.InProduction })
      .andWhere(
        `NOT EXISTS ${query
          .subQuery()
          .select('1')
          .from(BillingItem, 'item')
          .innerJoin('item.charges', 'charge')
          .where('item.circuitId = circuit.id')
          .andWhere('charge.period = :period', { period })
          .getQuery()}`
      );

    const circuits = await query.getMany();
    return circuits.map((circuit) => ({ circuitId: circuit.id, refId: circuit.refId! }));
  }

  /**
   * Get active circuits with charges on a closed site
   */
  private async getActiveCircuitsOnClosedSites(vendorId: string, period: string): Promise<BillingMismatchAlertData[]> {
    const sites = await this.entityManager.findBy(Site, { status: SiteStatus.Closed, isDefault: true });
    const query = this.entityManager.createQueryBuilder(Circuit, 'circuit');
    query
      .where('circuit.vendorId = :vendorId', { vendorId })
      .andWhere('circuit.status = :status', { status: CircuitStatus.InProduction })
      .andWhere('circuit.siteCode = ANY(:siteCodes)', { siteCodes: sites.map((s) => s.code) })
      .andWhere(
        `EXISTS ${query
          .subQuery()
          .select('1')
          .from(BillingItem, 'item')
          .innerJoin('item.charges', 'charge')
          .where('item.circuitId = circuit.id')
          .andWhere('charge.period = :period', { period })
          .getQuery()}`
      );

    const circuits = await query.getMany();
    return circuits.map((circuit) => {
      const site = sites.find((site) => site.code === circuit.siteCode)!;
      return {
        circuitId: circuit.id,
        refId: circuit.refId!,
        site: {
          code: site.code,
          closedDate: site.closedDate ? toISODate(site.closedDate) : undefined
        }
      };
    });
  }
}

import { write<PERSON><PERSON> } from 'fs-extra';
import { DataSource, DataSourceCredential } from '~/common/data-sources';
import { EntityManager } from '~/common/database';
import { InvalidStateError, NotFoundError } from '~/common/errors';
import { EventBus } from '~/common/events';
import { createHttpClient, downloadFile, HttpClient } from '~/common/http';
import { getNextPeriod } from '~/common/periods';
import { Provider } from '~/common/providers';
import { orderBy, sortBy } from '~/common/utils/collection';
import { addDays } from '~/common/utils/date';
import {
  BillingFile,
  BillingFileDownloadContext,
  BillingFileDownloader,
  BillingFileDownloadResult,
  BillingFileDownloadStatus,
  BillingFileStatus,
  createBillingFileName
} from '~/modules/billing';

// API request timeout
const timeout = 30000;

@Provider()
@BillingFileDownloader('contact-centers:genesys:api')
export class ApiDownloader implements BillingFileDownloader<HttpClient> {
  constructor(
    private entityManager: EntityManager,
    private eventBus: EventBus
  ) {}

  async initialize({ username, password, properties }: DataSourceCredential, { url }: DataSource) {
    const baseUrl = properties?.URL ?? url;
    return createHttpClient({
      // *.pure.cloud API is prefixed with "/platform"
      baseURL: `${baseUrl}${baseUrl.endsWith('pure.cloud') ? '/platform' : ''}/api/v2`,
      timeout,
      events: this.eventBus,
      oauth2: {
        clientId: username,
        clientSecret: password,
        url: (() => {
          // different host for OAuth API
          // https://api.mypurecloud.com => https://login.mypurecloud.com
          // https://aps1.pure.cloud => https://login.aps1.pure.cloud
          const loginUrl = new URL(baseUrl);
          const { hostname } = loginUrl;
          loginUrl.hostname = `login.${hostname.endsWith('pure.cloud') ? hostname : hostname.split('.').slice(1).join('.')}`;
          loginUrl.pathname = '/oauth/token';
          return loginUrl.toString();
        })()
      }
    });
  }

  async downloadFile(api: HttpClient, context: BillingFileDownloadContext): Promise<BillingFileDownloadResult> {
    switch (context.type) {
      case 'invoice':
        return this.downloadInvoice(api, context);
      case 'subscription-overview':
        return this.downloadSubscriptionOverview(api, context);
      case 'usage':
        return this.downloadUsage(api, context);
      case 'voice-usage':
        return this.downloadVoiceUsage(api, context);
      default:
        throw new InvalidStateError(`Downloader not defined for type "${context.type}"`);
    }
  }

  /**
   * PDF invoice download
   */
  private async downloadInvoice(api: HttpClient, { file, targetDir }: BillingFileDownloadContext) {
    type Invoices = { entities: { id: string }[] };
    type Invoice = { id: string; invoiceDate: string };
    type InvoiceFile = { url: string };

    // find invoices for a given period
    const period = getNextPeriod(file.period);
    const { data } = await api.get<Invoices>('/billing/invoices');
    const invoices: Invoice[] = [];
    // order by invoice sequence number from ID (e.g. in8325-202600757, inv-8325-171128, inv-8325-98834)
    const entities = orderBy(data.entities, [(entity) => parseInt(entity.id.split('-').slice(-1)[0])], ['desc']);
    for (const { id } of entities) {
      // check if invoice file is already downloaded
      const downloaded = await this.entityManager.findOneBy(BillingFile, {
        vendorId: file.vendorId,
        name: `${id}.pdf`,
        status: BillingFileStatus.Processed
      });
      if (downloaded) {
        continue;
      }

      try {
        const { data: invoice } = await api.get<Invoice>(`/billing/invoices/${id}`);
        if (invoice.invoiceDate.startsWith(period)) {
          invoices.push(invoice);
        } else if (invoice.invoiceDate < period) {
          // skip older invoices
          break;
        }
      } catch (e) {
        this.eventBus.error('error', e);
      }
    }

    const [invoice] = sortBy(invoices, 'invoiceDate');
    if (invoice) {
      const { data: file } = await api.get<InvoiceFile>(`/billing/invoices/${invoice.id}/pdf`);
      return {
        path: await downloadFile(api, file.url, targetDir)
      };
    } else {
      throw new NotFoundError('Invoice not found');
    }
  }

  /**
   * Subscription overview data download
   */
  private async downloadSubscriptionOverview(api: HttpClient, { file, targetDir }: BillingFileDownloadContext) {
    const endDate = addDays(file.cycleEndDate!, 1);
    const { data } = await api.get('/billing/subscriptionoverview', {
      params: {
        periodEndingTimestamp: endDate.getTime() - 1
      }
    });
    const path = `${targetDir}/${this.createFileName(file)}`;
    await writeJson(path, data, { spaces: 2 });
    return { path };
  }

  /**
   * Usage data download
   */
  private async downloadUsage(api: HttpClient, { file, targetDir }: BillingFileDownloadContext) {
    const startTime = file.cycleStartDate!.getTime();
    const endTime = addDays(file.cycleEndDate!, 1).getTime() - 1;
    const { data } = await api.get(`/billing/reports/billableUsageData/${startTime}_${endTime}/csv`);
    return {
      path: await downloadFile(api, data.url, targetDir, { filename: this.createFileName(file) })
    };
  }

  /**
   * Voice data download
   */
  private async downloadVoiceUsage(
    api: HttpClient,
    { file, download, targetDir }: BillingFileDownloadContext<{ id: string }>
  ): Promise<BillingFileDownloadResult> {
    const reportUrl = `/carrierservices/report/usage/calls/csv`;

    // get current reports
    type Entity = { id: string; status: string; response: { url: string } };
    const { data } = await api.get<{ entities: Entity[] }>(reportUrl);

    // already requested?
    const id = download.data?.id;
    const existing = id ? data.entities.find((entity) => entity.id === id) : null;

    switch (existing?.status) {
      case 'COMPLETE':
        // download finished report
        const { data: report } = await api.get<Entity>(`${reportUrl}/${id}`);
        const { url: downloadUrl } = report.response;
        if (downloadUrl) {
          return {
            path: await downloadFile(api, downloadUrl, targetDir, { filename: this.createFileName(file) })
          };
        } else {
          throw new NotFoundError('No report generated');
        }

      case 'RUNNING':
        // wait for report to finish
        return {};

      default:
        // request new report
        const { data: request } = await api.post<Entity>(reportUrl, null, {
          params: {
            from: file.cycleStartDate!.toISOString(),
            to: new Date(addDays(file.cycleEndDate!, 1).getTime() - 1).toISOString()
          }
        });
        return {
          status: BillingFileDownloadStatus.Requested,
          data: { id: request.id },
          retryTimeout: 300
        };
    }
  }

  private createFileName(file: BillingFile) {
    return createBillingFileName(file.name, file);
  }
}

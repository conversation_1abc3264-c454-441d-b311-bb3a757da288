import { List } from '~/common/lists';
import { Route } from '~/common/ui/routing';
import { adminRoute } from '~/modules/admin/admin-route';
import { alertsRoute } from '~/modules/alerts/alerts-route';
import { billingFileDownloadErrorAlertType } from '~/modules/billing/billing-file-download-error-alert-type';
import { billingRoute } from '~/modules/billing/billing-route';
import { contactsRoute } from '~/modules/contacts/contacts-route';
import { contractsRoute } from '~/modules/contracts/contracts-route';
import { dashboardRoute } from './billing/dashboard-route';
import { overviewRoute } from './billing/overview-route';
import { usageRoute } from './billing/usage-route';

export const contactCentersRoute: Route = {
  data: {
    serviceSettings: {
      alertTypes: new List([billingFileDownloadErrorAlertType])
    }
  },
  children: [
    dashboardRoute,
    overviewRoute,
    usageRoute,
    billingRoute,
    contractsRoute,
    alertsRoute,
    contactsRoute,
    adminRoute(['billingFileTypes', 'users', 'vendors'])
  ]
};

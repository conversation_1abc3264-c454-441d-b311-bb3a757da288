import { authMatch } from '~/common/ui/identity';
import { Route } from '~/common/ui/routing';
import { BillingRead } from './billing-permissions';

export const usageRoute: Route = {
  path: 'license-usage',
  canMatch: [authMatch],
  data: {
    title: 'License usage',
    authorize: BillingRead,
    menu: {
      icon: 'customer-service'
    }
  },
  children: [
    {
      path: '',
      pathMatch: 'full',
      loadComponent: () => import('./usage-component').then((c) => c.UsageComponent)
    }
  ]
};

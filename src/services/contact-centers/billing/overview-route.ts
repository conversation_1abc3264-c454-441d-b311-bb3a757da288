import { authMatch } from '~/common/ui/identity';
import { Route } from '~/common/ui/routing';
import { BillingRead } from './billing-permissions';

export const overviewRoute: Route = {
  path: 'overview',
  canMatch: [authMatch],
  data: {
    title: 'License overview',
    authorize: BillingRead,
    menu: {
      icon: 'dashboard'
    }
  },
  children: [
    {
      path: '',
      pathMatch: 'full',
      loadComponent: () => import('./overview-component').then((c) => c.OverviewComponent)
    }
  ]
};

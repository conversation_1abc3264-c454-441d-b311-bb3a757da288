import { EntityManager, mapRawEntities } from '~/common/database';
import { InvalidStateError } from '~/common/errors';
import { EventBus } from '~/common/events';
import { Provider } from '~/common/providers';
import { toMap } from '~/common/utils/map';
import { isNil } from '~/common/utils/value';
import { Vendor } from '~/common/vendors';
import { BillingModelBuildHandler, BuildModel } from '~/modules/billing';
import { contactCentersServiceId } from '~/services/contact-centers/contact-centers-service-id';
import { BillingScope } from './billing-scope';
import { Product } from './product';
import { Subscription } from './subscription';
import { Usage } from './usage';

@Provider()
export class BillingModelBuilder {
  constructor(
    private entityManager: EntityManager,
    private eventBus: EventBus
  ) {}

  /**
   * @todo avoid using SQL queries
   */
  @BillingModelBuildHandler(contactCentersServiceId)
  async buildModel({ vendorId, period }: typeof BuildModel) {
    const { schema } = await this.entityManager.findOneByOrFail(Vendor, { id: vendorId });

    await this.entityManager.transaction(async (em) => {
      // subscriptions
      this.eventBus.verbose('subscriptions', 'Populating subscriptions');
      const products = toMap(await em.findBy(Product, { vendorId }), 'name');
      const subscriptions = await mapRawEntities<Subscription, { product: string }>(
        await em.query(`SELECT * FROM ${schema}.subscriptions WHERE period = $1`, [period]),
        Subscription,
        async (raw, subscription) => {
          const name = raw.product;
          const product = products.has(name) ? products.get(name)! : await em.save(Product, { name, vendorId });
          products.set(name, product);
          subscription.product = product;
          subscription.vendorId = vendorId;
          calculateSubscriptionProperties(subscription);
        }
      );
      await em.delete(Subscription, { vendorId, period });
      await em.save(subscriptions);

      // usage
      this.eventBus.verbose('usage', 'Populating usage');
      const usages = await mapRawEntities<Usage, { accountId: string; name: string }>(
        await em.query(`SELECT * FROM ${schema}.usage WHERE period = $1 ORDER BY created_date`, [period]),
        Usage,
        async ({ accountId, name }, usage) => {
          const { product, item, quantity } = parseUsageName(name);
          const subscription = subscriptions.find((s) => s.accountId === accountId && s.product!.name === product);
          if (!subscription) {
            throw new InvalidStateError(`Subscription not found for usage product "${product}"`);
          }

          subscription.usage = [...(subscription.usage ?? []), usage];
          const { prepaidQuantity, prepaidPrice, overagePrice, includedQuantity } = subscription;
          const totalQuantity = subscription.usage!.reduce((a, u) => a + (u.quantity ?? 1), 0) ?? 0;
          usage.subscriptionId = subscription.id;
          usage.item = item;
          usage.quantity = quantity;
          usage.scope =
            totalQuantity <= (includedQuantity ?? 0)
              ? BillingScope.Included
              : totalQuantity <= (prepaidQuantity ?? 0)
                ? BillingScope.Prepaid
                : BillingScope.Overage;
          usage.cost =
            quantity *
            (usage.scope === BillingScope.Included
              ? 0
              : usage.scope === BillingScope.Prepaid
                ? (prepaidPrice ?? 0)
                : (overagePrice ?? 0));
        }
      );
      await em.save(usages);
    });
  }
}

function calculateSubscriptionProperties(s: Subscription) {
  const { usageQuantity, prepaidQuantity, prepaidPrice, overagePrice } = s;
  if (isNil(usageQuantity)) {
    return;
  }

  if (!isNil(prepaidQuantity) && !isNil(prepaidPrice)) {
    s.prepaidUsage = usageQuantity > prepaidQuantity ? prepaidQuantity : usageQuantity;
    s.overageUsage = usageQuantity > prepaidQuantity ? usageQuantity - prepaidQuantity : 0;
    s.prepaidAmount = prepaidQuantity * prepaidPrice;
    s.unusedAmount = usageQuantity < prepaidQuantity ? prepaidPrice * (prepaidQuantity - usageQuantity) : 0;
    s.unusedQuantity = prepaidQuantity > s.prepaidUsage ? prepaidQuantity - s.prepaidUsage : 0;
  } else {
    s.overageUsage = usageQuantity;
  }

  const includedQuantity = s.includedQuantity ?? 0;
  if (s.overageUsage > includedQuantity) {
    s.overageUsage -= includedQuantity;
  } else {
    s.overageUsage = 0;
  }

  s.overageAmount = (s.overageUsage ?? 0) * (overagePrice ?? 0);
  s.totalAmount = (s.prepaidAmount ?? 0) + s.overageAmount;
}

/**
 * Parse usage name to product, item and quantity
 */
function parseUsageName(name: string): { product: string; item?: string; quantity: number } {
  const match = name.match(/\(([^\(]+)\)/);
  if (match) {
    // AI Experience Tokens (Genesys Native Agent Assist @ 1 * 25 tokens)
    const [item, formula] = match[1].split('@').map((i) => i.trim());
    return {
      product: name.replace(match[0], '').trim(),
      item,
      quantity: parseFloat(eval(formula.replace(/[^0-9+\-*\.]/g, '')))
    };
  } else {
    return {
      product: name,
      quantity: 1
    };
  }
}

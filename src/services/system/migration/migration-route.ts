import { authMatch } from '~/common/ui/identity';
import { Route } from '~/common/ui/routing';
import { MigrationExecute } from './migration-permissions';

export const migrationRoute: Route = {
  path: 'migration',
  canMatch: [authMatch],
  data: {
    title: 'Migration',
    authorize: MigrationExecute,
    menu: {
      icon: 'cloud'
    }
  },
  children: [
    {
      path: '',
      pathMatch: 'full',
      loadComponent: () => import('./migration-component').then((c) => c.MigrationComponent)
    }
  ]
};

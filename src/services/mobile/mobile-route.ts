import { datasetsRoute } from '~/common/datasets/datasets-route';
import { List } from '~/common/lists/list';
import { Route } from '~/common/ui/routing';
import { adminRoute } from '~/modules/admin/admin-route';
import { alertsRoute } from '~/modules/alerts/alerts-route';
import { billingFileDownloadErrorAlertType } from '~/modules/billing/billing-file-download-error-alert-type';
import { billingRoute } from '~/modules/billing/billing-route';
import { contactsRoute } from '~/modules/contacts/contacts-route';
import { contractsRoute } from '~/modules/contracts/contracts-route';
import { reportsRoute } from '~/modules/reports/reports-route';
import { totalSpendAlertType } from './alerts/total-spend-alert-type';
import { userStatusAlertType } from './alerts/user-status-alert-type';
import { campaignsRoute } from './campaigns/campaigns-route';
import { inventoryRoute } from './inventory/inventory-route';

export const mobileRoute: Route = {
  data: {
    serviceSettings: {
      alertTypes: new List([totalSpendAlertType, billingFileDownloadErrorAlertType, userStatusAlertType])
    }
  },
  children: [
    reportsRoute,
    billingRoute,
    contractsRoute,
    inventoryRoute,
    campaignsRoute,
    alertsRoute,
    contactsRoute,
    datasetsRoute(['sites']),
    adminRoute(['billingFileTypes', 'reports', 'users', 'vendors'])
  ]
};

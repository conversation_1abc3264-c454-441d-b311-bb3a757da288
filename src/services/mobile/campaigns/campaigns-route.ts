import { authMatch } from '~/common/ui/identity';
import { Route, RouteData } from '~/common/ui/routing';
import { CampaignRead } from './campaign-permissions';
import { CampaignTypeWrite } from './campaign-type-permissions';
import { CampaignTypeResolver } from './campaign-type-resolver';
import { type CampaignTypeWithConfig } from './campaign-types-api-client';
import { CampaignsMenuConfig } from './campaigns-menu-config';

declare module '~/common/ui/routing/route-data' {
  interface RouteData {
    campaignType?: CampaignTypeWithConfig;
  }
}

export const campaignsRoute: Route = {
  path: 'campaigns',
  data: {
    title: $localize`:@@campaigns:Campaigns`,
    authorize: CampaignRead,
    menu: {
      icon: 'mail',
      config: CampaignsMenuConfig
    }
  },
  children: [
    {
      // feedback from users (eg. to cancel an unused SIM)
      path: ':campaignTypeId/:campaignId/feedback/:itemId',
      loadComponent: () => import('./campaign-feedback-component').then((m) => m.CampaignFeedbackComponent),
      data: {
        title: $localize`:@@feedback:Feedback`
      }
    },
    {
      // campaign administration (eg. /campaigns/top-consumers)
      path: ':campaignTypeId',
      canMatch: [authMatch],
      resolve: {
        campaignType: CampaignTypeResolver
      },
      data: {
        title: ({ campaignType }: RouteData) => campaignType!.name,
        authorize: CampaignRead
      },
      children: [
        {
          path: '',
          pathMatch: 'full',
          loadComponent: () => import('./campaigns-component').then((m) => m.CampaignsComponent)
        },
        {
          path: 'settings',
          loadComponent: () =>
            import('./campaign-type-settings-component').then((m) => m.CampaignTypeSettingsComponent),
          data: {
            title: 'Settings',
            authorize: CampaignTypeWrite
          }
        },
        {
          path: ':campaignId',
          loadComponent: () => import('./campaign-component').then((m) => m.CampaignComponent)
        }
      ]
    }
  ]
};

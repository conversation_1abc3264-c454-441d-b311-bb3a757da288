import { <PERSON>tityManager, IsNotNull } from '~/common/database';
import { Provider } from '~/common/providers';
import { Vendor, VendorStatus } from '~/common/vendors';
import { BillingModelBuildHandler, BuildModel } from '~/modules/billing';
import { mobileServiceId } from '~/services/mobile/mobile-service-id';

@Provider()
export class BillingModelBuilder {
  constructor(private entityManager: EntityManager) {}

  @BillingModelBuildHandler(mobileServiceId)
  async buildModel({ vendorId, isPartial }: typeof BuildModel) {
    if (isPartial) {
      return;
    }

    const vendor = await this.entityManager.findOneBy(Vendor, {
      id: vendorId,
      serviceId: mobileServiceId,
      schema: IsNotNull(),
      status: VendorStatus.Onboarded
    });
    if (vendor) {
      await this.entityManager.transaction(async (em) => {
        // execute recreate() procedure in vendor schema
        await em.query(`CALL ${vendor.schema}.recreate()`);
        // finalize data recreation
        await em.query('CALL reports.finalize_recreate($1)', [vendor.schema]);
      });
    }
  }
}

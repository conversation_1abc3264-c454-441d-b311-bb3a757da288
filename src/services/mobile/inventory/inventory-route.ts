import { authMatch } from '~/common/ui/identity';
import { Route } from '~/common/ui/routing';
import { InventoryItemRead, InventoryItemWrite, InventoryManagedDeviceRead } from './inventory-permissions';

export const inventoryRoute: Route = {
  path: 'inventory',
  canMatch: [authMatch],
  data: {
    title: 'Inventory',
    menu: {
      icon: 'appstore'
    }
  },
  children: [
    {
      path: 'circuits',
      data: {
        title: 'Circuits',
        authorize: InventoryItemRead
      },
      children: [
        {
          path: '',
          pathMatch: 'full',
          loadComponent: () => import('./inventory-component').then((c) => c.InventoryComponent)
        },
        {
          path: 'settings',
          loadComponent: () => import('./inventory-settings-component').then((c) => c.InventorySettingsComponent),
          data: {
            title: 'Settings',
            authorize: InventoryItemWrite
          }
        }
      ]
    },
    {
      path: 'managed-devices',
      data: {
        title: 'Managed devices',
        authorize: InventoryManagedDeviceRead
      },
      children: [
        {
          path: '',
          pathMatch: 'full',
          loadComponent: () => import('./managed-devices-component').then((c) => c.ManagedDevicesComponent)
        }
      ]
    }
  ]
};

import { authMatch } from '~/common/ui/identity';
import { Route } from '~/common/ui/routing';
import { ContactRead } from './contact-permissions';

export const contactsRoute: Route = {
  path: 'contacts',
  canMatch: [authMatch],
  data: {
    title: 'Contacts',
    authorize: ContactRead,
    menu: {
      icon: 'contacts'
    }
  },
  children: [
    {
      path: '',
      pathMatch: 'full',
      loadComponent: () => import('./contacts-component').then((c) => c.ContactsComponent)
    }
  ]
};

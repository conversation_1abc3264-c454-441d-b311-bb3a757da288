import { List } from '~/common/lists/list';
import { authMatch } from '~/common/ui/identity';
import { Route } from '~/common/ui/routing';
import { AlertRead } from './alert-permissions';
import { AlertTypeConfig } from './alert-type';
import { AlertsMenuConfig } from './alerts-menu-config';

declare module '~/common/services/service-settings' {
  interface ServiceSettingsValue {
    alertTypes?: List<string, AlertTypeConfig<any>>;
  }
}

export const alertsRoute: Route = {
  path: 'alerts',
  canMatch: [authMatch],
  data: {
    title: 'Alerts',
    authorize: AlertRead,
    menu: {
      icon: 'alert',
      config: AlertsMenuConfig
    }
  },
  children: [
    {
      path: '',
      pathMatch: 'full',
      loadComponent: () => import('./alerts-component').then((c) => c.AlertsComponent)
    }
  ]
};

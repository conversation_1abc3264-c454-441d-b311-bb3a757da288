import { Injectable } from '@angular/core';
import { ServiceApiClient } from '~/common/services/service-api-client';
import { BillingFileImport } from './billing-file-import';
import { BillingFileType } from './billing-file-type';
import {
  BillingFileTypeCreateDto,
  BillingFileTypeParseDto,
  BillingFileTypeThresholdCreateDto,
  BillingFileTypeThresholdUpdateDto,
  BillingFileTypeUpdateDto
} from './billing-file-types-dto';
import { BillingFileTypeImportsQueryParams, BillingFileTypesQueryParams } from './billing-file-types-query-params';

const baseUrl = 'billing/file-types';

@Injectable({ providedIn: 'root' })
export class BillingFileTypesApiClient {
  constructor(private apiClient: ServiceApiClient) {}

  getFileTypes(params: BillingFileTypesQueryParams = {}) {
    return this.apiClient.get<BillingFileType[], BillingFileTypesQueryParams>(baseUrl, params, {
      responseType: BillingFileType,
      onAction: true,
      onEntityEvents: [BillingFileType]
    });
  }

  getFileType(id: string, params: BillingFileTypesQueryParams = {}) {
    return this.apiClient.get<BillingFileType, BillingFileTypesQueryParams>(`${baseUrl}/${id}`, params, {
      responseType: BillingFileType
    });
  }

  createFileType(data: BillingFileTypeCreateDto) {
    return this.apiClient.post(baseUrl, data, {
      bodyType: BillingFileTypeCreateDto,
      responseType: BillingFileType
    });
  }

  updateFileType(id: string, data: BillingFileTypeUpdateDto) {
    return this.apiClient.patch(`${baseUrl}/${id}`, data, {
      bodyType: BillingFileTypeUpdateDto,
      responseType: BillingFileType
    });
  }

  deleteFileType(id: string) {
    return this.apiClient.delete(`${baseUrl}/${id}`);
  }

  createFileTypeThreshold(typeId: string, data: BillingFileTypeThresholdCreateDto) {
    return this.apiClient.post(`${baseUrl}/${typeId}/thresholds`, data, {
      bodyType: BillingFileTypeThresholdCreateDto
    });
  }

  updateFileTypeThreshold(typeId: string, thresholdId: string, data: BillingFileTypeThresholdUpdateDto) {
    return this.apiClient.patch(`${baseUrl}/${typeId}/thresholds/${thresholdId}`, data, {
      bodyType: BillingFileTypeThresholdUpdateDto
    });
  }

  deleteFileTypeThreshold(typeId: string, thresholdId: string) {
    return this.apiClient.delete(`${baseUrl}/${typeId}/thresholds/${thresholdId}`);
  }

  getFileTypeImports(typeId: string, params?: BillingFileTypeImportsQueryParams) {
    return this.apiClient.get<BillingFileImport[]>(`${baseUrl}/${typeId}/imports`, params);
  }

  parseFileTypeDocuments(typeId: string, params?: BillingFileTypeParseDto) {
    return this.apiClient.post(`${baseUrl}/${typeId}/parse`, params);
  }
}

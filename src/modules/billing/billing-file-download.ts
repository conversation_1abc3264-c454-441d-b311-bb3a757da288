import { DataSourceType } from '~/common/data-sources/data-source-type';
import { ManyToOne, Optional, PrimaryGenerated, Required, Table, type Relation } from '~/common/database/entity';
import { User } from '~/common/identity/user';
import { BillingFile } from './billing-file';
import { BillingFileDownloadStatus, billingFileDownloadStatuses } from './billing-file-download-status';

@Table('billing_file_downloads', { events: true })
export class BillingFileDownload<T = any> {
  @PrimaryGenerated('uuid')
  id: string;

  @Required()
  fileId: string;

  @ManyToOne(() => BillingFile, 'fileId')
  file?: Relation<BillingFile>;

  @Required({ enum: DataSourceType })
  sourceType: DataSourceType;

  @Required()
  createdDate: Date;

  @Required()
  startDate: Date;

  @Optional()
  finishDate?: Date;

  @Required({ list: billingFileDownloadStatuses })
  status: BillingFileDownloadStatus;

  @Optional()
  message?: string;

  @Optional()
  sourceUrl?: string;

  @Optional()
  screenshotUrl?: string;

  @Optional({ type: Object })
  data?: T;

  @Optional()
  userId?: string;

  @ManyToOne(() => User, 'userId')
  user?: Relation<User>;
}

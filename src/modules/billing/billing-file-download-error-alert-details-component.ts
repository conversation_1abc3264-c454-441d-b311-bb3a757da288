import { ChangeDetectionStrategy, Component, Input } from '@angular/core';
import { RouterLink } from '@angular/router';
import { NgZorroModule } from '~/common/ui/ng-zorro';
import { LinkPipe } from '~/common/ui/routing';
import { Alert } from '~/modules/alerts/alert';
import { AlertDetailsComponent, AlertDetailsView } from '~/modules/alerts/alert-details-component';
import { BillingFileDownloadErrorAlertData } from './billing-file-download-error-alert-type';

@Component({
  imports: [NgZorroModule, RouterLink, LinkPipe],
  template: `
    @switch (view) {
      @case ('detail') {
        <nz-descriptions nzBordered nzSize="small" [nzColumn]="1">
          <nz-descriptions-item nzTitle="File name">
            <a
              [routerLink]="['/', alert.serviceId, 'billing/files'] | link"
              [queryParams]="{ vendorId: alert.vendorId, accountId: alert.accountId, period: alert.period }"
              >{{ alert.data.name }}</a
            >
          </nz-descriptions-item>
        </nz-descriptions>
      }
    }
  `,
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class BillingFileDownloadErrorAlertDetailsComponent extends AlertDetailsComponent<BillingFileDownloadErrorAlertData> {
  @Input()
  alert: Alert<BillingFileDownloadErrorAlertData>;

  @Input()
  view: AlertDetailsView;
}

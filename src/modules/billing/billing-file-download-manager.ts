import { DataSource, DataSourceCredential } from '~/common/data-sources';
import { EntityManager, create } from '~/common/database';
import { DiscoveryService } from '~/common/discovery';
import { InvalidStateError, getErrorMessage } from '~/common/errors';
import { EventBus } from '~/common/events';
import { FileStorage } from '~/common/files';
import { JobBus, Worker, everyDayAt } from '~/common/jobs';
import { Provider } from '~/common/providers';
import { groupBy } from '~/common/utils/collection';
import { pick } from '~/common/utils/object';
import { EventAlertGenerator } from '~/modules/alerts/event-alert-generator';
import { BillingFile } from './billing-file';
import { BillingFileDownload } from './billing-file-download';
import { BillingFileDownloadConfig } from './billing-file-download-config';
import {
  BillingFileDownloadErrorAlertData,
  billingFileDownloadErrorAlertType
} from './billing-file-download-error-alert-type';
import { BillingFileDownloadStatus, billingFileDownloadStatuses } from './billing-file-download-status';
import { BillingFileDownloader, BillingFileDownloaderMetadata } from './billing-file-downloader';
import { BillingFilePipeline } from './billing-file-pipeline';
import { BillingFileStatus } from './billing-file-status';
import { getBillingFileStorageBasePath } from './billing-file-storage';

interface DownloadContext extends Pick<BillingFile, 'id' | 'vendorId' | 'accountId' | 'period' | 'name'> {
  serviceId: string;
  downloaderId: string;
}

@Provider()
@EventAlertGenerator<DownloadContext, BillingFileDownloadErrorAlertData>({
  event: { name: `file:${BillingFileDownloadStatus.Error}` },
  close: { name: `file:${BillingFileDownloadStatus.Finished}` },
  alert: ({ serviceId, vendorId, accountId, period, name, id }) => ({
    serviceId,
    typeId: billingFileDownloadErrorAlertType.id,
    vendorId,
    accountId,
    period,
    message: `Failed to download file "${name}"`,
    refId: id,
    data: { id, name }
  })
})
export class BillingFileDownloadManager {
  constructor(
    private config: BillingFileDownloadConfig,
    private entityManager: EntityManager,
    private discovery: DiscoveryService,
    private fileStorage: FileStorage,
    private pipeline: BillingFilePipeline,
    private jobBus: JobBus,
    private eventBus: EventBus
  ) {}

  /**
   * Download billing files
   */
  @Worker({
    description: 'Download billing files',
    schedule: everyDayAt(3),
    fork: true
  })
  async downloadFiles(
    options: { period?: string; vendorId?: string; id?: string; force?: boolean; userId?: string } = {}
  ) {
    if (!this.config.enabled && !options.force && !options.id) {
      return;
    }

    const query = this.entityManager
      .createQueryBuilder(BillingFile, 'file')
      .innerJoinAndSelect('file.type', 'type')
      .innerJoinAndSelect('file.vendor', 'vendor')
      .leftJoinAndSelect('file.download', 'download')
      .leftJoinAndSelect('file.account', 'account')
      .innerJoinAndSelect('type.source', 'source')
      .leftJoinAndSelect('source.credentials', 'credentials')
      .addSelect('credentials.password')
      .addSelect('credentials.properties')
      .addSelect('type.password')
      .where('type.downloaderId IS NOT NULL')
      .andWhere('file.status IN (:...statuses)', { statuses: [BillingFileStatus.Expected, BillingFileStatus.Failed] });

    if (options.id) {
      query.andWhereInIds([options.id]);
    } else if (options.period) {
      query.andWhere('file.period = :period', { period: options.period });
    } else {
      // on expected date (or later) when period is not specified
      query.andWhere('file.expectedDate <= CURRENT_DATE');
    }

    if (options.vendorId) {
      query.andWhere('file.vendorId = :vendorId', { vendorId: options.vendorId });
    }

    const files = await query.getMany();
    const filesByDownloaderId = groupBy(files, (file) => file.type!.downloaderId);
    for (const [downloaderId, files] of Object.entries(filesByDownloaderId)) {
      await this.runDownloader(downloaderId, files, options.userId);
    }
  }

  private async getDownloader(downloaderId: string): Promise<BillingFileDownloader<any>> {
    const downloaders = await this.discovery.getProviders<BillingFileDownloaderMetadata>(BillingFileDownloader);
    for (const { meta, instance } of downloaders) {
      if (meta.id === downloaderId) {
        return instance;
      }
    }
    throw new Error(`Downloader "${downloaderId}" not found.`);
  }

  private async runDownloader(downloaderId: string, files: BillingFile[], userId?: string) {
    const downloader = await this.getDownloader(downloaderId);
    const instances = new Map<string, any>();
    const getInstance = async (credentials: DataSourceCredential, source: DataSource) => {
      const instance = instances.get(credentials.id);
      if (instance) {
        return instance;
      } else {
        const instance = await downloader.initialize(credentials, source);
        instances.set(credentials.id, instance);
        return instance;
      }
    };

    try {
      for (const file of files) {
        const meta: DownloadContext = {
          ...pick(file, ['id', 'vendorId', 'accountId', 'period', 'name']),
          serviceId: file.vendor!.serviceId,
          downloaderId
        };
        const storageDir = getBillingFileStorageBasePath(file.id) + '/download';
        const download =
          file.download ?? create(BillingFileDownload, { sourceType: file.type!.sourceType, fileId: file.id, userId });

        const update = async (data: Partial<BillingFileDownload>, context: Record<string, any> = {}) => {
          await this.entityManager.save(Object.assign(download, data));
          if (data.status) {
            const statusName = billingFileDownloadStatuses.get(data.status).name.toLowerCase();
            this.eventBus.info(`file:${data.status}`, `File download ${statusName}`, { ...meta, ...context });
          }
        };

        let instance: any;

        try {
          if (download.status !== BillingFileDownloadStatus.Requested) {
            await update({
              status: BillingFileDownloadStatus.Started,
              startDate: new Date(),
              finishDate: null!,
              message: null!,
              screenshotUrl: null!
            });
          }

          const source = file.type!.source!;
          const credentials = source.credentials!.find((c) => (c.accountId ? c.accountId === file.accountId : true));
          if (!credentials) {
            throw new InvalidStateError('Credentials not defined');
          }

          instance = await getInstance(credentials, source);

          // download file
          const targetDir = await this.fileStorage.createDirectory(storageDir);
          const result = await downloader.downloadFile(instance, {
            file,
            type: file.type!.refId!,
            download,
            targetDir
          });

          const { name, path, retryTimeout, message, data } = result;
          const status = result.status ?? (path ? BillingFileDownloadStatus.Finished : undefined);
          const finishDate = status === BillingFileDownloadStatus.Finished ? new Date() : undefined;
          await update({ status, finishDate, message, data }, { path });

          if (path) {
            // add to pipeline for processing when finished
            await this.pipeline.process(file.id, path, { originalName: name, password: file.type?.password });
          } else if (retryTimeout) {
            // schedule retry when requested
            const startDate = new Date(Date.now() + retryTimeout * 1000);
            await this.jobBus.addJob([BillingFileDownloadManager, 'downloadFiles'], { id: file.id }, { startDate });
            this.eventBus.info('file:scheduled', 'File download scheduled', { ...meta, status, startDate });
          }
        } catch (error) {
          await update(
            { status: BillingFileDownloadStatus.Error, message: getErrorMessage(error).substring(0, 200) },
            { error }
          );
        } finally {
          // take a screenshot when finished or failed
          if (instance && downloader.screenshot) {
            const screenshotPath = `${storageDir}/screenshot.png`;
            await downloader.screenshot(instance, this.fileStorage.getAbsolutePath(screenshotPath));
            await update({
              screenshotUrl: this.fileStorage.createUrl(screenshotPath)
            });
          }
        }
      }
    } catch (error) {
      this.eventBus.error('error', error, { downloaderId });
    } finally {
      // destroy when finished/failed
      if (downloader.destroy) {
        for (const instance of instances.values()) {
          await downloader.destroy(instance);
        }
      }
    }
  }
}

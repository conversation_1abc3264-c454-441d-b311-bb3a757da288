import { ManyToOne, Optional, PrimaryGenerated, Required, Table, type Relation } from '~/common/database/entity';
import { User } from '~/common/identity/user';
import { Job } from '~/common/jobs/job';
import { BillingFile } from './billing-file';
import { type BillingFileStageData } from './billing-file-stage-data';
import { BillingFileStageName } from './billing-file-stage-name';
import { BillingFileStageStatus } from './billing-file-stage-status';

@Table('billing_file_stages')
export class BillingFileStage {
  /**
   * Stage ID
   * @example e6bafb92-dfa3-431c-bfdc-38363c307320
   */
  @PrimaryGenerated('uuid')
  id: string;

  /**
   * File ID
   * @example 89b11058-d4fe-42d5-ba2f-b02af4ff462c
   */
  @Required()
  fileId: string;

  /**
   * File
   */
  @ManyToOne(() => BillingFile, 'fileId')
  file?: Relation<BillingFile>;

  /**
   * Stage name
   * @example archive
   */
  @Required({ enum: BillingFileStageName })
  name: BillingFileStageName;

  /**
   * Status
   * @example finished
   */
  @Required({ enum: BillingFileStageStatus })
  status: BillingFileStageStatus;

  /**
   * File path
   */
  @Optional()
  path?: string;

  /**
   * File URL
   */
  @Optional({ isVirtual: true })
  url?: string;

  /**
   * Create date
   * @example 2020-03-12T11:43:54Z
   */
  @Required()
  createdDate: Date;

  /**
   * Created user ID
   * @example danispa2
   */
  @Optional()
  createdUserId?: string;

  /**
   * Created user
   * @example danispa2
   */
  @ManyToOne(() => User, 'createdUserId')
  createdUser?: Relation<User>;

  /**
   * Start date
   * @example 2020-03-12T11:43:54Z
   */
  @Optional()
  startDate?: Date;

  /**
   * Finish date
   * @example 2020-03-12T11:44:00Z
   */
  @Optional()
  finishDate?: Date;

  /**
   * Message (eg. error message)
   */
  @Optional()
  message?: string;

  /**
   * Job ID
   * @example null
   */
  @Optional()
  jobId?: string;

  /**
   * Job
   */
  @ManyToOne(() => Job, 'jobId')
  job?: Relation<Job>;

  /**
   * Additional data (not stored in database)
   * @example { "password": "123" } for protected files
   */
  data?: BillingFileStageData;
}

import { Optional, PartialType, PickType, UnionType } from '~/common/schema';
import { BillingFileType } from './billing-file-type';
import { BillingFileTypeContact } from './billing-file-type-contact';
import { BillingFileTypeThreshold } from './billing-file-type-threshold';

class BillingFileTypeContactDto extends UnionType(
  PickType(BillingFileTypeContact, ['contactId']),
  PartialType(PickType(BillingFileTypeContact, ['id']))
) {}

export class BillingFileTypeCreateDto extends PickType(BillingFileType, [
  'vendorId',
  'name',
  'description',
  'perAccount',
  'accounts',
  'validFromPeriod',
  'validToPeriod',
  'expectedBase',
  'expectedOffset',
  'cycleStartDay',
  'cycleDuration',
  'sourceType',
  'sourceId',
  'documentType',
  'parserId',
  'parserConfig',
  'isPasswordProtected',
  'password'
]) {
  @Optional({ type: BillingFileTypeContactDto })
  contacts?: BillingFileTypeContactDto[];
}

export class BillingFileTypeUpdateDto extends PartialType(BillingFileTypeCreateDto) {
  @Optional({ type: BillingFileTypeContactDto })
  contacts?: BillingFileTypeContactDto[];
}

export class BillingFileTypeThresholdCreateDto extends PickType(BillingFileTypeThreshold, [
  'accountId',
  'dataset',
  'minRecordsCount',
  'maxRecordsCount'
]) {}

export class BillingFileTypeThresholdUpdateDto extends PickType(BillingFileTypeThreshold, [
  'minRecordsCount',
  'maxRecordsCount'
]) {}

export class BillingFileTypeParseDto {
  @Optional()
  periods?: string[];
}

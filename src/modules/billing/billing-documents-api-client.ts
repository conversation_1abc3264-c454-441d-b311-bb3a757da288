import { Injectable } from '@angular/core';
import { type ApiRequestOptions } from '~/common/api/api-client';
import { ServiceApiClient } from '~/common/services/service-api-client';
import { BillingDocument } from './billing-document';
import {
  BillingDocumentCreateDto,
  BillingDocumentDeleteDto,
  BillingDocumentParseDto,
  BillingDocumentUpdateDto
} from './billing-documents-dto';
import { BillingDocumentsQueryParams } from './billing-documents-query-params';

const baseUrl = 'billing/documents';

@Injectable({ providedIn: 'root' })
export class BillingDocumentsApiClient {
  constructor(private apiClient: ServiceApiClient) {}

  getDocuments(params: BillingDocumentsQueryParams = {}, options: ApiRequestOptions = {}) {
    return this.apiClient.get<BillingDocument[]>(baseUrl, params, {
      ...options,
      responseType: BillingDocument
    });
  }

  getDocument(id: string, params?: BillingDocumentsQueryParams) {
    return this.apiClient.get<BillingDocument>(`${baseUrl}/${id}`, params, {
      responseType: BillingDocument
    });
  }

  createDocument(data: BillingDocumentCreateDto) {
    return this.apiClient.post(baseUrl, data, {
      bodyType: BillingDocumentCreateDto,
      responseType: BillingDocument
    });
  }

  updateDocument(id: string, data: BillingDocumentUpdateDto) {
    return this.apiClient.patch(`${baseUrl}/${id}`, data, {
      bodyType: BillingDocumentUpdateDto,
      responseType: BillingDocument
    });
  }

  deleteDocument(id: string, data?: BillingDocumentDeleteDto) {
    return this.apiClient.delete(`${baseUrl}/${id}`, data);
  }

  parseDocument(data: BillingDocumentParseDto) {
    return this.apiClient.post<BillingDocument>(`${baseUrl}/parse`, data, {
      bodyType: BillingDocumentParseDto,
      responseType: BillingDocument
    });
  }
}

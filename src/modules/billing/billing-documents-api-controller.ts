import { Authorize, CurrentUser, User, Uuid<PERSON>aram, getCollection } from '~/common/api';
import { Body, Delete, Get, Patch, Post, Query } from '~/common/controllers';
import { ExchangeRate } from '~/common/currencies';
import { ExchangeRateType } from '~/common/currencies/exchange-rate-type';
import { EntityManager, EntityOperation, EntityTrigger } from '~/common/database';
import { InvalidArgumentError, NotFoundError } from '~/common/errors';
import { formatPeriod, getPeriodByCycleStart } from '~/common/periods/period-utils';
import { ServiceApiController, ServiceId } from '~/common/services';
import { ServiceSettings } from '~/common/services/service-settings';
import { UploadStorage } from '~/common/uploads';
import { omit, pick } from '~/common/utils/object';
import { Vendor, VendorRead } from '~/common/vendors';
import { BillingDocument } from './billing-document';
import { BillingDocumentItem } from './billing-document-item';
import { BillingDocumentParser } from './billing-document-parser';
import { BillingDocumentRead, BillingDocumentWrite } from './billing-document-permissions';
import {
  BillingDocumentCreateDto,
  BillingDocumentDeleteDto,
  BillingDocumentParseDto,
  BillingDocumentUpdateDto
} from './billing-documents-dto';
import { BillingDocumentsQueryParams } from './billing-documents-query-params';
import { BillingFile } from './billing-file';
import { BillingFileGenerator } from './billing-file-generator';
import { BillingFilePipeline } from './billing-file-pipeline';
import { canResetBillingFile } from './billing-file-utils';

@ServiceApiController('billing/documents')
export class BillingDocumentsApiController {
  constructor(
    private entityManager: EntityManager,
    private uploadStorage: UploadStorage,
    private pipeline: BillingFilePipeline,
    private generator: BillingFileGenerator,
    private parser: BillingDocumentParser
  ) {}
  /**
   * Return documents
   */
  @Get()
  @Authorize(BillingDocumentRead)
  async getDocuments(
    @ServiceId() serviceId: string,
    @Query() params: BillingDocumentsQueryParams = {},
    @CurrentUser() user: User
  ) {
    const settings = await this.entityManager.findOneByOrFail(ServiceSettings, { serviceId });
    const exchangeRateType = settings.value.exchangeRateType ?? ExchangeRateType.Daily;
    const query = this.entityManager
      .createQueryBuilder(BillingDocument, 'document')
      .innerJoinAndSelect('document.vendor', 'vendor')
      .leftJoinAndSelect('document.account', 'account')
      .leftJoinAndSelect('vendor.country', 'country')
      .leftJoin(
        ExchangeRate,
        'rate',
        `rate.currency = document.currency AND rate.type = :exchangeRateType${
          exchangeRateType === ExchangeRateType.Daily ? ' AND rate.date = document.issueDate' : ''
        }`,
        { exchangeRateType }
      )
      .addSelect('rate.rate', 'usdRate')
      .addSelect('document.netAmount / rate.rate', 'netAmountUsd')
      .addSelect('document.totalAmount / rate.rate', 'totalAmountUsd')
      .where('vendor.serviceId = :serviceId', { serviceId })
      .orderBy('document.period', 'DESC')
      .addOrderBy('vendor.countryId')
      .addOrderBy('vendor.name')
      .addOrderBy('document.issueDate', 'DESC')
      .addOrderBy('document.refId', 'DESC');

    return getCollection(query, params, {
      search: ['refId', 'poRefId', 'vendor.name', 'account.name', 'account.refId', 'country.name'],
      filter: {
        fromExpected: (value) => {
          const subQuery = query
            .subQuery()
            .from(BillingFile, 'file')
            .where('file.documentId = document.id')
            .andWhere('file.typeId IS NOT NULL');
          query.andWhere(`${value ? '' : 'NOT '}EXISTS ${subQuery.getQuery()}`);
        }
      },
      load: {
        items: async (documents) => {
          const items = await this.entityManager
            .createQueryBuilder(BillingDocumentItem, 'item')
            .where('item.documentId IN (:...documentIds)', { documentIds: documents.map(({ id }) => id) })
            .orderBy('item.position')
            .getMany();

          for (const document of documents) {
            document.items = items.filter((item) => item.documentId === document.id);
          }
        },
        files: async (documents) => {
          const files = await this.entityManager
            .createQueryBuilder(BillingFile, 'file')
            .where('file.documentId IN (:...documentIds)', { documentIds: documents.map(({ id }) => id) })
            .orderBy('file.createdDate')
            .getMany();

          for (const document of documents) {
            document.files = files.filter((file) => file.documentId === document.id);
          }
        }
      },
      sort: {
        vendor: (direction) => {
          query.addOrderBy('vendor.countryId', direction);
          query.addOrderBy('vendor.name', direction);
        }
      },
      ability: {
        user,
        permission: VendorRead,
        conditions: {
          serviceId: 'vendor.serviceId = :serviceId',
          countryId: 'vendor.countryId = :countryId'
        }
      }
    });
  }

  /**
   * Create new document
   */
  @Post()
  @Authorize(BillingDocumentWrite)
  async createDocument(@Body() data: BillingDocumentCreateDto): Promise<BillingDocument> {
    return this.saveDocument(new BillingDocument(), data);
  }

  /**
   * Parse a document from upload or reparse existing file document
   */
  @Post('parse')
  @Authorize(BillingDocumentWrite)
  async parseDocument(@Body() { vendorId, uploadId, fileId, config }: BillingDocumentParseDto) {
    if (uploadId) {
      return this.parser.createDocument(vendorId, this.uploadStorage.getAbsolutePath(uploadId), {
        filename: this.uploadStorage.getFileMetadata(uploadId).name,
        config
      });
    } else if (fileId) {
      return this.parser.parseFileDocument(fileId, { config });
    } else {
      throw new InvalidArgumentError('Missing upload ID or file ID');
    }
  }

  /**
   * Return document by ID
   */
  @Get(':documentId')
  @Authorize(BillingDocumentRead)
  async getDocument(
    @ServiceId() serviceId: string,
    @UuidParam('documentId') documentId: string,
    @Query() params: BillingDocumentsQueryParams = {},
    @CurrentUser() user: User
  ): Promise<BillingDocument> {
    const [document] = await this.getDocuments(serviceId, { ...params, id: documentId }, user);
    if (document) {
      return document;
    } else {
      throw new NotFoundError('Billing document not found');
    }
  }

  /**
   * Update document
   */
  @Patch(':documentId')
  @Authorize(BillingDocumentWrite)
  async updateDocument(
    @ServiceId() serviceId: string,
    @UuidParam('documentId') documentId: string,
    @Body() data: Partial<BillingDocument>,
    @CurrentUser() user: User
  ): Promise<BillingDocument> {
    const document = await this.getDocument(serviceId, documentId, { expand: ['files'] }, user);
    return this.saveDocument(document, data);
  }

  /**
   * Delete document
   */
  @Delete(':documentId')
  @Authorize(BillingDocumentWrite)
  async deleteDocument(
    @ServiceId() serviceId: string,
    @UuidParam('documentId') documentId: string,
    @Body() data: BillingDocumentDeleteDto,
    @CurrentUser() user: User
  ): Promise<void> {
    const document = await this.getDocument(serviceId, documentId, { expand: ['files'] }, user);
    const files = document.files ?? [];
    await this.entityManager.transaction(async (entityManager) => {
      if (data.withFiles) {
        await Promise.all(files.map((file) => entityManager.remove(file)));
      }
      await entityManager.remove(document);
    });
  }

  @EntityTrigger({
    after: [EntityOperation.Delete],
    entity: BillingFile,
    toArgs: ({ entity: file }) => [file]
  })
  async onDocumentFileDelete(file: BillingFile) {
    // regenerate expected document files
    if (file.documentId && canResetBillingFile(file)) {
      await this.generator.generateFiles({ period: file.period, typeId: file.typeId });
    }
  }

  /**
   * Save document with provided data
   * @param document
   * @param data
   * @todo Run in transaction (pipeline has to be refactored to get transaction entity manager instance)
   */
  private async saveDocument(document: BillingDocument, data: BillingDocumentUpdateDto): Promise<BillingDocument> {
    // document
    Object.assign(document, omit(data, ['files']));
    delete document.vendor;
    delete document.account;

    // filter "N/A" values
    if (document.refId && ['n/a', 'na', ''].includes(document.refId.toLowerCase())) {
      document.refId = null!;
    }

    // check unique document ID
    if (document.refId) {
      const existing = await this.entityManager.findOneBy(BillingDocument, {
        vendorId: document.vendorId,
        refId: document.refId
      });
      if (existing && existing.id !== document.id) {
        throw new InvalidArgumentError(
          `Billing document "${document.refId}" already exists in period "${formatPeriod(existing.period)}"`
        );
      }
    }

    const vendor = await this.entityManager.findOneByOrFail(Vendor, { id: document.vendorId });
    document.period = getPeriodByCycleStart(new Date(document.cycleStartDate), vendor.periodThreshold);

    await this.entityManager.save(document);

    // files
    if (data.files) {
      for (const dto of data.files) {
        const file = dto.id ? await this.entityManager.findOneBy(BillingFile, { id: dto.id }) : new BillingFile();
        if (file) {
          Object.assign(file, omit(dto, ['uploadId']));
          Object.assign(file, pick(document, ['vendorId', 'accountId', 'cycleStartDate', 'cycleEndDate']));
          file.period = document.period;
          file.documentId = document.id;
          await this.entityManager.save(file);
        } else {
          throw new InvalidArgumentError('Invalid file ID');
        }

        if (dto.uploadId) {
          await this.pipeline.upload(file.id, dto.uploadId, { originalName: file.name });
        }
      }

      if (document.files) {
        for (const file of document.files) {
          const exists = data.files.some(({ id }) => id === file.id);
          if (!exists) {
            await this.entityManager.remove(file);
          }
        }
      }
    }

    return document;
  }
}

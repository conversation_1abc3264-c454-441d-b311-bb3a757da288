import { Component, Inject } from '@angular/core';
import { ReactiveFormsModule, UntypedFormControl, UntypedFormGroup, Validators } from '@angular/forms';
import { NZ_MODAL_DATA, NzModalRef } from 'ng-zorro-antd/modal';
import { PeriodSelectComponent } from '~/common/periods/period-select-component';
import { Action, ActionButtonComponent } from '~/common/ui/actions';
import { formValid$ } from '~/common/ui/forms';
import { NgZorroModule } from '~/common/ui/ng-zorro';
import { VendorSelectComponent } from '~/common/vendors/vendor-select-component';
import { BillingFilesApiClient } from './billing-files-api-client';

export interface BillingFilesGenerateFormData {
  period?: string;
  vendorId?: string;
}

@Component({
  selector: 'billing-files-generate-form',
  imports: [ReactiveFormsModule, NgZorroModule, ActionButtonComponent, PeriodSelectComponent, VendorSelectComponent],
  templateUrl: './billing-files-generate-form-component.html'
})
export class BillingFilesGenerateFormComponent {
  form = new UntypedFormGroup({
    period: new UntypedFormControl(null, [Validators.required]),
    vendorId: new UntypedFormControl()
  });

  constructor(
    private modalRef: NzModalRef,
    @Inject(NZ_MODAL_DATA) modalData: BillingFilesGenerateFormData,
    private filesRepository: BillingFilesApiClient
  ) {
    this.form.patchValue(modalData);
  }

  submitAction: Action = {
    label: 'Submit',
    type: 'primary',
    icon: 'save',
    enabled: formValid$(this.form),
    execute: () => {
      const { period, vendorId } = this.form.value;
      return this.filesRepository.generateFiles(period, vendorId);
    },
    success: () => ({
      execute: () => {
        this.modalRef.close(true);
      }
    })
  };
}

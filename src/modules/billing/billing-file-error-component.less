@import 'theme.less';

section {
  margin-bottom: 1.4rem;

  &:last-child {
    margin-bottom: 0;
  }

  > strong {
    display: block;
    margin-bottom: 0.2rem;
  }
}

.hints {
  ul {
    padding-left: 1.6rem;
    margin: 0;
  }

  ::ng-deep {
    pre {
      font-size: 0.8rem;
      max-height: 300px;
      overflow: auto;
    }
  }
}

.details ::ng-deep {
  .ant-descriptions-item-label,
  .ant-descriptions-item-content {
    font-size: 0.8rem;
    padding: 0.3rem 0.5rem !important;
    vertical-align: top;
  }
  .ant-descriptions-item-label {
    white-space: nowrap;
    max-width: 30%;
  }

  .row-data {
    max-height: 150px;
    overflow: auto;

    tr.highlight {
      td {
        background-color: @alert-warning-bg-color;
      }
    }

    td.field {
      width: 45%;
    }
  }
}

import { ManyToOne, Optional, PrimaryGenerated, Required, Table, type Relation } from '~/common/database/entity';
import { BillingDocument } from './billing-document';

@Table('billing_document_items', { events: true })
export class BillingDocumentItem {
  /**
   * Item ID
   * @example fa6796ce-1451-4aba-8e52-6a71307597a2
   */
  @PrimaryGenerated('uuid')
  id: string;

  /**
   * Document ID
   * @example 9e054556-73d7-434f-aad1-b2a4866ebb20
   */
  @Required()
  documentId?: string;

  /**
   * Document
   */
  @ManyToOne(() => BillingDocument, 'documentId')
  document?: Relation<BillingDocument>;

  /**
   * Description
   */
  @Optional()
  description: string;

  /**
   * Quantity
   * @example 10
   */
  @Optional()
  quantity?: number;

  /**
   * Unit name
   */
  @Optional()
  unitName?: string;

  /**
   * Unit price
   */
  @Optional()
  unitPrice?: number;

  /**
   * Net amount
   * @example 100000
   */
  @Optional()
  netAmount?: number;

  /**
   * Tax amount
   * @example 21000
   */
  @Optional()
  taxAmount?: number;

  /**
   * Total amount
   * @example 122000
   */
  @Optional()
  totalAmount?: number;

  /**
   * Position
   */
  @Required()
  position: number;
}

import { InvalidArgumentError } from '~/common/errors';
import { formatPeriod } from '~/common/periods/period-utils';
import { inDateRange } from '~/common/utils/date';
import { placeholderTextTransformer, replacePlaceholders } from '~/common/utils/template';
import { BillingFile } from './billing-file';
import { BillingFileStatus } from './billing-file-status';

/**
 * Is billing file expected?
 */
export function isExpectedBillingFile(file: BillingFile) {
  return Boolean(file.typeId);
}

/**
 * Can billing file be deleted?
 */
export function canDeleteBillingFile(file: BillingFile) {
  // only awaiting expected files or custom files
  return isExpectedBillingFile(file) ? file.status === BillingFileStatus.Expected : true;
}

/**
 * Can billing file be reset to "Expected" state (imported data removed)
 */
export function canResetBillingFile(file: BillingFile) {
  // only processed/failed expected file
  return isExpectedBillingFile(file)
    ? file.status === BillingFileStatus.Processed || file.status === BillingFileStatus.Failed
    : false;
}

/**
 * Can billing file be uploaded?
 */
export function canUploadBillingFile(file: BillingFile) {
  if (file.id) {
    // failed (reupload) or not processed yet (but expected)
    return file.status === BillingFileStatus.Failed || file.status === BillingFileStatus.Expected;
  } else {
    // allow new files
    return true;
  }
}

/**
 * Create billing file name from mask by replacing placeholder with provided context (period, account ID)
 * @example vfV2InvDIR2_{ACCOUNT_ID}_XXXXXXXXXX_{PERIOD:YYYYMM}.xml
 */
export function createBillingFileName(
  mask: string,
  context: { period?: string; account?: { refId?: string; name?: string } }
): string {
  const { period, account } = context;
  return replacePlaceholders(
    mask,
    {
      PERIOD: period,
      ACCOUNT_ID: account?.refId,
      ACCOUNT_NAME: account?.name,
      /** @deprecated */
      ACCOUNT: account?.refId
    },
    {
      PERIOD: (period, [format], options) => formatPeriod(period, format, options),
      ACCOUNT_ID: placeholderTextTransformer,
      ACCOUNT_NAME: placeholderTextTransformer,
      /** @deprecated */
      ACCOUNT: placeholderTextTransformer
    }
  );
}

/**
 * Is date in billing file cycle range?
 * @param date
 * @param file
 * @returns
 */
export function inBillingFileCycle(date: Date, file: BillingFile) {
  if (!file.cycleStartDate) {
    throw new InvalidArgumentError('Billing file cycle start not defined');
  }
  if (!file.cycleEndDate) {
    throw new InvalidArgumentError('Billing file cycle end not defined');
  }
  return inDateRange(date, file.cycleStartDate, file.cycleEndDate);
}

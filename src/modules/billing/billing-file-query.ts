import { EntityManager, SelectQueryBuilder } from '~/common/database';
import { User, abilityToQuery, checkAbility } from '~/common/identity';
import { VendorRead } from '~/common/vendors';
import { BillingFile } from './billing-file';
import { BillingFileRead } from './billing-file-permissions';

/**
 * Create vendor query by user ability
 */
export function createBillingFileQuery(
  entityManager: EntityManager,
  options: { serviceId?: string; user?: User } = {}
): SelectQueryBuilder<BillingFile> {
  const query = entityManager
    .createQueryBuilder(BillingFile, 'file')
    .innerJoinAndSelect('file.vendor', 'vendor')
    .leftJoinAndSelect('file.account', 'account')
    .leftJoinAndSelect('vendor.country', 'country');

  const { serviceId, user } = options;

  if (serviceId) {
    query.where('vendor.serviceId = :serviceId', { serviceId });
  }

  const ability = user?.ability;
  if (ability && checkAbility(ability, BillingFileRead)) {
    // apply user ability
    abilityToQuery(query, ability, VendorRead, {
      serviceId: 'vendor.serviceId = :serviceId',
      countryId: 'vendor.countryId = :countryId'
    });
  } else {
    query.where('1=0');
  }

  return query;
}

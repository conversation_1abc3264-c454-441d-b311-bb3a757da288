import { SetMetadata } from '@nestjs/common';
import { DataSource, DataSourceCredential } from '~/common/data-sources';
import { PlaywrightInstance, PlaywrightLaunchOptions, PlaywrightManager } from '~/common/playwright';
import { Provider } from '~/common/providers';
import { type Promisable } from '~/common/utils/promise';
import { BillingFileDownloadContext } from './billing-file-download-context';
import { BillingFileDownloadResult } from './billing-file-download-result';

export interface BillingFileDownloaderMetadata {
  id: string;
  perAccount?: boolean;
}

export function BillingFileDownloader(id: string, options: Omit<BillingFileDownloaderMetadata, 'id'> = {}) {
  return SetMetadata(BillingFileDownloader, { id, ...options });
}

export interface BillingFileDownloader<Instance> {
  initialize(credentials: DataSourceCredential, source: DataSource): Promisable<Instance>;
  downloadFile(instance: Instance, context: BillingFileDownloadContext): Promise<BillingFileDownloadResult>;
  screenshot?(instance: Instance, path: string): Promise<void>;
  destroy?(instance: Instance): Promise<void>;
}

@Provider()
export abstract class BillingFilePortalDownloader implements BillingFileDownloader<PlaywrightInstance> {
  constructor(protected playwrightManager: PlaywrightManager) {}

  protected launch(options: PlaywrightLaunchOptions = {}) {
    return this.playwrightManager.launch(options);
  }

  abstract initialize(credentials: DataSourceCredential, source: DataSource): Promisable<PlaywrightInstance>;

  abstract downloadFile(
    playwright: PlaywrightInstance,
    context: BillingFileDownloadContext
  ): Promise<BillingFileDownloadResult>;

  async takeScreenshot(playwright: PlaywrightInstance, path: string) {
    await playwright.screenshot(path);
  }

  async destroy(playwright: PlaywrightInstance) {
    await playwright.destroy();
  }
}

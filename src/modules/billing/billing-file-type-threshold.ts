import { ManyToOne, Optional, PrimaryGenerated, Required, Table, type Relation } from '~/common/database/entity';
import { VendorAccount } from '~/common/vendors/vendor-account';
import { BillingFileType } from './billing-file-type';

@Table('billing_file_type_thresholds', { history: true, events: true })
export class BillingFileTypeThreshold {
  /**
   * Threshold ID
   * @example 0c7618e7-b18c-45b9-a53e-39c68780c0f6
   */
  @PrimaryGenerated()
  id: string;

  /**
   * File type ID
   * @example 3e85be98-c987-40e2-882a-ab69739420c6
   */
  @Required()
  typeId: string;

  /**
   * File type
   */
  @ManyToOne(() => BillingFileType, 'typeId')
  type?: Relation<BillingFileType>;

  /**
   * Account ID
   * @example dca70d90-1f38-498d-bebf-dfc629bc0e8e
   */
  @Optional()
  accountId?: string;

  /**
   * Account
   */
  @ManyToOne(() => VendorAccount, 'accountId')
  account?: Relation<VendorAccount>;

  /**
   * Dataset code
   * @example usage
   */
  @Required()
  dataset: string;

  /**
   * Minimum records count
   * @example 10000
   */
  @Optional()
  minRecordsCount?: number;

  /**
   * Maximum records count
   * @example 12000
   */
  @Optional()
  maxRecordsCount?: number;
}

import { CommonModule } from '@angular/common';
import { Component, Inject } from '@angular/core';
import { FormArray, FormControl, ReactiveFormsModule, UntypedFormControl, UntypedFormGroup } from '@angular/forms';
import { NZ_MODAL_DATA } from 'ng-zorro-antd/modal';
import { CurrencySelectComponent } from '~/common/currencies/currency-select-component';
import { ListSelectComponent } from '~/common/lists/list-select-component';
import { ActionButtonComponent } from '~/common/ui/actions';
import { HelpIconComponent } from '~/common/ui/components/help-icon';
import { DrawerService } from '~/common/ui/drawer';
import { formControlValue$ } from '~/common/ui/forms';
import { NgZorroModule } from '~/common/ui/ng-zorro';
import { UploadButtonComponent } from '~/common/uploads/upload-button-component';
import { Vendor } from '~/common/vendors/vendor';
import { openBillingDocument } from './billing-document-detail-component';
import { billingDocumentParserFields } from './billing-document-parser-config';

export function createBillingDocumentParserForm() {
  return new UntypedFormGroup({
    items: new FormControl(false),
    pages: new FormControl(),
    currency: new FormControl(),
    fields: new UntypedFormGroup(
      billingDocumentParserFields
        .ids()
        .reduce((controls, field) => ({ ...controls, [field]: new UntypedFormControl() }), {})
    )
  });
}

export interface BillingDocumentParserFormData {
  control: ReturnType<typeof createBillingDocumentParserForm>;
  vendor: Vendor;
}

@Component({
  imports: [
    CommonModule,
    ReactiveFormsModule,
    NgZorroModule,
    ActionButtonComponent,
    CurrencySelectComponent,
    HelpIconComponent,
    ListSelectComponent,
    UploadButtonComponent
  ],
  templateUrl: './billing-document-parser-form-component.html',
  styleUrl: './billing-document-parser-form-component.less'
})
export class BillingDocumentParserFormComponent {
  fields = billingDocumentParserFields;
  fieldControls = new FormArray<FormControl>([]);

  get control() {
    return this.data.control;
  }

  constructor(
    @Inject(NZ_MODAL_DATA) private data: BillingDocumentParserFormData,
    private drawerService: DrawerService
  ) {
    formControlValue$(this.control.controls.fields).subscribe((fields) => {
      const added = this.fieldControls.value;
      for (const [field, value] of Object.entries(fields)) {
        if (value && !added.includes(field)) {
          this.addField(field);
        }
      }
    });
  }

  addField(name?: string) {
    this.fieldControls.push(new FormControl(name));
  }

  removeField(index: number) {
    const { value: name } = this.fieldControls.at(index);
    this.fieldControls.removeAt(index);
    if (name) {
      this.control.controls.fields?.get(name)?.setValue(null);
    }
  }

  parseUpload(uploadId: string) {
    openBillingDocument(this.drawerService, {
      parse: {
        vendorId: this.control.parent!.value.vendorId,
        uploadId,
        config: this.control.value
      }
    });
  }
}

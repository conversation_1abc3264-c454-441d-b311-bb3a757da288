@let parsing = (loader.load$ | async)?.value?.parse ? true : false;
<div [loader]="loader" [loadingMessage]="parsing ? 'Parsing document ...' : undefined">
  <ng-template let-document>
    <section>
      <nz-descriptions nzBordered nzSize="small" [nzColumn]="1">
        @if (document.vendor) {
          <nz-descriptions-item nzTitle="Vendor">
            <vendor-name [vendor]="document.vendor" />
          </nz-descriptions-item>
        }
        @if (document.account) {
          <nz-descriptions-item nzTitle="Account">
            <vendor-account-name [account]="document.account" />
          </nz-descriptions-item>
        }
        @if (document.refId) {
          <nz-descriptions-item nzTitle="Document ID">
            <strong>{{ document.refId }}</strong>
          </nz-descriptions-item>
        }
        @if (document.poRefId) {
          <nz-descriptions-item nzTitle="PO number">
            {{ document.poRefId }}
          </nz-descriptions-item>
        }
        <nz-descriptions-item nzTitle="Period">
          <period-name [period]="document.period" />
        </nz-descriptions-item>
        <nz-descriptions-item nzTitle="Billing cycle">
          {{ document | cycle }}
        </nz-descriptions-item>
        @if (document.issueDate) {
          <nz-descriptions-item nzTitle="Issue date">
            {{ document.issueDate | date }}
          </nz-descriptions-item>
        }
        @if (document.connectionsCount) {
          <nz-descriptions-item nzTitle="Number of connections">
            {{ document.connectionsCount | number }}
          </nz-descriptions-item>
        }
      </nz-descriptions>
    </section>

    <section>
      <h3>Charges</h3>
      @for (amount of amounts; track amount) {
        @if ((document[amount.field] | typeof) === 'number') {
          <div nz-row class="py-1">
            <div nz-col nz-typography [nzType]="amount.total ? undefined : 'secondary'" class="amount-label m-0">
              {{ amount.label }}
            </div>
            <div nz-col nzFlex="auto" class="text-right" [class.text-bold]="amount.total">
              {{ document[amount.field] ?? 0 | currency: document.currency : '' }} {{ document.currency }}
            </div>
          </div>
        }
      }

      @if (document.items?.length) {
        <div class="mt-2">
          <a (click)="items$.next(!items$.value)">
            <span nz-icon [nzType]="items$.value ? 'caret-down' : 'caret-right'"></span>
            {{ items$.value ? 'Hide' : 'Show' }} items
          </a>
        </div>

        @if (items$ | async) {
          <billing-document-items [items]="document.items" [currency]="document.currency" />
        }
      }
    </section>

    @if (document.files?.length) {
      <section>
        <h3>Files</h3>
        @for (file of document.files; track file.name) {
          <div class="p-1">
            <file-name [name]="file.name" [description]="file.description" [url]="file.url" />
          </div>
        }
      </section>
    }

    @if (fields$ | async; as fields) {
      <section style="position: relative">
        <a
          href="https://github.com/Azure-Samples/document-intelligence-code-samples/blob/main/schema/2024-11-30-ga/invoice.md#supported-document-fields"
          nz-tooltip
          nzTooltipTitle="Open parser schema documentation with all supported fields"
          target="_blank"
          style="position: absolute; top: 1.2rem; right: 0"
        >
          <span nz-icon nzType="profile"></span> Schema
        </a>
        <h3>Fields</h3>
        <nz-collapse>
          @for (field of fields | entries; track field.key) {
            <nz-collapse-panel [nzHeader]="field.key">
              <pre class="m-0">{{ field.value | json }}</pre>
            </nz-collapse-panel>
          }
        </nz-collapse>
      </section>
    }
  </ng-template>
</div>

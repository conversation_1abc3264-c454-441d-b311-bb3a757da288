import { CollectionQueryType } from '~/common/api/collection-query-type';
import { Optional } from '~/common/schema/property';
import { BillingFile } from './billing-file';

export class BillingFilesQueryParams extends CollectionQueryType(BillingFile, [
  'id',
  'vendorId',
  'accountId',
  'typeId',
  'period',
  'status',
  'cycleStartDate',
  'cycleEndDate'
]) {
  /**
   * File is overdue = current date missed expected date
   * @example true
   */
  @Optional()
  isOverdue?: boolean;

  /**
   * File is expected = has defined file type
   * @example true
   */
  @Optional()
  isExpected?: boolean;

  /**
   * File with download - downloaded or going to be downloaded
   * @example true
   */
  @Optional()
  hasDownload?: boolean;

  /**
   * Period range [from, to]
   */
  @Optional()
  periods?: string[];
}

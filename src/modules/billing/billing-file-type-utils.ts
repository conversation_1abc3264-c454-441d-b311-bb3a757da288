import { Cycle } from '~/common/periods/cycle';
import { getEntityPeriodCycle, isEntityValidInPeriod } from '~/common/periods/period-utils';
import { addDays } from '~/common/utils/date';
import { BillingFileType } from './billing-file-type';

/**
 * Is file type valid in a given period?
 */
export const isBillingFileTypeValid = isEntityValidInPeriod<BillingFileType>;

/**
 * Return billing cycle of a file type for a given period
 */
export const getBillingFileTypeCycle = getEntityPeriodCycle<BillingFileType>;

/**
 * Return expected date of a file type for a given cycle
 */
export function getBillingFileTypeExpectedDate(fileType: BillingFileType, cycle: Cycle): Date | undefined {
  return addDays(
    new Date(fileType.expectedBase === 'start' ? cycle.startDate : cycle.endDate),
    fileType.expectedOffset
  );
}

/**
 * Is file type with data?
 */
export function isDataBillingFileType(fileType: BillingFileType) {
  // must have a parser and is not a document
  return Boolean(fileType.parserId && !fileType.documentType);
}

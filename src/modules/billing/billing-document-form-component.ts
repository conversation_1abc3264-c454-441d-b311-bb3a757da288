import { Component } from '@angular/core';
import { FormArray, FormGroup, ReactiveFormsModule, UntypedFormControl, Validators } from '@angular/forms';
import { ActivatedRoute } from '@angular/router';
import { map, of } from 'rxjs';
import { CurrencySelectComponent } from '~/common/currencies/currency-select-component';
import { create } from '~/common/database/entity';
import { CurrentService } from '~/common/services/current-service';
import { Action, ActionButtonsComponent } from '~/common/ui/actions';
import { formControlValue$, markFormAsDirty, toggleFormControl } from '~/common/ui/forms';
import { untilDestroyed } from '~/common/ui/hooks';
import { LoaderComponent, createLoaderFrom } from '~/common/ui/loader';
import { NgZorroModule } from '~/common/ui/ng-zorro';
import { PageHeaderComponent } from '~/common/ui/page';
import { Vendor } from '~/common/vendors/vendor';
import { VendorAccountSelectComponent } from '~/common/vendors/vendor-account-select-component';
import { VendorSelectComponent } from '~/common/vendors/vendor-select-component';
import { BillingDocument } from './billing-document';
import { BillingDocumentFilesFormComponent } from './billing-document-files-form-component';
import { billingDocumentTypes } from './billing-document-type';
import { BillingDocumentsApiClient } from './billing-documents-api-client';
import { BillingFilesApiClient } from './billing-files-api-client';

@Component({
  selector: 'billing-document-form',
  imports: [
    ReactiveFormsModule,
    NgZorroModule,
    ActionButtonsComponent,
    CurrencySelectComponent,
    LoaderComponent,
    PageHeaderComponent,
    VendorAccountSelectComponent,
    VendorSelectComponent,
    BillingDocumentFilesFormComponent
  ],
  templateUrl: './billing-document-form-component.html'
})
export class BillingDocumentFormComponent {
  private settings = this.service.settings?.billingDocuments;

  loader = createLoaderFrom(
    this.route.params,
    ({ documentId }) => {
      if (documentId) {
        return this.documentsApiClient.getDocument(documentId, { expand: ['files'] });
      }

      // document by existing file
      const fileId = this.getFileId();
      if (fileId) {
        return this.filesRepository.getFile(fileId, { expand: ['type'] }).pipe(
          map((file) =>
            create(BillingDocument, {
              vendorId: file.vendorId,
              accountId: file.accountId,
              cycleStartDate: file.cycleStartDate,
              cycleEndDate: file.cycleEndDate,
              type: file.type?.documentType,
              files: [file]
            })
          )
        );
      }

      const document = create(BillingDocument, {
        ...this.route.snapshot.queryParams,
        files: []
      });
      return of(document);
    },
    {
      onData: (document) => {
        this.form.patchValue({
          ...document,
          cycle: document.cycleStartDate ? [document.cycleStartDate, document.cycleEndDate] : null
        });
      }
    }
  );

  form = new FormGroup({
    id: new UntypedFormControl(),
    vendorId: new UntypedFormControl(null, [Validators.required]),
    accountId: new UntypedFormControl(),
    type: new UntypedFormControl(billingDocumentTypes.items[0].id),
    refId: new UntypedFormControl(),
    poRefId: new UntypedFormControl(),
    issueDate: new UntypedFormControl(null, [Validators.required]),
    cycle: new UntypedFormControl(null, [Validators.required]),
    connectionsCount: new UntypedFormControl(),
    currency: new UntypedFormControl(null, [Validators.required]),
    netAmount: new UntypedFormControl(),
    taxAmount: new UntypedFormControl(),
    otherAmount: new UntypedFormControl(),
    totalAmount: new UntypedFormControl(),
    files: new FormArray<FormGroup>([])
  });

  types = billingDocumentTypes;

  actions: Action[] = [
    {
      label: 'Save',
      icon: 'check',
      type: 'primary',
      before: () => {
        markFormAsDirty(this.form);
        return this.form.valid;
      },
      execute: () => this.saveDocument(),
      success: () => ({
        message: 'Document saved',
        navigate: {
          path: ['@documents']
        }
      })
    },
    {
      label: 'Cancel',
      icon: 'close',
      navigate: () => ({
        path: ['@documents']
      })
    }
  ];

  private untilDestroyed = untilDestroyed();

  constructor(
    private route: ActivatedRoute,
    private service: CurrentService,
    private documentsApiClient: BillingDocumentsApiClient,
    private filesRepository: BillingFilesApiClient
  ) {
    // enable connections count only for allowed type
    this.untilDestroyed(formControlValue$(this.form.controls.type)).subscribe((type) => {
      const withConnections = Boolean(
        this.settings?.withConnections && type && billingDocumentTypes.get(type).withConnections
      );
      toggleFormControl(this.form.controls.connectionsCount, withConnections);
    });
  }

  private saveDocument() {
    const values = this.form.value;
    const data = {
      ...values,
      cycleStartDate: values.cycle[0],
      cycleEndDate: values.cycle[1]
    };

    if (values.id) {
      return this.documentsApiClient.updateDocument(values.id, data);
    } else {
      return this.documentsApiClient.createDocument(data as any);
    }
  }

  onVendorChange(vendor: Vendor | undefined, document: BillingDocument) {
    if (!document.id) {
      // prefill currency with vendor country currency for new document
      this.form.patchValue({ currency: vendor?.country?.currency });
    }
  }

  getFileId(): string {
    return this.route.snapshot.queryParams.fileId;
  }
}

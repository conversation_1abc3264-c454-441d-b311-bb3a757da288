<span [innerHTML]="formatMessage() | html"></span>
<p class="mt-1"><a nz-popover [nzPopoverContent]="details" nzPopoverPlacement="left">Show error details</a></p>
<ng-template #details>
  <nz-alert nzType="error" [nzMessage]="message" class="mb-3">
    <ng-template #message>
      <p [innerHTML]="formatMessage() | html"></p>
    </ng-template>
  </nz-alert>

  @if (getHints(); as hints) {
    <section class="hints">
      <strong>Troubleshooting</strong>
      @if (hints.length > 1) {
        <ul>
          @for (hint of hints; track hint) {
            <li [innerHTML]="formatMessage(hint) | html"></li>
          }
        </ul>
      } @else {
        <p [innerHTML]="formatMessage(hints[0]) | html"></p>
      }

      @if (getActions(); as actions) {
        <action-buttons [actions]="actions" [context]="file" [menu]="false" size="small" class="d-block mt-2" />
      }
    </section>
  }

  @if (getData(); as data) {
    <section class="details">
      <strong>Details</strong>
      <nz-descriptions nzBordered nzSize="small" [nzColumn]="1">
        @if (data.file) {
          <nz-descriptions-item nzTitle="Uploaded file">
            <div class="d-flex justify-content-between align-items-start">
              <file-name [name]="data.file" />
              <span
                nz-icon
                nzType="download"
                (click)="downloadFile()"
                nz-tooltip
                nzTooltipTitle="Download file"
                class="cursor-pointer ml-2"
              ></span>
            </div>
          </nz-descriptions-item>
        }
        @if (data.sheet) {
          <nz-descriptions-item nzTitle="Sheet name">
            {{ data.sheet }}
          </nz-descriptions-item>
        }
        @if (data.row) {
          <nz-descriptions-item nzTitle="Row number">
            {{ data.row }}
          </nz-descriptions-item>
        }
        @if (data.data; as row) {
          <nz-descriptions-item nzTitle="Row data">
            <div class="row-data" [scrollInto]="data.field ? getFieldRef(data.field) : false">
              <nz-table [nzFrontPagination]="false" [nzShowPagination]="false" nzTemplateMode class="size-xs">
                <tbody>
                  @for (entry of row | entries; track entry) {
                    <tr [class.highlight]="data.field === entry.key" [attr.data-field]="entry.key">
                      <td class="field">{{ entry.key }}</td>
                      <td class="value">{{ entry.value }}</td>
                    </tr>
                  }
                </tbody>
              </nz-table>
            </div>
          </nz-descriptions-item>
        }
      </nz-descriptions>
    </section>
  }
</ng-template>

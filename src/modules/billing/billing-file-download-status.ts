import { List } from '~/common/lists';

export enum BillingFileDownloadStatus {
  Started = 'started',
  Requested = 'requested',
  Finished = 'finished',
  Skipped = 'skipped',
  Error = 'error'
}

export const billingFileDownloadStatuses = new List<
  BillingFileDownloadStatus,
  { color: 'warning' | 'danger' | 'success' }
>({
  [BillingFileDownloadStatus.Started]: {
    name: 'Started',
    color: 'warning'
  },
  [BillingFileDownloadStatus.Requested]: {
    name: 'Requested',
    color: 'warning'
  },
  [BillingFileDownloadStatus.Finished]: {
    name: 'Finished',
    color: 'success'
  },
  [BillingFileDownloadStatus.Skipped]: {
    name: 'Skipped',
    color: 'warning'
  },
  [BillingFileDownloadStatus.Error]: {
    name: 'Failed',
    color: 'danger'
  }
});

import { action } from '~/common/actions/action';
import { any, date, keyValue, number, object, optional, string } from '~/common/schema';
import { BillingFileStageData } from './billing-file-stage-data';

export const ParseBillingFile = action({
  name: 'Billing:Files.parseFile',
  result: any(),
  params: {
    /**
     * Parser ID
     */
    parserId: string(),

    /**
     * Parser configuration
     */
    config: optional(keyValue()),

    /**
     * Parsed file
     */
    file: object({
      name: string(),
      period: string(),
      periodThreshold: optional(number()),
      cycleStartDate: date(),
      cycleEndDate: date(),
      vendorId: string(),
      vendorRefId: string(),
      accountRefId: optional(string())
    }),

    /**
     * Default data type from file type code
     */
    type: string(),

    /**
     * Path of parsed file
     */
    path: string(),

    /**
     * Directory of the parsed file
     */
    directory: string(),

    /**
     * Password for protected files
     */
    password: optional(string())
  }
});

export const ExecuteBillingFileStage = action({
  name: 'Billing:Files:Pipeline.executeStage',
  result: optional(string()),
  params: {
    fileId: string(),
    path: string(),
    data: optional(BillingFileStageData),
    context: object({ name: string() })
  }
});

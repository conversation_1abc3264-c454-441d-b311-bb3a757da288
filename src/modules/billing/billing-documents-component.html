<ng-template #header>
  <form nz-form nzLayout="inline" [formGroup]="filter" [formRouteSync]="filterRouteSync">
    <nz-form-item>
      <nz-form-label>Vendor</nz-form-label>
      <nz-form-control>
        <vendor-select formControlName="vendorId" placeholder="All vendors" />
      </nz-form-control>
    </nz-form-item>
    <nz-form-item [hidden]="filter.controls.accountId.disabled">
      <nz-form-label>Account</nz-form-label>
      <nz-form-control>
        <vendor-account-select
          [vendorId]="filter.value.vendorId"
          formControlName="accountId"
          placeholder="All accounts"
        />
      </nz-form-control>
    </nz-form-item>
    <nz-form-item>
      <nz-form-label>Period</nz-form-label>
      <nz-form-control>
        <period-select formControlName="period" placeholder="Any period" />
      </nz-form-control>
    </nz-form-item>
    <nz-form-item>
      <nz-form-label>
        Search
        <help-icon content="Search by document ID, PO number, vendor, account or country" />
      </nz-form-label>
      <nz-form-control>
        <search-input formControlName="search" />
      </nz-form-control>
    </nz-form-item>
    <nz-form-item>
      <nz-form-label>Type</nz-form-label>
      <nz-form-control>
        <list-select [list]="types" formControlName="type" placeholder="All types" />
      </nz-form-control>
    </nz-form-item>
    <nz-form-item>
      <nz-form-label>Amount in USD</nz-form-label>
      <nz-form-control>
        <nz-switch formControlName="usd" />
      </nz-form-control>
    </nz-form-item>
  </form>
</ng-template>

<collection-page
  [header]="header"
  [actions]="globalActions"
  [loader]="loader"
  [columns2]="columns"
  [templates]="{ vendor, refId, period, files, connectionsCount, netAmount, totalAmount }"
  [sort]="filter.value.sort"
  (sort)="filter.patchValue({ sort: $event })"
  (rowClick)="openDocument($event)"
>
  <ng-template #vendor let-document>
    <vendor-name [vendor]="document.vendor" />
    @if (document.account) {
      <p class="detail" [class.vendor-with-country]="service.settings.vendors?.withCountry">
        <vendor-account-name [account]="document.account" target="list" />
      </p>
    }
  </ng-template>

  <ng-template #refId let-document let-property="column">
    <property-value [property]="property" [data]="document" />
    @if (document.poRefId) {
      <p class="detail">PO: {{ document.poRefId }}</p>
    }
  </ng-template>

  <ng-template #period let-document>
    <period-name [period]="document.period" />
    <p class="detail">
      <cycle [entity]="document" />
    </p>
  </ng-template>

  <ng-template #files let-document>
    @for (file of document.files; track file.id) {
      <ng-template #fileInfo>
        <file-name [name]="file.name" [description]="file.description" [url]="file.url" />
      </ng-template>
      <a [href]="file.url" (click)="$event.stopPropagation()" target="_blank">
        <file-icon [name]="file.name" nz-popover [nzPopoverContent]="fileInfo" />
      </a>
    }
  </ng-template>

  <ng-template #connectionsCount let-document let-property="column">
    <editable-property [property]="property" [value]="document.connectionsCount" [save]="updateDocument(document.id)">
      @if (document.connectionsCount) {
        {{ document.connectionsCount }}
      } @else {
        <span class="text-disabled">-</span>
      }
    </editable-property>
  </ng-template>

  <ng-template #netAmount let-document>
    <ng-container
      [ngTemplateOutlet]="amount"
      [ngTemplateOutletContext]="{
        data: {
          amount: document.netAmount,
          amountUsd: document.netAmountUsd,
          currency: document.currency,
          usd: filter.value.usd,
          usdRate: document.usdRate
        }
      }"
    />
  </ng-template>

  <ng-template #totalAmount let-document>
    <ng-container
      [ngTemplateOutlet]="amount"
      [ngTemplateOutletContext]="{
        data: {
          amount: document.totalAmount,
          amountUsd: document.totalAmountUsd,
          currency: document.currency,
          usd: filter.value.usd,
          usdRate: document.usdRate
        }
      }"
    />
  </ng-template>
</collection-page>

<ng-template #amount let-data="data">
  <ng-template #format let-usd="usd">
    @if (usd) {
      <span class="text-nowrap" nz-popover [nzPopoverContent]="`1 USD = ${data.usdRate} ${data.currency}`">
        {{ data.amountUsd | currency: data.currency : '' }} USD
      </span>
    } @else {
      <span class="text-nowrap">{{ data.amount | currency: data.currency : '' }} {{ data.currency }}</span>
    }
  </ng-template>
  @if (data.amount | isEmpty) {
    <span class="text-disabled">-</span>
  } @else if (data.currency === 'USD') {
    <ng-container [ngTemplateOutlet]="format" />
  } @else {
    @if (data.usd && data.amountUsd | isEmpty) {
      <span class="text-danger">no rate available</span>
    } @else {
      <ng-container [ngTemplateOutlet]="format" [ngTemplateOutletContext]="{ usd: data.usd }" />
    }
    @if (data.usd) {
      <p class="detail">
        <ng-container [ngTemplateOutlet]="format" [ngTemplateOutletContext]="{ usd: !data.usd }" />
      </p>
    }
  }
</ng-template>

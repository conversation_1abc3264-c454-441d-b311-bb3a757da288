import { basename } from 'path';
import { AzureDocumentIntelligenceClient } from '~/common/azure/document-intelligence';
import { EntityManager, create } from '~/common/database';
import { Worker } from '~/common/jobs';
import { formatPeriod, getEntityPeriodCycle, getPeriodByCycleStart } from '~/common/periods';
import { Provider } from '~/common/providers';
import { omitBy, withObjectMetadata } from '~/common/utils/object';
import { parsePlaceholders } from '~/common/utils/template';
import { ifDefined, isEmpty } from '~/common/utils/value';
import { Vendor } from '~/common/vendors';
import { BillingDocument } from './billing-document';
import { BillingDocumentItem } from './billing-document-item';
import { BillingDocumentParserAgent } from './billing-document-parser-agent';
import { BillingDocumentParserField, type BillingDocumentParserConfig } from './billing-document-parser-config';
import { BillingDocumentType } from './billing-document-type';
import { BillingFile } from './billing-file';
import { BillingFileDataError } from './billing-file-data-error';
import { BillingFileParser, type BillingFileParsePayload, type BillingFileParseResult } from './billing-file-parser';
import { BillingFileStorage } from './billing-file-storage';

const defaultPages = '1-2';

@Provider()
export class BillingDocumentParser {
  constructor(
    private entityManager: EntityManager,
    private fileStorage: BillingFileStorage,
    private di: AzureDocumentIntelligenceClient,
    private agent: BillingDocumentParserAgent
  ) {}

  /**
   * Billing document parser
   */
  @BillingFileParser('document')
  async parseFile({ file, path, config }: BillingFileParsePayload): Promise<BillingFileParseResult> {
    const document = await this.createDocument(file.vendorId, path, {
      config,
      defaults: { cycleStartDate: file.cycleStartDate, cycleEndDate: file.cycleEndDate },
      filename: file.name
    });

    // reject duplicated ref ID
    if (document.refId && !document.id) {
      const existing = await this.entityManager.findOneBy(BillingDocument, {
        vendorId: document.vendorId,
        refId: document.refId
      });
      if (existing) {
        throw new BillingFileDataError(
          `Billing document "${document.refId}" already exists in period "${formatPeriod(existing.period)}"`
        );
      }
    }

    return [
      {
        type: BillingDocument,
        source: [document],
        validate: {
          period: () => document.period,
          accountId: () => document.account?.refId
        },
        after: async (file) => {
          // save items
          if (document.items) {
            await this.entityManager.save(
              document.items.map((item) => {
                item.documentId = document.id;
                return item;
              })
            );
          }

          // connect document with the file
          file.documentId = document.id;
          await this.entityManager.save(file);
        }
      }
    ];
  }

  @Worker({
    description: 'Parse document PDF file associated with billing file and update the document with the result',
    toArgs: ({ id, config }) => [id, { config }]
  })
  async parseFileDocument(id: string, options: { config?: BillingDocumentParserConfig }) {
    const file = await this.entityManager.findOneOrFail(BillingFile, {
      where: { id },
      relations: { document: true, type: true }
    });
    const content = await this.fileStorage.readFile(file.id);
    const document = await this.createDocument(file.vendorId, content, {
      config: options.config ?? file.type?.parserConfig, // reuse file type parser config (if available),
      defaults: file.document ?? { cycleStartDate: file.cycleStartDate, cycleEndDate: file.cycleEndDate },
      filename: file.name
    });
    return this.entityManager.transaction(async (entityManager) => {
      await entityManager.save(document);

      if (!file.documentId) {
        file.documentId = document.id;
        await entityManager.save(file);
      }

      if (document.items) {
        await entityManager.delete(BillingDocumentItem, { documentId: document.id });
        await entityManager.save(
          document.items.map((item) => {
            item.documentId = document.id;
            return item;
          })
        );
      }

      return document;
    });
  }

  /**
   * Create document from parsed PDF
   */
  async createDocument(
    vendorId: string,
    path: string | Buffer,
    options: { config?: BillingDocumentParserConfig; defaults?: Partial<BillingDocument>; filename?: string } = {}
  ) {
    const vendor = await this.entityManager.findOneOrFail(Vendor, {
      where: { id: vendorId },
      relations: { country: true, accounts: true }
    });

    const { config, defaults } = options;
    const { items, pages, locale, currency } = omitBy(config ?? {}, isEmpty);

    const fields = omitBy(config?.fields ?? {}, isEmpty) as Record<BillingDocumentParserField, string>;
    const aliases: Record<string, string> = {};
    const formulas: Record<string, string> = {};
    for (const [targetKey, sourceKey] of Object.entries(fields)) {
      if (sourceKey) {
        // parse formulas from mapping
        const placeholders = parsePlaceholders(sourceKey);
        if (placeholders.length) {
          formulas[targetKey] = sourceKey;
          for (const { variable } of placeholders) {
            aliases[variable] = variable;
          }
        } else {
          aliases[targetKey] = sourceKey;
        }
      }
    }

    const values = await (async () => {
      try {
        return await this.di.analyzeDocument(path, {
          aliases,
          locale: locale ?? vendor.country?.languageId,
          pages: pages ?? defaultPages
        });
      } catch (cause) {
        throw new BillingFileDataError(
          'Failed to analyze the document',
          {},
          {
            cause,
            hints: cause instanceof Error ? [`Parser responded with an error: "${cause.message}"`] : []
          }
        );
      }
    })();

    const { agent } = this;
    let agentPromise: ReturnType<typeof agent.evaluate>;
    const fromAgent = async (field: string) => {
      if (!agentPromise) {
        if (field in fields) {
          agentPromise = agent.evaluate(fields, {
            result: values.result,
            filename: options.filename ?? (typeof path === 'string' ? basename(path) : undefined)
          });
        } else {
          return undefined;
        }
      }

      const result = await agentPromise;
      return result?.fields.find((f) => f.field === field)?.result;
    };

    function evaluate(field: BillingDocumentParserField) {
      return {
        string: () => values.string(field) ?? fromAgent(field),
        number: async () => {
          const value = values.number(field);
          if (value !== undefined) {
            return value;
          }

          let formula: string | undefined = formulas[field];
          if (!formula) {
            // get formula from AI
            formula = (await fromAgent(field)) ?? undefined;
          }
          if (!formula) {
            return undefined;
          }

          for (const { placeholder, variable } of parsePlaceholders(formula)) {
            const value = values.number(variable);
            if (value !== undefined) {
              formula = formula.replace(placeholder, String(value));
            } else {
              // if any placeholder is empty => return undefined
              return undefined;
            }
          }

          try {
            const result = parseFloat(eval(formula));
            return isNaN(result) ? undefined : result;
          } catch {
            return undefined;
          }
        }
      };
    }

    const totalAmount = (await evaluate('totalAmount').number()) ?? values.number(['InvoiceTotal', 'AmountDue']);
    const taxAmount = (await evaluate('taxAmount').number()) ?? values.number(['TotalTax']);

    const skip = '-';
    const document = create(BillingDocument, {
      id: defaults?.id,
      type: BillingDocumentType.Invoice,
      vendorId,
      vendor,
      cycleStartDate: values.date('ServiceStartDate', { confidence: 0.6 }) ?? defaults?.cycleStartDate,
      cycleEndDate: values.date('ServiceEndDate', { confidence: 0.6 }) ?? defaults?.cycleEndDate,
      refId: fields.refId === skip ? null! : ((await evaluate('refId').string()) ?? values.string(['InvoiceId'])),
      poRefId:
        fields.poRefId === skip
          ? null!
          : ((await evaluate('poRefId').string()) ?? values.string(['poRefId', 'PurchaseOrder'])),
      issueDate: values.date('InvoiceDate'),
      currency: currency ?? values.currency(['InvoiceTotal', 'AmountDue', 'Items.Amount']),
      netAmount:
        (await evaluate('netAmount').number()) ??
        ifDefined(taxAmount, (tax) => values.number(['SubTotal']) ?? ifDefined(totalAmount, (total) => total - tax)),
      taxAmount,
      otherAmount: await evaluate('otherAmount').number(),
      totalAmount,
      connectionsCount: ifDefined(await evaluate('connectionsCount').number(), Math.floor) ?? defaults?.connectionsCount
    });

    // remove non-alphanumeric prefixes from invoice number and PO number (e.g. "**********-", ": *********")
    for (const field of ['refId', 'poRefId'] as const) {
      const value = document[field]?.replace(/^([^0-9a-z]+)/i, '').replace(/([^0-9a-z]+)$/i, '');
      document[field] = value?.length ? value : null!;
    }

    // currency is required (from total amount)
    if (!document.currency) {
      throw new BillingFileDataError('Total invoice amount not found in the file');
    }

    // find account
    const accountId = (await evaluate('accountId').string()) ?? values.string(['CustomerId']);
    const sanitizeAccountId = (value: string) => value.toLowerCase().replaceAll(/[^a-z0-9]/gi, '');
    document.account = accountId
      ? vendor.accounts!.find(({ refId }) => sanitizeAccountId(refId) === sanitizeAccountId(accountId))
      : null!;
    document.accountId = document.account?.id;

    // set cycle dates if not provided
    if (!document.cycleStartDate || !document.cycleEndDate) {
      const cycle = getEntityPeriodCycle(document.account ? [document.account, vendor] : [vendor]);
      document.cycleStartDate = cycle.startDate;
      document.cycleEndDate = cycle.endDate;
    }

    // set period
    document.period = getPeriodByCycleStart(document.cycleStartDate, vendor.periodThreshold);

    if (items) {
      document.items = values.collection('Items').map((v, i) =>
        create(BillingDocumentItem, {
          description: v.string('Description'),
          quantity: v.number('Quantity'),
          unitName: v.string('Unit'),
          unitPrice: v.number('UnitPrice'),
          totalAmount: v.number('Amount'),
          position: i + 1
        })
      );
    }

    return withObjectMetadata(document, { fields: values.result });
  }
}

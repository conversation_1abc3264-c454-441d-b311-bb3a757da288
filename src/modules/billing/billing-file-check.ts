import { ManyToOne, PrimaryGenerated, Required, Table, type Relation } from '~/common/database/entity';
import { BillingFile } from './billing-file';

@Table('billing_file_checks', { events: true })
export class BillingFileCheck {
  /**
   * Type check ID
   * @example 8060ff50-4018-4b0e-8839-c5941bf11758
   */
  @PrimaryGenerated()
  id: string;

  /**
   * File ID
   * @example 89b11058-d4fe-42d5-ba2f-b02af4ff462c
   */
  @Required()
  fileId: string;

  /**
   * File
   */
  @ManyToOne(() => BillingFile, 'fileId')
  file?: Relation<BillingFile>;

  /**
   * Dataset code
   */
  @Required()
  dataset: string;

  /**
   * Actual records count
   * @example 7000
   */
  @Required()
  recordsCount: number;

  /**
   * Expected minimum records count
   * @example 10500
   */
  @Required()
  minRecordsCount?: number;

  /**
   * Expected maximum records count
   * @example 12000
   */
  @Required()
  maxRecordsCount?: number;

  /**
   * Check passed?
   * @example false
   */
  @Required()
  passed: boolean;

  /**
   * Execution date
   */
  @Required()
  executedDate: Date;
}

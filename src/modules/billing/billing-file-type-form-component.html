<div [loader]="loader">
  <ng-template let-fileType>
    <form nz-form [formGroup]="form">
      <nz-form-item>
        <nz-form-label [nzSm]="6" [nzXs]="24" nzRequired>Vendor</nz-form-label>
        <nz-form-control [nzSm]="18" [nzXs]="24">
          <vendor-select
            formControlName="vendorId"
            (vendor)="vendor$.next($event)"
            [params]="{ expand: ['dataSources'] }"
          />
        </nz-form-control>
      </nz-form-item>

      @if (examples.name | async; as example) {
        <nz-form-item [class.with-example]="example.value">
          <nz-form-label [nzSm]="6" [nzXs]="24" nzRequired>Filename</nz-form-label>
          <nz-form-control [nzSm]="18" [nzXs]="24">
            <input nz-input formControlName="name" />
          </nz-form-control>
          <ng-container [ngTemplateOutlet]="exampleTpl" [ngTemplateOutletContext]="{ example: example }" />
        </nz-form-item>
      }

      <nz-form-item>
        <nz-form-label [nzSm]="6" [nzXs]="24">Description</nz-form-label>
        <nz-form-control [nzSm]="18" [nzXs]="24">
          <input nz-input formControlName="description" placeholder="E.g. Usage data" />
        </nz-form-control>
      </nz-form-item>

      @if (form.controls.perAccount.enabled) {
        <nz-form-item>
          <nz-form-label [nzSm]="6" [nzXs]="24">Per account</nz-form-label>
          <nz-form-control [nzSm]="18" [nzXs]="24">
            <nz-select formControlName="perAccount" style="width: 12rem">
              <nz-option nzLabel="No" nzValue="no" />
              <nz-option nzLabel="All accounts" nzValue="all" />
              <nz-option nzLabel="Selected accounts" nzValue="selected" />
            </nz-select>
            @if (form.controls.accounts.enabled) {
              <vendor-account-select
                formControlName="accounts"
                [vendorId]="form.value.vendorId"
                [multiple]="true"
                key="refId"
                class="d-block mt-1"
              />
            }
          </nz-form-control>
        </nz-form-item>
      }

      @if (examples.cycle | async; as example) {
        <nz-form-item [class.with-example]="example.value">
          <nz-form-label [nzSm]="6" [nzXs]="24">Cycle</nz-form-label>
          <nz-form-control [nzSm]="18" [nzXs]="24">
            <nz-select formControlName="cycleSource" style="width: 12rem">
              <nz-option nzLabel="Inherit from vendor" nzValue="inherit" />
              <nz-option nzLabel="Custom" nzValue="custom" />
            </nz-select>
            @if (form.controls.cycleStartDay.enabled) {
              <nz-form-item class="p-1">
                <nz-form-label>Start day</nz-form-label>
                <nz-form-control>
                  <cycle-start-day-input formControlName="cycleStartDay" />
                </nz-form-control>
                <nz-form-label>Duration</nz-form-label>
                <nz-form-control>
                  <cycle-duration-input formControlName="cycleDuration" />
                </nz-form-control>
              </nz-form-item>
            }
          </nz-form-control>
          <ng-template #cycleExampleTpl let-value="value">
            {{ value | cycle }}
          </ng-template>
          <ng-container
            [ngTemplateOutlet]="exampleTpl"
            [ngTemplateOutletContext]="{ example: example, tpl: cycleExampleTpl }"
          />
        </nz-form-item>
      }

      @if (examples.expectedDate | async; as example) {
        <nz-form-item [class.with-example]="example.value">
          <nz-form-label [nzSm]="6" [nzXs]="24" nzRequired>Expected</nz-form-label>
          <nz-form-control [nzSm]="18" [nzXs]="24">
            <input nz-input formControlName="expectedOffset" type="number" style="width: 3rem" />
            <span class="mx-2">days</span>
            <nz-select formControlName="expectedBase" style="width: 9rem">
              <nz-option nzLabel="after cycle end" nzValue="end" />
              <nz-option nzLabel="after cycle start" nzValue="start" />
            </nz-select>
          </nz-form-control>
          <ng-template #expectedDateExampleTpl let-value="value">{{ value | date }}</ng-template>
          <ng-container
            [ngTemplateOutlet]="exampleTpl"
            [ngTemplateOutletContext]="{ example: example, tpl: expectedDateExampleTpl }"
          />
        </nz-form-item>
      }

      <nz-form-item>
        <nz-form-label [nzSm]="6" [nzXs]="24">Valid from</nz-form-label>
        <nz-form-control [nzSm]="6" [nzXs]="24">
          <period-select formControlName="validFromPeriod" />
        </nz-form-control>
        <nz-form-label [nzSm]="5" [nzXs]="24">Valid to</nz-form-label>
        <nz-form-control [nzSm]="7" [nzXs]="24">
          <period-select formControlName="validToPeriod" />
        </nz-form-control>
      </nz-form-item>

      <nz-form-item>
        <nz-form-label [nzSm]="6" [nzXs]="24">Source</nz-form-label>
        <nz-form-control [nzSm]="18" [nzXs]="24" class="text-nowrap">
          <nz-select
            formControlName="sourceType"
            nzPlaceHolder="Select source type"
            [nzAllowClear]="true"
            style="width: 12rem"
          >
            @for (sourceType of sourceTypes; track sourceType.id) {
              <nz-option [nzLabel]="sourceType.name" [nzValue]="sourceType.id" />
            }
          </nz-select>
          @if (dataSources$ | async; as dataSources) {
            @if (form.controls.sourceId.enabled && dataSources.length) {
              <div class="mt-1">
                <nz-select
                  formControlName="sourceId"
                  nzPlaceHolder="Select available data source"
                  [nzAllowClear]="true"
                >
                  @for (source of dataSources; track source.id) {
                    @if (source.type === form.value.sourceType) {
                      <nz-option [nzValue]="source.id" [nzLabel]="source.url" />
                    }
                  }
                </nz-select>
              </div>
            }
          }
          @if (contacts$ | async; as contacts) {
            @if (form.controls.contacts.enabled && contacts.length) {
              <div class="mt-1">
                <ul class="list-unstyled">
                  @for (group of form.controls.contacts.controls; track group) {
                    <li [formGroup]="group" class="mb-1">
                      <nz-select formControlName="contactId" [nzAllowClear]="false" style="width: 20rem">
                        @for (contact of contacts; track contact.id) {
                          <nz-option [nzLabel]="contact.email ?? (contact | personName)" [nzValue]="contact.id" />
                        }
                      </nz-select>
                      <action-button [action]="removeContactAction" [context]="$index" />
                    </li>
                  }
                </ul>
                <action-button [action]="addContactAction" />
              </div>
            }
          }
        </nz-form-control>
      </nz-form-item>

      <nz-form-item>
        <nz-form-label [nzSm]="6" [nzXs]="24">Document type</nz-form-label>
        <nz-form-control [nzSm]="6" [nzXs]="24" class="text-nowrap">
          <nz-select
            formControlName="documentType"
            nzPlaceHolder="Select type"
            [nzAllowClear]="true"
            style="width: 9rem"
          >
            @for (documentType of documentTypes; track documentType.id) {
              <nz-option [nzLabel]="documentType.name" [nzValue]="documentType.id" />
            }
          </nz-select>
        </nz-form-control>
        @if (form.controls.parseDocument.enabled) {
          <nz-form-control [nzSm]="10" [nzXs]="24">
            <nz-select formControlName="parseDocument" style="width: 8rem">
              <nz-option nzLabel="Automatic" [nzValue]="true" />
              <nz-option nzLabel="Manual" [nzValue]="false" />
            </nz-select>
            <help-icon class="ml-2" [content]="info">
              <ng-template #info>
                <p>
                  <strong>Automatic</strong><br />
                  Document will be created automatically from a parsed file.
                </p>
                <p>
                  <strong>Manual</strong><br />
                  Document form will be opened for manual entry when file is uploaded.
                </p>
              </ng-template>
            </help-icon>
          </nz-form-control>
          @if (form.controls.documentParserConfig.enabled) {
            <nz-form-control [nzSm]="2" [nzXs]="24">
              <div class="text-right">
                <action-button [action]="parserConfigAction" />
              </div>
            </nz-form-control>
          }
        }
      </nz-form-item>
      <nz-form-item>
        <nz-form-label [nzSm]="6" [nzXs]="24">Password protected</nz-form-label>
        <nz-form-control [nzSm]="2" [nzXs]="24" class="text-nowrap">
          <label formControlName="isPasswordProtected" nz-checkbox></label>
        </nz-form-control>
        @if (form.controls.password.enabled) {
          <nz-form-label [nzSm]="4" [nzXs]="24">Password</nz-form-label>
          <nz-form-control [nzSm]="12" [nzXs]="24" class="text-nowrap">
            <secret-input formControlName="password" />
          </nz-form-control>
        }
      </nz-form-item>
    </form>

    <div class="text-center mt-4">
      <action-button [action]="submitAction" />
    </div>
  </ng-template>
</div>

<ng-template #exampleTpl let-example="example" let-tpl="tpl">
  @if (example.value) {
    <div class="example">
      @if (!example.error) {
        <span class="text-muted">Example: </span>
      }
      <span [class.text-info]="example.value" [class.text-danger]="example.error">
        @if (tpl) {
          <ng-container [ngTemplateOutlet]="tpl" [ngTemplateOutletContext]="{ value: example.value }" />
        } @else {
          {{ example.value }}
        }
      </span>
    </div>
  }
</ng-template>

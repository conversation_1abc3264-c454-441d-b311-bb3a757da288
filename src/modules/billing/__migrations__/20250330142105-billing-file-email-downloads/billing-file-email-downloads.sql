-- set user_id for existing email downloads
UPDATE billing_file_downloads d
   SET user_id = ( SELECT created_user_id FROM billing_file_stages WHERE file_id = d.file_id AND created_user_id IS NOT NULL LIMIT 1 )
 WHERE source_type = 'email'
   AND status = 'finished';

-- set email downloader for types with email source and at least one successful email download
ALTER TABLE billing_file_types DISABLE TRIGGER USER;
UPDATE billing_file_types
   SET downloader_id = 'email'
 WHERE source_type = 'email'
   AND id IN (
        SELECT f.type_id
          FROM billing_files f
          JOIN billing_file_downloads d ON f.id = d.file_id
         WHERE f.status = 'processed'
           AND d.source_type = 'email' AND d.status = 'finished'
       );
ALTER TABLE billing_file_types ENABLE TRIGGER USER;

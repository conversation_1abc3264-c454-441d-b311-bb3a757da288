UPDATE billing_file_types SET name = 'AccountSummary_{PERIOD:YYYYMM}.txt' WHERE vendor_id = 'us-verizon' AND code = 'account-summary';
UPDATE billing_file_types SET name = 'Account & Wireless Summary_{PERIOD:YYYYMM}.txt' WHERE vendor_id = 'us-verizon' AND code = 'account-wireless-summary';
UPDATE billing_file_types SET name = 'Acct & Wireless Charges Detail Summary Usage_{PERIOD:YYYYMM}.txt' WHERE vendor_id = 'us-verizon' AND code = 'account-wireless-charges';
UPDATE billing_file_types SET name = 'AnalyzePhoneCharges_{PERIOD:YYYYMM}.txt' WHERE vendor_id = 'us-verizon' AND code = 'phone-charges';
UPDATE billing_file_types SET name = 'Device_{PERIOD:YYYYMM}.txt' WHERE vendor_id = 'us-verizon' AND code = 'device';
UPDATE billing_file_types SET name = 'Wireless Usage Detail_{PERIOD:YYYYMM}.txt' WHERE vendor_id = 'us-verizon' AND code = 'wireless-usage-detail';

UPDATE billing_files
   SET period = substr(cycle_end_date::text, 1, 7)
 WHERE type_id IN ( SELECT id FROM billing_file_types where cycle_start_day >= 15 );

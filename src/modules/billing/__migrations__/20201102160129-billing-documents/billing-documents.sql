CREATE TABLE billing_documents (
  id uuid NOT NULL DEFAULT uuid_generate_v4() PRIMARY KEY,
  vendor_id character varying NOT NULL,
  account_id character varying,
  type character varying NOT NULL,
  identifier character varying NOT NULL,
  created_date timestamptz NOT NULL DEFAULT CURRENT_TIMESTAMP,
  cycle_start_date date,
  cycle_end_date date,
  issue_date date,
  connections_count integer,
  currency character varying NOT NULL,
  net_amount numeric,
  tax_amount numeric,
  other_amount numeric,
  total_amount numeric NOT NULL,
  FOREIGN KEY (vendor_id) REFERENCES vendors (id) ON UPDATE CASCADE ON DELETE RESTRICT,
  FOREIGN KEY (account_id) REFERENCES vendor_accounts (id) ON UPDATE CASCADE ON DELETE RESTRICT
);

INSERT INTO billing_documents (id, vendor_id, account_id, type, identifier, created_date, cycle_start_date, cycle_end_date, issue_date, currency, net_amount, tax_amount, other_amount, total_amount)
SELECT id, vendor_id, account_id, 'invoice', number, created_date, usage_start_date, usage_end_date, issue_date, currency, net_amount, tax_amount, other_amount, total_amount
  FROM invoices;

DROP TABLE invoices;

ALTER TABLE billing_files ADD COLUMN document_id uuid;
ALTER TABLE billing_files ADD FOREIGN KEY (document_id)
  REFERENCES billing_documents (id) ON UPDATE CASCADE ON DELETE SET NULL;

CREATE INDEX billing_documents_vendor_id_account_id_idx ON billing_documents USING btree(vendor_id, account_id);

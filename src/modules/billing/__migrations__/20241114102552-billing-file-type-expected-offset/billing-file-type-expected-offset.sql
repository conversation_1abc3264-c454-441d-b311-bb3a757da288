ALTER TABLE billing_file_types ADD COLUMN expected_base varchar NOT NULL DEFAULT 'end';
ALTER TABLE billing_file_types ADD COLUMN expected_offset smallint NOT NULL DEFAULT 1;

ALTER TABLE billing_file_types DISABLE TRIGGER USER;
UPDATE billing_file_types
   SET expected_base = 'end',
       expected_offset = ( CASE
        WHEN expected_period_offset = 0 THEN expected_day - cycle_start_day + 1
	      WHEN expected_day >= cycle_start_day THEN expected_day - cycle_start_day + 1
	      ELSE 31 - cycle_start_day + expected_day + 1 END)
 WHERE cycle_start_day IS NOT NULL AND expected_day IS NOT NULL;
ALTER TABLE billing_file_types ENABLE TRIGGER USER;

ALTER TABLE billing_file_types ALTER COLUMN cycle_start_day DROP DEFAULT;
ALTER TABLE billing_file_types ALTER COLUMN cycle_duration DROP DEFAULT;

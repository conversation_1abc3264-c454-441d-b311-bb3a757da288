CREATE TABLE billing_file_stages (
  id uuid NOT NULL DEFAULT uuid_generate_v4() PRIMARY KEY,
  file_id uuid NOT NULL,
  name character varying NOT NULL,
  status character varying NOT NULL,
  path character varying,
  created_date timestamp with time zone NOT NULL DEFAULT NOW(),
  created_user_id character varying,
  start_date timestamp with time zone,
  finish_date timestamp with time zone,
  message character varying,
  job_id bigint,
  FOREIGN KEY (file_id) REFERENCES billing_files (id) ON UPDATE CASCADE ON DELETE RESTRICT,
  FOREIGN KEY (created_user_id) REFERENCES users (id) ON UPDATE CASCADE ON DELETE RESTRICT
);

CREATE OR REPLACE FUNCTION billing_file_stages_current_stage_trigger()
R<PERSON>URNS trigger AS
$$
DECLARE
  r record := (CASE TG_OP WHEN 'DELETE' THEN OLD ELSE NEW END);
  v_current_stage_id uuid;
BEGIN
  -- set last stage as current file stage
  SELECT id INTO v_current_stage_id
    FROM billing_file_stages
   WHERE file_id = r.file_id
   ORDER BY created_date DESC
   LIMIT 1;

  UPDATE billing_files
     SET stage_id = v_current_stage_id
   WHERE id = r.file_id;

  RETURN r;
END;
$$
LANGUAGE plpgsql;

CREATE TRIGGER billing_file_stages_current_stage_trigger
AFTER INSERT OR DELETE ON billing_file_stages
FOR EACH ROW EXECUTE PROCEDURE billing_file_stages_current_stage_trigger();

CREATE TABLE billing_file_imports (
  id uuid NOT NULL DEFAULT uuid_generate_v4() PRIMARY KEY,
  file_id uuid NOT NULL,
  table_name character varying NOT NULL,
  start_date timestamp with time zone NOT NULL,
  finish_date timestamp with time zone NOT NULL,
  records_count integer NOT NULL,
  FOREIGN KEY (file_id) REFERENCES billing_files (id) ON UPDATE CASCADE ON DELETE CASCADE,
  CONSTRAINT billing_file_imports_unique UNIQUE (file_id, table_name)
);

CREATE OR REPLACE FUNCTION billing_file_imports_delete_data_trigger()
RETURNS trigger AS
$$
BEGIN
  -- delete data from imported table
  EXECUTE 'DELETE FROM ' || OLD.table_name || ' WHERE file_id = $1 ' USING OLD.file_id;
  RETURN OLD;
END;
$$
LANGUAGE plpgsql;

CREATE TRIGGER billing_file_imports_delete_data_trigger
AFTER DELETE ON billing_file_imports
FOR EACH ROW EXECUTE PROCEDURE billing_file_imports_delete_data_trigger();

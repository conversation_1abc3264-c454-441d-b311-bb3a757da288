CREATE TABLE billing_file_downloads (
  id uuid NOT NULL DEFAULT uuid_generate_v4() PRIMARY KEY,
  file_id uuid NOT NULL,
  start_date timestamp with time zone NOT NULL,
  finish_date timestamp with time zone,
  status character varying NOT NULL,
  message character varying,
  path character varying,
  screenshot_url character varying,
  FOREIGN KEY (file_id) REFERENCES billing_files (id) ON UPDATE CASCADE ON DELETE CASCADE,
  CONSTRAINT billing_file_downloads_unique UNIQUE (file_id)
);

-- set downloader for CZ-Vodafone detail/XML type
UPDATE billing_file_types SET downloader_id = 'cz-vodafone:portal' WHERE vendor_id = 'cz-vodafone' AND code = 'detail';

-- add static types for CZ-Vodafone (statement, summery) with downloader
DELETE FROM billing_files WHERE type_id IN ( SELECT id FROM billing_file_types WHERE vendor_id = 'cz-vodafone' AND code in ('summary', 'statement') );
DELETE FROM billing_file_types WHERE vendor_id = 'cz-vodafone' AND code in ('summary', 'statement');
INSERT INTO billing_file_types (vendor_id, code, name, description, per_account, expected_day, downloader_id) VALUES
  ('cz-vodafone', 'summary', 'summary_{ACCOUNT}_XXXXXXXXXX_{PERIOD:YYYYMM}.pdf', 'Summary', true, 15, 'cz-vodafone:portal'),
  ('cz-vodafone', 'statement', 'statement_{ACCOUNT}_XXXXXXXXXX_{PERIOD:YYYYMM}.pdf', 'Statement', true, 15, 'cz-vodafone:portal');

ALTER TABLE billing_files DROP CONSTRAINT billing_files_type_id_fkey;

ALTER TABLE billing_file_types ALTER COLUMN id TYPE varchar USING id::varchar;
ALTER TABLE billing_files ALTER COLUMN type_id TYPE varchar USING type_id::varchar;

ALTER TABLE billing_files ADD FOREIGN KEY (type_id)
  REFERENCES billing_file_types (id) ON UPDATE CASCADE ON DELETE RESTRICT;

-- update ID to vendorId:code format, eg. cz-vodafone:detail
UPDATE billing_file_types SET id = concat(vendor_id, ':', code);

ALTER TABLE billing_file_types DROP COLUMN code;

CREATE TABLE billing_file_type_contacts (
  id uuid NOT NULL DEFAULT uuid_generate_v4(),
  type_id uuid NOT NULL,
  contact_id uuid NOT NULL,
  CONSTRAINT billing_file_type_contacts_unique UNIQUE (type_id, contact_id),
  F<PERSON>EI<PERSON><PERSON> KEY (type_id) REFERENCES billing_file_types (id) ON UPDATE CASCADE ON DELETE CASCADE,
  <PERSON>OR<PERSON><PERSON><PERSON> KEY (contact_id) REFERENCES contacts (id) ON UPDATE CASCADE ON DELETE CASCADE
);

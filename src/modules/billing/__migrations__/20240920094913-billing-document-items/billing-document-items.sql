CREATE TABLE billing_document_items (
  id uuid NOT NULL DEFAULT uuid_generate_v4(),
  document_id uuid NOT NULL,
  description varchar,
  quantity numeric,
  unit_name varchar,
  unit_price numeric,
  net_amount numeric,
  tax_amount numeric,
  total_amount numeric,
  position smallint NOT NULL,
  PRIMARY KEY (id),
  FOREIGN KEY (document_id) REFERENCES billing_documents (id) ON UPDATE CASCADE ON DELETE CASCADE
);

CREATE INDEX billing_document_items_document_id_idx ON billing_document_items USING btree(document_id);

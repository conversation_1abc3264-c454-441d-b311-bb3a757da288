ALTER TABLE billing_documents ADD COLUMN usd_rate numeric;
ALTER TABLE billing_documents ADD COLUMN net_amount_usd numeric;
ALTER TABLE billing_documents ADD COLUMN total_amount_usd numeric;

UPDATE billing_documents d
   SET usd_rate = r.rate,
       net_amount_usd = d.net_amount / r.rate,
       total_amount_usd = d.total_amount / r.rate
  FROM exchange_rates r
 WHERE r.date = COALESCE(d.issue_date, d.cycle_start_date)
   AND r.currency = d.currency;

ALTER TABLE billing_model_builds ADD COLUMN is_partial boolean NOT NULL DEFAULT false;

ALTER TABLE billing_model_builds ADD COLUMN service_id varchar;
UPDATE billing_model_builds b SET service_id = ( SELECT service_id FROM vendors WHERE id = b.vendor_id );
ALTER TABLE billing_model_builds ALTER COLUMN service_id SET NOT NULL;
ALTER TABLE billing_model_builds ADD FOREIGN KEY (service_id) REFERENCES services (id) ON UPDATE CASCADE ON DELETE RESTRICT;

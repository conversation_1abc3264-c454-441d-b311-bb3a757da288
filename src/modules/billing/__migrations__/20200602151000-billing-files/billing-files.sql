CREATE TABLE billing_files (
  id uuid NOT NULL DEFAULT uuid_generate_v4() PRIMARY KEY,
  vendor_id character varying NOT NULL,
  period character varying NOT NULL,
  cycle_start_date date,
  cycle_end_date date,
  type character varying,
  account_id character varying,
  name character varying,
  description character varying,
  url character varying,
  created_date timestamp with time zone NOT NULL DEFAULT NOW(),
  stage_id uuid,
  CONSTRAINT billing_files_unique UNIQUE (vendor_id, account_id, period, type),
  FOREIGN KEY (vendor_id) REFERENCES vendors (id) ON UPDATE CASCADE ON DELETE RESTRICT,
  FOREI<PERSON><PERSON> KEY (account_id) REFERENCES vendor_accounts (id) ON UPDATE CASCADE ON DELETE RESTRICT
);

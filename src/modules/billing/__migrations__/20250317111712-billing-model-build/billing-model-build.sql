CREATE TABLE billing_model_builds (
  id uuid NOT NULL DEFAULT uuid_generate_v4() PRIMARY KEY,
  vendor_id uuid NOT NULL,
  period varchar NOT NULL,
  status varchar NOT NULL,
  created_date timestamptz NOT NULL DEFAULT now(),
  start_date timestamptz,
  finish_date timestamptz,
  error jsonb,
  FOREIGN KEY (vendor_id) REFERENCES vendors (id) ON UPDATE CASCADE ON DELETE CASCADE
);

CREATE INDEX billing_model_builds_vendor_id_period_idx ON billing_model_builds USING btree(vendor_id, period);

INSERT INTO billing_model_builds (vendor_id, period, status, created_date, start_date, finish_date)
SELECT vendor_id, period, 'success', max(analyze_date), max(analyze_date), max(analyze_date)
  FROM billing_files
 WHERE analyze_date IS NOT NULL
   AND type_id IN ( SELECT id FROM billing_file_types WHERE parser_id IS NOT NULL )
 GROUP BY 1, 2, 3
 ORDER BY 4, 2, 1;

ALTER TABLE billing_files DROP COLUMN analyze_date;

CREATE TABLE billing_file_type_thresholds (
  id uuid NOT NULL DEFAULT uuid_generate_v4() PRIMARY KEY,
  type_id uuid NOT NULL,
  dataset varchar NOT NULL,
  account_id uuid,
  min_records_count integer,
  max_records_count integer,
  FOREIG<PERSON> KEY (type_id) REFERENCES billing_file_types (id) ON UPDATE CASCADE ON DELETE CASCADE,
  FOREIGN KEY (account_id) REFERENCES vendor_accounts (id) ON UPDATE CASCADE ON DELETE CASCADE
);

CREATE TABLE billing_file_checks (
  id uuid NOT NULL DEFAULT uuid_generate_v4() PRIMARY KEY,
  file_id uuid NOT NULL,
  dataset varchar NOT NULL,
  records_count integer NOT NULL,
  min_records_count integer,
  max_records_count integer,
  passed boolean NOT NULL,
  executed_date timestamptz NOT NULL DEFAULT CURRENT_TIMESTAMP,
  FOREI<PERSON>N KEY (file_id) REFERENCES billing_files (id) ON UPDATE CASCADE ON DELETE CASCADE
);

CREATE INDEX billing_file_checks_file_id_idx ON billing_file_checks USING btree(file_id);

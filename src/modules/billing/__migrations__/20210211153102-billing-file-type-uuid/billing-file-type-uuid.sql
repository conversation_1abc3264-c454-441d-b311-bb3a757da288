ALTER TABLE billing_file_types ADD COLUMN code varchar;
UPDATE billing_file_types SET code = (string_to_array(id, ':'))[2];

UPDATE billing_file_types SET id = uuid_generate_v4()
 WHERE id IN (
  'ae-du:all-calls',
  'ae-du:invoice',
  'ae-du:other-mobile-usage',
  'ae-du:statement-details',
  'au-optus:account-summary',
  'au-optus:invoice',
  'au-optus:usage',
  'be-proximus:cdr',
  'be-proximus:invoice',
  'be-proximus:proxi-budget',
  'be-proximus:rough-data',
  'br-claro:detail',
  'ca-bell:acc',
  'ca-bell:dtl',
  'ca-bell:mob',
  'ch-swisscom:pool-charge',
  'ch-swisscom:voice-bill-items',
  'ch-swisscom:voice-itemized',
  'cz-vodafone:data-usage',
  'cz-vodafone:detail',
  'cz-vodafone:detail:201907',
  'cz-vodafone:statement',
  'cz-vodafone:summary',
  'eg-vodafone:item:data',
  'eg-vodafone:sum:data',
  'eg-vodafone:sum:pdf',
  'es-vodafone:detail',
  'es-vodafone:summary',
  'fr-bouygues:invoice-items',
  'fr-bouygues:invoices',
  'fr-bouygues:usage',
  'fr-bouygues:user-invoice-items',
  'it-vodafone:data-traffic',
  'it-vodafone:international-usage',
  'it-vodafone:invoice',
  'it-vodafone:invoice-detail',
  'it-vodafone:roaming-in',
  'it-vodafone:roaming-out',
  'it-vodafone:sim-detail',
  'it-vodafone:sim-park',
  'it-vodafone:usage-detail',
  'it-vodafone:voice-traffic',
  'jp-softbank:all-priceitem',
  'jp-softbank:invoice',
  'jp-softbank:priceitem',
  'jp-softbank:statement',
  'sg-starhub:idd-cdr',
  'sg-starhub:idd-charge-summary',
  'sg-starhub:invoice',
  'sg-starhub:invoice-summary',
  'sg-starhub:local-usage-charges',
  'sg-starhub:roaming-cdr',
  'sg-starhub:roaming-charge-summary',
  'sg-starhub:spend-analysis',
  'sg-starhub:subscription-other-charges',
  'sk-orange:inv-det',
  'sk-orange:invoice',
  'sk-orange:usage',
  'ua-vodafone:detail',
  'uk-vodafone:charges',
  'uk-vodafone:usage',
  'us-verizon:account-summary',
  'us-verizon:account-wireless-charges',
  'us-verizon:account-wireless-summary',
  'us-verizon:device',
  'us-verizon:phone-charges',
  'us-verizon:wireless-usage-detail'
);

ALTER TABLE billing_files DROP CONSTRAINT billing_files_type_id_fkey;
ALTER TABLE billing_file_types ALTER COLUMN id TYPE uuid USING id::uuid;
ALTER TABLE billing_files ALTER COLUMN type_id TYPE uuid USING type_id::uuid;
ALTER TABLE billing_files ADD FOREIGN KEY (type_id) REFERENCES billing_file_types (id) ON UPDATE CASCADE ON DELETE RESTRICT;

CREATE TABLE invoices (
  id uuid NOT NULL DEFAULT uuid_generate_v4() PRIMARY KEY,
  vendor_id character varying NOT NULL,
  account_id character varying,
  number character varying NOT NULL,
  created_date timestamptz NOT NULL DEFAULT CURRENT_TIMESTAMP,
  usage_start_date date NOT NULL,
  usage_end_date date NOT NULL,
  issue_date date,
  currency character varying NOT NULL,
  net_amount real NOT NULL,
  tax_amount real NOT NULL,
  other_amount real,
  total_amount real NOT NULL,
  FOREIGN KEY (vendor_id) REFERENCES vendors (id) ON UPDATE CASCADE ON DELETE RESTRICT,
  FOREIGN KEY (account_id) REFERENCES vendor_accounts (id) ON UPDATE CASCADE ON DELETE RESTRICT,
  CONSTRAINT invoices_unique UNIQUE (vendor_id, account_id, number)
);

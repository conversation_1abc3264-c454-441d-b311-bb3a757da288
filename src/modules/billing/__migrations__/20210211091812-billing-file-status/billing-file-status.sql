ALTER TABLE billing_files ADD COLUMN status varchar NOT NULL DEFAULT 'expected';

UPDATE billing_files f
   SET status = (CASE s.status WHEN 'pending' THEN 'processing' WHEN 'error' THEN 'failed' ELSE 'processed' END)
  FROM billing_file_stages s
 WHERE f.stage_id = s.id;

ALTER TABLE billing_files ALTER COLUMN status SET NOT NULL;
ALTER TABLE billing_files DROP COLUMN stage_id;
DROP FUNCTION billing_file_stages_current_stage_trigger() CASCADE;

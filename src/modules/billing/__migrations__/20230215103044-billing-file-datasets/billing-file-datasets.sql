-- dataset for import - each import from single file can produce multiple datasets
ALTER TABLE billing_file_imports ADD COLUMN dataset varchar;
UPDATE billing_file_imports SET dataset = (regexp_match(table_name, '[a-z0-1_]\.raw_(.+)', 'i'))[1];

-- available datasets from billing file type
ALTER TABLE billing_file_types ADD COLUMN datasets varchar[];
UPDATE billing_file_types SET datasets = ARRAY[code] WHERE code IS NOT NULL AND parser_id IS NOT NULL;
UPDATE billing_file_types _t
   SET datasets = x.datasets
  FROM (
	  SELECT f.type_id, array_agg(distinct i.dataset) AS datasets
	    FROM billing_file_imports i
	    JOIN billing_files f on i.file_id = f.id
	    JOIN billing_file_types t on f.type_id = t.id
	   GROUP BY 1
  ) x
 WHERE id = x.type_id
   AND parser_id IS NOT NULL;

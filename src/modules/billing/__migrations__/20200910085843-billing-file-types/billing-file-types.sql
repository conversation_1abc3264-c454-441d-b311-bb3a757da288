CREATE TABLE billing_file_types (
  id uuid NOT NULL DEFAULT uuid_generate_v4() PRIMARY KEY,
  vendor_id character varying NOT NULL,
  name character varying NOT NULL,
  code character varying,
  description character varying,
  per_account boolean NOT NULL DEFAULT false,
  accounts character varying[],
  valid_from_period character varying,
  valid_to_period character varying,
  cycle_start_day smallint,
  cycle_duration smallint,
  expected_day smallint,
  period_length smallint NOT NULL DEFAULT 1,
  parser_id character varying,
  downloader_id character varying,
  FOREIGN KEY (vendor_id) REFERENCES vendors (id) ON UPDATE CASCADE ON DELETE RESTRICT,
  CONSTRAINT billing_file_types_vendor_id_code_unique UNIQUE (vendor_id, code)
);

-- BR Claro
INSERT INTO billing_file_types (vendor_id, code, name, expected_day, parser_id)
VALUES ('br-claro', 'detail', 'downloadTXT.zip', 17, 'br-claro:data');

-- CA Bell
INSERT INTO billing_file_types (vendor_id, code, name, expected_day, parser_id)
VALUES ('ca-bell', 'acc', 'B_0100666198_520710518_09{PERIOD:MMYYYY}_ACC.csv', 15, 'ca-bell:data');
INSERT INTO billing_file_types (vendor_id, code, name, expected_day, parser_id)
VALUES ('ca-bell', 'dtl', 'B_0100666198_520710518_09{PERIOD:MMYYYY}_DTL.csv', 15, 'ca-bell:data');
INSERT INTO billing_file_types (vendor_id, code, name, expected_day, parser_id)
VALUES ('ca-bell', 'mob', 'B_0100666198_520710518_09{PERIOD:MMYYYY}_MOB.csv', 15, 'ca-bell:data');

-- CH Swisscom
INSERT INTO billing_file_types (vendor_id, code, name, description, expected_day, per_account)
VALUES ('ch-swisscom', 'pool-charge', 'XXXXXXXXXX_Mobile Pool_{PERIOD:MMM YYYY:de-ch}.pdf', 'Pool charge', 21, true);
INSERT INTO billing_file_types (vendor_id, code, name, expected_day, parser_id)
VALUES ('ch-swisscom', 'voice-bill-items', 'rds_{PERIOD:YYYYMM}_voice_billitem.csv', 14, 'ch-swisscom:data');
INSERT INTO billing_file_types (vendor_id, code, name, expected_day, parser_id)
VALUES ('ch-swisscom', 'voice-itemized', 'rds_{PERIOD:YYYYMM}_voice_itemized.csv', 14, 'ch-swisscom:data');

-- CZ Vodafone
INSERT INTO billing_file_types (vendor_id, code, name, description, per_account)
VALUES ('cz-vodafone', 'summary', 'summary_{ACCOUNT}_XXXXXXXXXX_{PERIOD:YYYYMM}.pdf', 'Summary', true);
INSERT INTO billing_file_types (vendor_id, code, name, description, per_account)
VALUES ('cz-vodafone', 'statement', 'statement_{ACCOUNT}_XXXXXXXXXX_{PERIOD:YYYYMM}.pdf', 'Statement', true);
INSERT INTO billing_file_types (vendor_id, code, name, per_account, valid_to_period, parser_id)
VALUES ('cz-vodafone', 'detail:201907', '{ACCOUNT}_{ACCOUNT}_XXX{PERIOD:YYYY}_XXX.xlsx', true, '2019-07', 'cz-vodafone:detail');
INSERT INTO billing_file_types (vendor_id, code, name, per_account, expected_day, valid_from_period, parser_id)
VALUES ('cz-vodafone', 'detail', 'vfV2InvDIR2_{ACCOUNT}_XXXXXXXXXX_{PERIOD:YYYYMM}.xml', true, 15, '2019-08', 'cz-vodafone:detail');
INSERT INTO billing_file_types (vendor_id, code, name, per_account, expected_day, valid_from_period, parser_id)
VALUES ('cz-vodafone', 'data-usage', '{PERIOD:MM}_{ACCOUNT}.xlsx', true, 5, '2019-08', 'cz-vodafone:data-usage');

-- ES Vodafone
INSERT INTO billing_file_types (vendor_id, code, name, expected_day, parser_id)
VALUES ('es-vodafone', 'summary', 'Facturacion Novartis {PERIOD:MMM YY:es}.xlsx', 15, 'es-vodafone:summary');
INSERT INTO billing_file_types (vendor_id, code, name, expected_day, parser_id)
VALUES ('es-vodafone', 'detail', 'CI09XXXXXXXX', 15, 'es-vodafone:detail');

-- IT Vodafone
INSERT INTO billing_file_types (vendor_id, code, name, expected_day, parser_id)
VALUES ('it-vodafone', 'sim-park', 'Parco SIM.xlsx', 25, 'it-vodafone:summary');
INSERT INTO billing_file_types (vendor_id, code, name, expected_day, parser_id)
VALUES ('it-vodafone', 'sim-detail', 'SIM Eop Dettaglio Piano.xlsx', 25, 'it-vodafone:summary');
INSERT INTO billing_file_types (vendor_id, code, name, expected_day, parser_id)
VALUES ('it-vodafone', 'roaming-in', 'Roaming IN (new).xlsx', 25, 'it-vodafone:summary');
INSERT INTO billing_file_types (vendor_id, code, name, expected_day, parser_id)
VALUES ('it-vodafone', 'roaming-out', 'Roaming OUT (new).xlsx', 25, 'it-vodafone:summary');
INSERT INTO billing_file_types (vendor_id, code, name, expected_day, parser_id)
VALUES ('it-vodafone', 'data-traffic', 'Traffico Dati.xlsx', 25, 'it-vodafone:summary');
INSERT INTO billing_file_types (vendor_id, code, name, expected_day, parser_id)
VALUES ('it-vodafone', 'voice-traffic', 'Traffico Voce Mensile.xlsx', 25, 'it-vodafone:summary');
INSERT INTO billing_file_types (vendor_id, code, name, expected_day, parser_id)
VALUES ('it-vodafone', 'international-usage', 'Usage Internazonale.xlsx', 25, 'it-vodafone:summary');
INSERT INTO billing_file_types (vendor_id, code, name, expected_day, per_account, period_length)
VALUES ('it-vodafone', 'invoice', 'AM0XXXXXXX_9999_{ACCOUNT}.pdf', 10, true, 2);
INSERT INTO billing_file_types (vendor_id, code, name, expected_day, per_account, period_length, parser_id)
VALUES ('it-vodafone', 'invoice-detail', '21040AMXXXXXXXX_XXXXX_{ACCOUNT}.xlsx', 10, true, 2, 'it-vodafone:invoice-detail');
INSERT INTO billing_file_types (vendor_id, code, name, expected_day, accounts, period_length, parser_id)
VALUES ('it-vodafone', 'usage-detail', '21040AMXXXXXXXX_XXXXX_{ACCOUNT}.asc_det', 10, '{4.8935}', 2, 'it-vodafone:usage-detail');

-- JP Softbank
INSERT INTO billing_file_types (vendor_id, code, name, description, expected_day, per_account)
VALUES ('jp-softbank', 'invoice', 'bill_XXXXXXXXXXXXXX.pdf', 'Invoice', 15, true);
INSERT INTO billing_file_types (vendor_id, code, name, expected_day, per_account, parser_id)
VALUES ('jp-softbank', 'priceitem', 'billing_priceitem.csv', 15, true, 'jp-softbank:priceitem');
INSERT INTO billing_file_types (vendor_id, code, name, expected_day, per_account, parser_id)
VALUES ('jp-softbank', 'all-priceitem', 'billing_all_priceItem.csv', 15, true, 'jp-softbank:all-priceitem');
INSERT INTO billing_file_types (vendor_id, code, name, expected_day, accounts, parser_id)
VALUES ('jp-softbank', 'statement', 'meisai_csv_{PERIOD:YYYYMM}_all.csv', 15, '{**********}', 'jp-softbank:statement');

-- UA Vodafone
INSERT INTO billing_file_types (vendor_id, code, name, expected_day, per_account, parser_id)
VALUES ('ua-vodafone', 'detail', '{ACCOUNT}_{PERIOD:MM}.csv', 8, true, 'ua-vodafone:data');

-- UK Vodafone
INSERT INTO billing_file_types (vendor_id, code, name, expected_day, parser_id)
VALUES ('uk-vodafone', 'charges', 'Charges_925005987_670221028_{PERIOD:YYMM}XX_XXXX.xlsx', 24, 'uk-vodafone:data');
INSERT INTO billing_file_types (vendor_id, code, name, expected_day, parser_id)
VALUES ('uk-vodafone', 'usage', 'Usage_925005987_670221028_{PERIOD:YYMM}XX_XXXX.xlsx', 24, 'uk-vodafone:data');

-- US Verizon
INSERT INTO billing_file_types (vendor_id, code, name, expected_day, per_account, parser_id)
VALUES ('us-verizon', 'account-summary', 'AccountSummary_{PERIOD:YYYYMM}.txt', 20, true, 'us-verizon:data');
INSERT INTO billing_file_types (vendor_id, code, name, expected_day, per_account, parser_id)
VALUES ('us-verizon', 'account-wireless-summary', 'Account & Wireless Summary_{PERIOD:YYYYMM}.txt', 20, true, 'us-verizon:data');
INSERT INTO billing_file_types (vendor_id, code, name, expected_day, per_account, parser_id)
VALUES ('us-verizon', 'account-wireless-charges', 'Acct & Wireless Charges Detail Summary Usage_{PERIOD:YYYYMM}.txt', 20, true, 'us-verizon:data');
INSERT INTO billing_file_types (vendor_id, code, name, expected_day, per_account, parser_id)
VALUES ('us-verizon', 'phone-charges', 'AnalyzePhoneCharges_{PERIOD:YYYYMM}.txt', 20, true, 'us-verizon:data');
INSERT INTO billing_file_types (vendor_id, code, name, expected_day, per_account, parser_id)
VALUES ('us-verizon', 'device', 'Device_{PERIOD:YYYYMM}.txt', 20, true, 'us-verizon:data');
INSERT INTO billing_file_types (vendor_id, code, name, expected_day, per_account, parser_id)
VALUES ('us-verizon', 'wireless-usage-detail', 'Wireless Usage Detail_{PERIOD:YYYYMM}.txt', 20, true, 'us-verizon:data');

ALTER TABLE billing_files ADD COLUMN type_id uuid;
ALTER TABLE billing_files ADD FOREIGN KEY (type_id) REFERENCES billing_file_types (id) ON UPDATE CASCADE ON DELETE RESTRICT;
UPDATE billing_files f SET type_id = ( SELECT id FROM billing_file_types WHERE vendor_id = f.vendor_id AND code = f.type );

ALTER TABLE billing_files DROP COLUMN type;

<collection-page
  [header]="header"
  [actions]="globalActions"
  [loader]="loader"
  [columns]="columns"
  [content]="content"
/>

<ng-template #header>
  <form nz-form nzLayout="inline" [formGroup]="filter" [formRouteSync]="filterRouteSync">
    <nz-form-item>
      <nz-form-label>Vendor</nz-form-label>
      <nz-form-control>
        <vendor-select formControlName="vendorId" placeholder="All vendors" />
      </nz-form-control>
    </nz-form-item>
    <nz-form-item [hidden]="filter.controls.accountId.disabled">
      <nz-form-label>Account</nz-form-label>
      <nz-form-control>
        <vendor-account-select
          formControlName="accountId"
          [vendorId]="filter.value.vendorId"
          placeholder="All accounts"
        />
      </nz-form-control>
    </nz-form-item>
    <nz-form-item>
      <nz-form-label>Period</nz-form-label>
      <nz-form-control>
        <period-select formControlName="period" placeholder="Any period" />
      </nz-form-control>
    </nz-form-item>
    <nz-form-item>
      <nz-form-label>
        Search
        <help-icon content="Search by file name, description, vendor, account, country or document" />
      </nz-form-label>
      <nz-form-control>
        <search-input formControlName="search" />
      </nz-form-control>
    </nz-form-item>
    <nz-form-item>
      <nz-form-label>Status</nz-form-label>
      <nz-form-control>
        <list-select [list]="statuses" formControlName="status" placeholder="Any status" />
      </nz-form-control>
    </nz-form-item>
    <nz-form-item>
      <nz-form-label>Overdue</nz-form-label>
      <nz-form-control>
        <nz-switch formControlName="isOverdue">Overdue</nz-switch>
      </nz-form-control>
    </nz-form-item>
    <nz-form-item>
      <nz-form-label>Download</nz-form-label>
      <nz-form-control>
        <nz-switch formControlName="hasDownload">Download</nz-switch>
      </nz-form-control>
    </nz-form-item>
  </form>
</ng-template>

<ng-template #content let-files>
  @for (file of files; track file.id) {
    <tr
      [attr.data-id]="file.id"
      [attr.data-vendorId]="file.vendorId"
      [attr.data-accountId]="file.account?.refId"
      [attr.data-type]="file.type?.refId"
    >
      <td [class.vendor-with-country]="service.settings.vendors?.withCountry ?? false">
        <vendor-name [vendor]="file.vendor" />
        @if (file.account) {
          <p class="detail">
            <vendor-account-name [account]="file.account" target="list" />
          </p>
        }
      </td>
      <td class="text-nowrap">
        <period-name [period]="file.period" />
        @if (file.cycleStartDate) {
          <p class="detail">
            <cycle [entity]="file" />
          </p>
        }
        @if (file.expectedDate && file.status !== 'processed') {
          <p class="detail">
            <span
              nz-typography
              nz-tooltip
              [nzTooltipTitle]="'Expected date' + (file.isOverdue ? ' (overdue)' : '')"
              nz-typography
              [nzType]="file.isOverdue ? 'danger' : 'secondary'"
            >
              <span nz-icon nzType="clock-circle"></span>
              {{ file.expectedDate | date }}
            </span>
          </p>
        }
        @if (getDownload(file); as download) {
          <p class="detail">
            <span
              nz-tooltip
              [nzTooltipTitle]="download.status"
              nz-typography
              [nzType]="download.color"
              (click)="download.open()"
              [class.cursor-pointer]="download.data.screenshotUrl || download.data.sourceUrl"
            >
              <span nz-icon [nzType]="download.icon"></span>
              {{ download.data.finishDate ?? download.data.startDate | date }}
            </span>
          </p>
        }
      </td>
      <td class="filename pr-3">
        <file-name [name]="file.name" [url]="file.url" />
        @if (file.description || file.type?.description) {
          <p class="detail">
            {{ file.description || file.type?.description }}
          </p>
        }
        @if (file.document && canOpenDocument()) {
          <p class="detail">
            <a (click)="openDocument(file.documentId)">
              <span nz-icon nzType="file-done"></span>
              {{ getDocumentType(file.document.type) }} {{ file.document.refId }}
            </a>
          </p>
        }
      </td>
      <td data-column="status">
        <billing-file-status [status]="file.status" />
        @if (file.error) {
          <p class="detail">
            <billing-file-error [file]="file" [actions]="fileActions" />
          </p>
        }
      </td>
      <td class="text-right">
        <action-buttons [actions]="fileActions" [context]="file" size="small" />
      </td>
    </tr>
  }
</ng-template>

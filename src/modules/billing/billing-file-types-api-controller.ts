import { Authorize, CurrentUser, User, UuidParam, checkUserAbility, getCollection } from '~/common/api';
import { Body, Delete, Get, HttpCode, Patch, Post, Query } from '~/common/controllers';
import { Between, EntityManager, create, synchronizeEntities } from '~/common/database';
import { ForbiddenError, InvalidArgumentError, NotFoundError } from '~/common/errors';
import { JobBus } from '~/common/jobs';
import { getCurrentPeriod } from '~/common/periods';
import { ServiceApiController, ServiceId } from '~/common/services';
import { uniq } from '~/common/utils/array';
import { sortBy } from '~/common/utils/collection';
import { omit } from '~/common/utils/object';
import { VendorAccount } from '~/common/vendors';
import { BillingDocumentParser } from './billing-document-parser';
import { BillingFile } from './billing-file';
import { BillingFileImport } from './billing-file-import';
import { BillingFileImportRead } from './billing-file-import-permissions';
import { BillingFileStatus } from './billing-file-status';
import { BillingFileType } from './billing-file-type';
import { BillingFileTypeContact } from './billing-file-type-contact';
import { BillingFileTypeRead, BillingFileTypeWrite } from './billing-file-type-permissions';
import { BillingFileTypeThreshold } from './billing-file-type-threshold';
import { isBillingFileTypeValid } from './billing-file-type-utils';
import {
  BillingFileTypeCreateDto,
  BillingFileTypeParseDto,
  BillingFileTypeThresholdCreateDto,
  BillingFileTypeThresholdUpdateDto
} from './billing-file-types-dto';
import { BillingFileTypeImportsQueryParams, BillingFileTypesQueryParams } from './billing-file-types-query-params';

@ServiceApiController('billing/file-types')
export class BillingFileTypesApiController {
  constructor(
    private entityManager: EntityManager,
    private jobBus: JobBus
  ) {}

  /**
   * Return billing file types
   */
  @Get()
  @Authorize(BillingFileTypeRead)
  async getFileTypes(
    @ServiceId() serviceId: string,
    @Query() params: BillingFileTypesQueryParams = {},
    @CurrentUser() user: User
  ) {
    const query = this.entityManager
      .createQueryBuilder(BillingFileType, 'fileType')
      .innerJoinAndSelect('fileType.vendor', 'vendor')
      .where('vendor.serviceId = :serviceId', { serviceId })
      .orderBy('vendor.countryId')
      .addOrderBy('vendor.name');

    return getCollection(query, params, {
      filter: {
        hasDownloader: () => {
          query.andWhere('fileType.downloaderId IS NOT NULL');
        },
        hasParser: () => {
          query.andWhere('fileType.parserId IS NOT NULL');
        }
      },
      expand: {
        password: () => {
          if (params.id) {
            if (checkUserAbility(user, BillingFileTypeWrite)) {
              query.addSelect('fileType.password');
            }
          } else {
            throw new InvalidArgumentError('Password can be retrieved for a specified file type ID');
          }
        }
      },
      load: {
        contacts: async (types) => {
          const contacts = await this.entityManager
            .createQueryBuilder(BillingFileTypeContact, 'tc')
            .innerJoinAndSelect('tc.contact', 'contact')
            .where('tc.typeId IN (:...typeIds)', { typeIds: types.map((type) => type.id) })
            .getMany();

          for (const type of types) {
            type.contacts = contacts.filter((contact) => contact.typeId === type.id) ?? [];
          }
        },
        vendorAccounts: async (types) => {
          const accounts = await this.entityManager
            .createQueryBuilder(VendorAccount, 'ta')
            .where('ta.vendorId IN (:...vendorIds)', { vendorIds: uniq(types.map((type) => type.vendorId)) })
            .orderBy('ta.refId')
            .setParameter('period', getCurrentPeriod())
            .addOrderBy('(CASE WHEN ta.validToPeriod < :period THEN 1 ELSE 0 END)')
            .getMany();

          for (const type of types) {
            type.vendor!.accounts = accounts.filter((account) => account.vendorId === type.vendorId) ?? [];
          }
        }
      },
      transform: (fileTypes) =>
        sortBy(fileTypes, [
          // by country
          'vendor.countryId',
          // by vendor name
          'vendor.name',
          // valid first
          (ft) => (isBillingFileTypeValid(ft) ? 0 : 1),
          // non-data first
          (ft) => (ft.parserId ? 1 : 0),
          // name
          (ft) => ft.name.toLowerCase(),
          // accounts
          (ft) => ft.accounts?.join(',').toLowerCase(),
          // description
          (ft) => ft.description?.toLowerCase()
        ])
    });
  }

  /**
   * Create billing file type
   */
  @Post()
  @Authorize(BillingFileTypeWrite)
  async createFileType(@Body() data: BillingFileTypeCreateDto): Promise<BillingFileType> {
    return this.entityManager.save(create(BillingFileType, data));
  }

  /**
   * Return billing file type by ID
   */
  @Get(':fileTypeId')
  @Authorize(BillingFileTypeRead)
  async getFileType(
    @ServiceId() serviceId: string,
    @UuidParam('fileTypeId') fileTypeId: string,
    @Query() params: BillingFileTypesQueryParams = {},
    @CurrentUser() user: User
  ): Promise<BillingFileType> {
    const [fileType] = await this.getFileTypes(serviceId, { ...params, id: fileTypeId }, user);
    if (fileType) {
      return fileType;
    } else {
      throw new NotFoundError('File type not found');
    }
  }

  /**
   * Update billing file type
   */
  @Patch(':fileTypeId')
  @Authorize(BillingFileTypeWrite)
  async updateFileType(
    @ServiceId() serviceId: string,
    @UuidParam('fileTypeId') fileTypeId: string,
    @Body() data: BillingFileTypeCreateDto,
    @CurrentUser() user: User
  ): Promise<BillingFileType> {
    const fileType = await this.getFileType(serviceId, fileTypeId, { expand: ['contacts'] }, user);

    await this.entityManager.transaction(async (entityManager) => {
      // file type params
      Object.assign(fileType, omit(data, ['contacts']));
      await entityManager.save(fileType);

      // contacts
      if (data.contacts) {
        fileType.contacts = await synchronizeEntities(
          entityManager,
          BillingFileTypeContact,
          fileType.contacts ?? [],
          data.contacts.map((tc) => create(BillingFileTypeContact, { ...tc, typeId: fileTypeId }))
        );
      }
    });

    return fileType;
  }

  /**
   * Delete billing file type by ID
   */
  @Delete(':fileTypeId')
  @Authorize(BillingFileTypeWrite)
  @HttpCode(204)
  async deleteFileType(
    @ServiceId() serviceId: string,
    @UuidParam('fileTypeId') fileTypeId: string,
    @CurrentUser() user: User
  ): Promise<void> {
    const fileType = await this.getFileType(serviceId, fileTypeId, {}, user);

    // not allowed when processed files exist
    const file = await this.entityManager.findOne(BillingFile, {
      where: {
        typeId: fileTypeId,
        status: BillingFileStatus.Processed
      }
    });
    if (file) {
      throw new ForbiddenError(
        `File type can't be deleted. Processed files of this type exist and has to be deleted first.`
      );
    }

    await this.entityManager
      .createQueryBuilder()
      .delete()
      .from(BillingFile)
      .where('typeId = :fileTypeId', { fileTypeId })
      .andWhere('status = :status', { status: BillingFileStatus.Expected })
      .execute();

    await this.entityManager.remove(fileType);
  }

  private async getFileTypeThreshold(
    serviceId: string,
    fileTypeId: string,
    thresholdId: string,
    user: User
  ): Promise<BillingFileTypeThreshold> {
    const fileType = await this.getFileType(serviceId, fileTypeId, { expand: ['thresholds'] }, user);
    const threshold = fileType.thresholds!.find((t) => t.id === thresholdId);
    if (threshold) {
      return threshold;
    } else {
      throw new NotFoundError('Threshold not found');
    }
  }

  /**
   * Parse billing file type documents
   */
  @Post(':fileTypeId/parse')
  @Authorize(BillingFileTypeWrite)
  async parseFileTypeDocuments(
    @ServiceId() serviceId: string,
    @UuidParam('fileTypeId') fileTypeId: string,
    @Body() data: BillingFileTypeParseDto,
    @CurrentUser() user: User
  ) {
    const type = await this.getFileType(serviceId, fileTypeId, {}, user);
    if (!type.documentType) {
      throw new InvalidArgumentError('File type is not a document');
    }
    if (!type.parserId) {
      throw new InvalidArgumentError(`File type can't be parsed`);
    }

    const [from, to] = data.periods ?? [];
    const files = await this.entityManager.findBy(BillingFile, {
      typeId: type.id,
      status: BillingFileStatus.Processed,
      period: from ? Between(from, to) : undefined
    });
    for (const file of files) {
      await this.jobBus.addJob([BillingDocumentParser, 'parseFileDocument'], { id: file.id });
    }
  }

  /**
   * Create billing file type threshold
   */
  @Post(':fileTypeId/thresholds')
  @Authorize(BillingFileTypeWrite)
  async createFileTypeThreshold(
    @ServiceId() serviceId: string,
    @UuidParam('fileTypeId') fileTypeId: string,
    @Body() data: BillingFileTypeThresholdCreateDto,
    @CurrentUser() user: User
  ): Promise<BillingFileTypeThreshold> {
    const { thresholds } = await this.getFileType(serviceId, fileTypeId, { expand: ['thresholds'] }, user);

    if (thresholds!.some((t) => t.dataset === data.dataset && t.accountId == data.accountId)) {
      throw new InvalidArgumentError('Threshold already exists');
    }

    const threshold = create(BillingFileTypeThreshold, { typeId: fileTypeId, ...data });
    return this.entityManager.save(threshold);
  }

  /**
   * Update billing file type threshold by ID
   */
  @Patch(':fileTypeId/thresholds/:thresholdId')
  @Authorize(BillingFileTypeWrite)
  async updateFileTypeThreshold(
    @ServiceId() serviceId: string,
    @UuidParam('fileTypeId') fileTypeId: string,
    @UuidParam('thresholdId') thresholdId: string,
    @Body() data: BillingFileTypeThresholdUpdateDto,
    @CurrentUser() user: User
  ): Promise<BillingFileTypeThreshold> {
    const threshold = await this.getFileTypeThreshold(serviceId, fileTypeId, thresholdId, user);
    Object.assign(threshold, data);
    return this.entityManager.save(threshold);
  }

  /**
   * Delete billing file type threshold by ID
   */
  @Delete(':fileTypeId/thresholds/:thresholdId')
  @Authorize(BillingFileTypeWrite)
  @HttpCode(204)
  async deleteFileTypeThreshold(
    @ServiceId() serviceId: string,
    @UuidParam('fileTypeId') fileTypeId: string,
    @UuidParam('thresholdId') thresholdId: string,
    @CurrentUser() user: User
  ): Promise<void> {
    const threshold = await this.getFileTypeThreshold(serviceId, fileTypeId, thresholdId, user);
    await this.entityManager.remove(threshold);
  }

  /**
   * Return billing file type imports
   */
  @Get(':fileTypeId/imports')
  @Authorize(BillingFileImportRead)
  async getFileTypeImports(
    @ServiceId() serviceId: string,
    @UuidParam('fileTypeId') fileTypeId: string,
    @Query() params: BillingFileTypeImportsQueryParams = {}
  ): Promise<BillingFileImport[]> {
    const query = this.entityManager
      .createQueryBuilder(BillingFileImport, 'import')
      .innerJoinAndSelect('import.file', 'file')
      .innerJoin('file.vendor', 'vendor')
      .where('file.typeId = :fileTypeId', { fileTypeId })
      .andWhere('vendor.serviceId = :serviceId', { serviceId })
      .orderBy('import.finishDate');

    return getCollection(query, params, {
      filter: {
        fromPeriod: (fromPeriod) => {
          query.andWhere('file.period >= :fromPeriod', { fromPeriod });
        },
        toPeriod: (toPeriod) => {
          query.andWhere('file.period <= :toPeriod', { toPeriod });
        },
        accountId: (accountId) => {
          query.andWhere('file.accountId = :accountId', { accountId });
        }
      },
      sort: {
        period: 'file.period'
      }
    });
  }
}

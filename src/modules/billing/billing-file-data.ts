import { Observable } from 'rxjs';
import { type Class } from '~/common/utils/class';
import { BillingFile } from './billing-file';
import { BillingDataValidators } from './billing-file-data-validators';

type TransformFn = (value: string) => string;

export interface BillingFileDataTranslateValue {
  field: string;
  // conditional translation when callback returns true, otherwise original value is used
  when?: (value: string, row: any) => boolean;
  // transform value before translation
  transform?: TransformFn;
}

export interface BillingFileData<T = any> {
  /**
   * Source - array or observable stream
   */
  source: T[] | Observable<T>;

  /**
   * Type
   * @example invoices, charges
   */
  type: string | Class<T>;

  /**
   * Allow data to be empty, otherwise throw an exception
   */
  allowEmpty?: boolean;

  /**
   * Data fields - by default they will be extracted from the first row
   */
  fields?: string[];

  /**
   * Translate data keys into English, mostly from non-latin characters (eg. Japanese), as { sourceText: translatedText }
   */
  translateKeys?: Record<string, string>;

  /**
   * Translate values of given keys into English
   */
  translateValues?: Array<string | BillingFileDataTranslateValue>;

  /**
   * Row filter
   */
  filter?: string | ((row: T) => any);

  /**
   * Validators
   */
  validate?: BillingDataValidators<T>;

  /**
   * Field aliases
   */
  aliases?: Record<string, string>;

  /**
   * Transform value for selected fields
   */
  transform?: Record<string, TransformFn> | ((row: T) => T);

  /**
   * Real billing cycle parsed from data (if available). Overwrites cycle specified by file type.
   */
  cycle?: [Date, Date];

  /**
   * Data sheet name (in case of XLSX)
   */
  sheet?: string;

  /**
   * Custom callback after import
   */
  after?: (file: BillingFile) => any;
}

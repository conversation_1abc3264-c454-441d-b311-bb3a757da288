CREATE FUNCTION billing_files_storage_delete_trigger()
<PERSON><PERSON><PERSON><PERSON> trigger AS
$$
BEGIN
  -- schedule processed file deletion from storage
  IF (OLD.status = 'processed') THEN
    INSERT INTO jobs.job (name, data) VALUES ('Billing:Files:BillingFileStorage.deleteFile', to_json(OLD.id));
  END IF;

  RETURN OLD;
END;
$$
LANGUAGE plpgsql;

CREATE TRIGGER billing_files_storage_delete_trigger
AFTER DELETE ON billing_files
FOR EACH ROW EXECUTE FUNCTION billing_files_storage_delete_trigger();

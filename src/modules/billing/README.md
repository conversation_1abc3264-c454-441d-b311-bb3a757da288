# Billing module

## Billing dokumenty

Jedná se o přijaté dokumenty od vendorů - faktury, credit notes a one-off charges.

- <PERSON><PERSON><PERSON><PERSON> [dokument](./billing-document-entity.ts) definuje kromě [typu](./billing-document-type.ts) svoje identifikační <PERSON>, datum v<PERSON>, ob<PERSON><PERSON><PERSON> (billing cycle), odpovídající sumu/náklady a může mít několik příloh ([souborů](../files/common/billing-file-entity.ts)).
- Většinou se jedná o dokumenty vendorů, kte<PERSON><PERSON> nejsou nebo nejdou onboardovat anebo jsou onboardování částe<PERSON>ně (partially onboarded)
- Některé dokumenty/faktury jsou důležité pro výpočet skutečných celkových nákladů, např. Švýcarsko (Swisscom). U těchto vendorů se náklady nepočítají z nahraných raw souborů, ale právě z nákladů zadaných v dokumentu.

### Administrace dokumentů

Dokumenty je možné spravovat v Insights sekci [Billing > Documents](https://tem.novartis.net/insights/billing/documents)

## Billing model

### Zpracování raw dat vendorů

Raw data získané ze [souborů](../files/README.md) se v aplikační databázi (PostgreSQL) zpracovávají ze schémat jednotlivých vendorů (např. `cz_vf`) do unifikované struktury ve schématu `reports`.

Toto schéma pak umožňuje s daty vendorů jednotně pracovat, slouží jako základ pro další funkčnosti aplikace (např. [kampaně](../../modules/campaigns/README.md)) a zejména jako zdroj dat pro QlikSense a [Insights](../../modules/insights/README.md) reporty.

Zpracování raw dat probíhá jednou denně v 1:00 pomocí služby [BillingAnalyzer](./billing-analyzer.ts).

1. Služba zjistí, které raw soubory vendorů byly v daný den nahrané a nejsou zatím zpracované.
2. Pro tyto vendory se spustí procedura `recreate()` ve vendor schématu, např. `cz_vf.recreate()`. Každé vendor schéma musí implementovat tuto proceduru.
3. Služba nastaví po zpracování odpovídajícím raw souborům vendora ([BillingFileEntity](../files/common/billing-file-entity.ts)) položku `analyzeDate` na aktuální datum, pro identifikaci kdy došlo ke zpracování.
4. Spustí se procedura `reports.finalize_recreate()`, která provede finalizaci procesu.

Kód zpracování raw dat (tzn. procedury a tabulky ve vendor schématech, schéma `reports`) prozatím nejsou součástí kódu aplikace. Více informací poskytne Petr Navrátil.

### Load dat do QlikSense

Load dat do QlikSense se pouští jednou denně ve 4:00 pomocí QlikSense task manageru. Kód této fáze prozatím není součástí kódu aplikace. Více informací poskytne Petr Navrátil.

import { basename, dirname } from 'path';
import { ActionBus } from '~/common/actions';
import { canExtractFile, extractFile } from '~/common/compression';
import { EntityManager, In, IsNotNull, IsNull } from '~/common/database';
import { EventBus } from '~/common/events';
import { getFileFormat } from '~/common/files';
import { Provider } from '~/common/providers';
import { sortBy } from '~/common/utils/collection';
import { levenshtein } from '~/common/utils/string';
import { isDefined } from '~/common/utils/value';
import { BillingDocumentParser } from './billing-document-parser';
import { BillingFile } from './billing-file';
import { ParseBillingFile } from './billing-file-actions';
import { BillingFileDataError } from './billing-file-data-error';
import { BillingFileImporter } from './billing-file-importer';
import { BillingFileParseResult } from './billing-file-parser';
import { BillingFilePipeline } from './billing-file-pipeline';
import { BillingFileStageHandler } from './billing-file-stage-handler';
import { BillingFileStageName } from './billing-file-stage-name';
import { BillingFileStatus } from './billing-file-status';
import { BillingFileType } from './billing-file-type';
import { createBillingFileName } from './billing-file-utils';

@Provider()
export class BillingFileProcessor {
  constructor(
    private entityManager: EntityManager,
    private actionBus: ActionBus,
    private pipeline: BillingFilePipeline,
    private importer: BillingFileImporter,
    private parser: BillingDocumentParser,
    private eventBus: EventBus
  ) {}

  /**
   * Execute "process" billing file stage
   */
  @BillingFileStageHandler(BillingFileStageName.Process)
  async processFile(fileId: string, path: string, options: { password?: string } = {}) {
    const file = await this.entityManager.findOneOrFail(BillingFile, {
      where: { id: fileId },
      relations: ['type', 'vendor', 'account']
    });
    this.eventBus.verbose('processing', 'Processing file', file);

    const { type } = file;
    if (type) {
      const name = createBillingFileName(type.name, { period: file.period, account: file.account });
      const format = getFileFormat(name);
      const { password } = options;

      // uploaded file is an archive, but the expected file is not => extract the file
      if (canExtractFile(path) && !canExtractFile(name)) {
        const extracted = await this.extractFile(path, { password });
        path = extracted[0]; // fallback to first file by default

        // map multiple files in the archive to expected types
        if (extracted.length > 1) {
          const accountId = file.account?.id;
          const mapped = await this.mapTypes(file.vendorId, file.period, extracted, { accountId });
          for (const m of mapped) {
            if (m.typeId === type.id && m.accountId === accountId) {
              // current type => proceed
              path = m.path;
            } else {
              // forward other files to their pipeline
              await this.forwardFile(file.vendorId, file.period, m.typeId, m.path, { accountId: m.accountId });
            }
          }
        }
      }

      // validate file type format by file extension
      const pathFormat = getFileFormat(path);
      if (format.length && pathFormat !== format) {
        throw new BillingFileDataError('Invalid file format', { expected: format, received: pathFormat });
      }

      // parse file
      if (type.parserId) {
        const parsedData: BillingFileParseResult = await this.actionBus.execute(ParseBillingFile, {
          parserId: type.parserId,
          config: type.parserConfig ?? undefined,
          path,
          directory: dirname(path),
          password: password ?? undefined,
          type: type.refId ?? type.documentType!,
          file: {
            name: file.name,
            vendorId: file.vendorId,
            vendorRefId: file.vendor!.refId,
            accountRefId: file.account?.refId ?? undefined,
            period: file.period,
            periodThreshold: file.vendor!.periodThreshold ?? undefined,
            cycleStartDate: file.cycleStartDate!,
            cycleEndDate: file.cycleEndDate!
          }
        });

        if (parsedData.length) {
          for (const item of parsedData) {
            if ('path' in item) {
              // parser found an additional expected file type => forward to its pipeline (eg. when zip contains multiple file types)
              const { vendorId, accountId, period } = file;
              const type = await this.entityManager
                .createQueryBuilder(BillingFileType, 'type')
                .where('type.vendorId = :vendorId', { vendorId })
                .andWhere('type.refId = :refId', { refId: item.type })
                .getOne();
              if (type) {
                await this.forwardFile(vendorId, period, type.id, item.path, { accountId });
              }
            } else {
              // import parsed data
              await this.importer.importData(file, item);
            }
          }
        } else {
          throw new Error('No parsed data');
        }
      }

      file.name = basename(path);
      await this.entityManager.save(file);
    }

    this.eventBus.info('processed', 'File processed', file);

    return path;
  }

  /**
   * Extract files from archive (including all nested archives)
   */
  private async extractFile(path: string, options: { password?: string } = {}) {
    const files: string[] = [];

    const extract = async (path: string) => {
      if (canExtractFile(path)) {
        this.eventBus.verbose('extracting', 'Extracting file', { path: basename(path) });
        const extracted = await extractFile(path, dirname(path), { password: options.password });
        await Promise.all(extracted.map(extract));
      } else {
        files.push(path);
      }
    };

    await extract(path);
    return files;
  }

  /**
   * Map files to expected types
   */
  private async mapTypes(vendorId: string, period: string, files: string[], options: { accountId?: string } = {}) {
    const { accountId } = options;
    const expected = await this.entityManager.find(BillingFile, {
      where: { vendorId, period, typeId: IsNotNull() },
      relations: ['account']
    });

    function findType(path: string) {
      const name = basename(path);
      if (name.startsWith('.')) {
        // ignore files that starts with a dot (eg. hidden files)
        return;
      }

      const [closest] = sortBy(
        // files of the same format
        expected.filter((file) => getFileFormat(file.name) === getFileFormat(path)),
        (file) => {
          // exact match
          if (file.name === name) {
            return 0;
          }

          // file mask match
          const regexp = new RegExp(
            `^${file.name
              .replaceAll('.', '\\.') // escape dots
              .replaceAll(/[X]{2,}/g, '(.+)') // replace double-X (or more) with any characters
              .replaceAll(/[?]+/g, '(.+)')}$`, // replace ? with any characters
            'i'
          );
          if (regexp.test(name)) {
            return 0.1 + (file.accountId === accountId ? 0 : 0.1);
          }

          // similarity
          return levenshtein(name, file.name);
        }
      );
      if (closest) {
        return closest.typeId;
      }
    }

    this.eventBus.verbose('mappingTypes', 'Mapping file types', { files: files.length });
    const result = await Promise.all(
      files.map(async (path) => {
        const typeId = findType(path);
        if (typeId) {
          return {
            path,
            typeId,
            accountId: await this.mapFileAccount(path, typeId)
          };
        } else {
          return null;
        }
      })
    );
    return result.filter(isDefined);
  }

  private async mapFileAccount(path: string, typeId: string) {
    const type = await this.entityManager.findOneByOrFail(BillingFileType, { id: typeId });
    if (type.documentType) {
      this.eventBus.verbose('parsingAccount', 'Parsing file account', { path: basename(path) });
      const document = await this.parser.createDocument(type.vendorId, path, {
        config: { ...type.parserConfig, pages: '1', items: false },
        defaults: { type: type.documentType }
      });
      return document.accountId;
    }
  }

  /**
   * Forward file to a expected file pipeline
   */
  private async forwardFile(
    vendorId: string,
    period: string,
    typeId: string,
    path: string,
    options: { accountId?: string } = {}
  ) {
    const file = await this.entityManager.findOneBy(BillingFile, {
      vendorId,
      period,
      typeId,
      accountId: options.accountId ?? IsNull(),
      status: In([BillingFileStatus.Expected, BillingFileStatus.Failed])
    });
    if (file) {
      await this.pipeline.process(file.id, path);
    }
  }
}

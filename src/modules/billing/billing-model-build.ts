import { ManyToOne, Optional, PrimaryGenerated, Relation, Required, Table } from '~/common/database/entity';
import { type ErrorPayload } from '~/common/errors';
import { Vendor } from '~/common/vendors/vendor';
import { BillingModelBuildStatus, billingModelBuildStatuses } from './billing-model-build-status';

@Table('billing_model_builds', { events: true })
export class BillingModelBuild {
  /**
   * Build ID
   * @example c5e87f8f-0fa9-4f5f-8ae4-7e9e20e3ef27
   */
  @PrimaryGenerated('uuid')
  id: string;

  /**
   * Service ID
   * @example mobile
   */
  @Required()
  serviceId: string;

  /**
   * Vendor ID
   * @example ecfcca0d-5e1c-49ce-be5a-8aaf337114aa
   */
  @Required()
  vendorId: string;

  /**
   * Vendor
   */
  @ManyToOne(() => Vendor, 'vendorId')
  vendor?: Relation<Vendor>;

  /**
   * Period
   * @example 2025-01
   */
  @Required()
  period: string;

  /**
   * Created date
   */
  @Required()
  createdDate: Date = new Date();

  /**
   * Start date
   */
  @Optional()
  startDate?: Date;

  /**
   * Finish date
   */
  @Optional()
  finishDate?: Date;

  /**
   * Is partial build? (not all expected data files are processed yet)
   */
  @Required()
  isPartial: boolean;

  /**
   * Status
   * @example success
   */
  @Required({ enum: BillingModelBuildStatus, list: billingModelBuildStatuses })
  status: BillingModelBuildStatus = BillingModelBuildStatus.Pending;

  /**
   * Error details
   * @example { "type": "BillingFileDataError", "message": "Expected period is September 2022, but received August 2022" }
   */
  @Optional({ type: Object })
  error?: ErrorPayload;
}

import { List } from '~/common/lists';
import { BillingDocument } from './billing-document';

export type BillingDocumentParserField = keyof Pick<
  BillingDocument,
  'refId' | 'accountId' | 'poRefId' | 'netAmount' | 'otherAmount' | 'taxAmount' | 'totalAmount' | 'connectionsCount'
>;

export const billingDocumentParserFields = new List<BillingDocumentParserField>({
  refId: 'Document ID',
  accountId: 'Account ID',
  poRefId: 'PO number',
  netAmount: 'Net amount',
  otherAmount: 'Other amount',
  taxAmount: 'Tax amount',
  totalAmount: 'Total amount',
  connectionsCount: 'Connections'
});

export interface BillingDocumentParserConfig {
  locale?: string;
  items?: boolean;
  pages?: string;
  fields?: Partial<Record<BillingDocumentParserField, string>>;
  currency?: string;
}

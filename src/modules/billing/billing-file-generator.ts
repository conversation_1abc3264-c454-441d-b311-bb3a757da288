import { create, EntityManager } from '~/common/database';
import { InvalidArgumentError } from '~/common/errors';
import { EventBus } from '~/common/events';
import { everyDayAt, Worker } from '~/common/jobs';
import { formatPeriod, getCurrentPeriod, getEntityPeriodCycle, isValidPeriod } from '~/common/periods';
import { Provider } from '~/common/providers';
import { omitBy } from '~/common/utils/object';
import { isNil } from '~/common/utils/value';
import { isVendorAccountValid, isVendorValid, VendorAccount } from '~/common/vendors';
import { BillingFile } from './billing-file';
import { BillingFileType } from './billing-file-type';
import { getBillingFileTypeExpectedDate, isBillingFileTypeValid } from './billing-file-type-utils';
import { createBillingFileName } from './billing-file-utils';

@Provider()
export class BillingFileGenerator {
  /**
   * @param entityManager
   * @param eventBus
   */
  constructor(
    private entityManager: EntityManager,
    private eventBus: EventBus
  ) {}

  /**
   * Generate expected billing files for a given period
   */
  @Worker({
    description: 'Generate billing files for a given period',
    schedule: everyDayAt(1)
  })
  async generateFiles(
    options: {
      period?: string;
      serviceId?: string;
      vendorId?: string;
      typeId?: string;
    } = {}
  ) {
    const period = options.period ?? getCurrentPeriod();

    // validate period
    if (!isValidPeriod(period)) {
      throw new InvalidArgumentError('Invalid period');
    }
    if (period > getCurrentPeriod()) {
      throw new InvalidArgumentError('Period in the future is not supported');
    }

    this.eventBus.verbose('generating', 'Generating billing files', options);

    const fileTypesQuery = this.entityManager
      .createQueryBuilder(BillingFileType, 'type')
      .innerJoinAndSelect('type.vendor', 'vendor')
      .leftJoinAndSelect('vendor.accounts', 'accounts');

    if (options.typeId) {
      // requested type only
      fileTypesQuery.whereInIds([options.typeId]);
    } else if (options.vendorId) {
      // requested vendor only
      fileTypesQuery.where('vendor.id = :vendorId', { vendorId: options.vendorId });
    } else if (options.serviceId) {
      // requested service only
      fileTypesQuery.where('vendor.serviceId = :serviceId', { serviceId: options.serviceId });
    }

    const [fileTypes, accounts] = await Promise.all([fileTypesQuery.getMany(), this.entityManager.find(VendorAccount)]);

    let count = 0;

    for (const fileType of fileTypes) {
      // skip when invalid period
      if (!isBillingFileTypeValid(fileType, period)) {
        continue;
      }

      const vendor = fileType.vendor!;

      // skip when vendor is invalid
      if (!isVendorValid(vendor, period)) {
        continue;
      }

      // file per account
      let fileTypeAccounts: VendorAccount[];
      if (fileType.accounts) {
        // selected accounts only
        fileTypeAccounts = accounts.filter((account) => (<string[]>fileType.accounts).includes(account.refId));
      } else if (fileType.perAccount) {
        if (vendor.accounts?.length) {
          // all vendor accounts
          fileTypeAccounts = vendor.accounts;
        } else {
          fileTypeAccounts = [];
        }

        if (!fileTypeAccounts.length) {
          throw new Error(`Vendor "${vendor.id}" doesn't have accounts required by file type "${fileType.id}"`);
        }
      } else {
        // empty account
        fileTypeAccounts = [new VendorAccount()];
      }

      for (const account of fileTypeAccounts) {
        // skip when given period is in period range
        // eg. skip 2020-02 when file type "2 months" periods (2020-01, 2020-03)
        if (fileType.cycleDuration && fileType.cycleDuration > 1) {
          const periodMonth = parseInt(formatPeriod(period, 'MM'));
          if ((periodMonth - 1) % fileType.cycleDuration) {
            continue;
          }
        }

        const accountId = account.id;
        const typeId = fileType.id;
        let file = await this.entityManager.findOne(BillingFile, {
          where: omitBy({ vendorId: vendor.id, period, typeId, accountId }, isNil)
        });

        // skip when file already exists
        if (file) {
          continue;
        }

        // skip when account is invalid
        if (!isVendorAccountValid(account, period)) {
          continue;
        }

        try {
          // calculate billing cycle
          const cycle = getEntityPeriodCycle([fileType, account, vendor], period);
          file = create(BillingFile, {
            vendorId: vendor.id,
            accountId,
            period,
            typeId: fileType.id,
            name: createBillingFileName(fileType.name, { period, account }),
            description: fileType.description,
            cycleStartDate: cycle.startDate,
            cycleEndDate: cycle.endDate,
            expectedDate: getBillingFileTypeExpectedDate(fileType, cycle)
          });

          await this.entityManager.save(file);
          count++;
          this.eventBus.info('generated', 'Billing file generated', { ...file });
        } catch (e) {
          this.eventBus.error('error', e);
        }
      }
    }

    this.eventBus.info('generated', 'Billing files generated', { ...options, count });
  }
}

import { Modu<PERSON> } from '~/common/modules';
import { BillingDocumentParser } from './billing-document-parser';
import { BillingDocumentParserAgent } from './billing-document-parser-agent';
import { BillingDocumentsApiController } from './billing-documents-api-controller';
import { BillingFileArchiver } from './billing-file-archiver';
import { BillingFileChecker } from './billing-file-checker';
import { BillingFileDownloadManager } from './billing-file-download-manager';
import { BillingFileEmailDownloader } from './billing-file-email-downloader';
import { BillingFileGenerator } from './billing-file-generator';
import { BillingFileImporter } from './billing-file-importer';
import { BillingFilePipeline } from './billing-file-pipeline';
import { BillingFileProcessor } from './billing-file-processor';
import { BillingFileStorage } from './billing-file-storage';
import { BillingFileTypesApiController } from './billing-file-types-api-controller';
import { BillingFilesApiController } from './billing-files-api-controller';
import { BillingModelBuilder } from './billing-model-builder';

@Module({
  controllers: [BillingDocumentsApiController, BillingFileTypesApiController, BillingFilesApiController],
  providers: [
    BillingDocumentParser,
    BillingDocumentParserAgent,
    BillingFileArchiver,
    BillingFileChecker,
    BillingFileDownloadManager,
    BillingFileEmailDownloader,
    BillingFileGenerator,
    BillingFileImporter,
    BillingFilePipeline,
    BillingFileProcessor,
    BillingFileStorage,
    BillingModelBuilder
  ]
})
export class BillingModule {}

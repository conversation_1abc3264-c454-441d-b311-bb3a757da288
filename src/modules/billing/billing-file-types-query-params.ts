import { CollectionQueryType } from '~/common/api/collection-query-type';
import { Optional } from '~/common/schema/property';
import { BillingFileImport } from './billing-file-import';
import { BillingFileType } from './billing-file-type';

export class BillingFileTypesQueryParams extends CollectionQueryType(BillingFileType, [
  'id',
  'vendorId',
  'sourceType'
]) {
  /**
   * Has assigned parser?
   */
  @Optional()
  hasParser?: boolean;

  /**
   * Has assigned downloader?
   */
  @Optional()
  hasDownloader?: boolean;
}

export class BillingFileTypeImportsQueryParams extends CollectionQueryType(BillingFileImport, ['dataset']) {
  /**
   * File account ID
   */
  @Optional()
  accountId?: string;

  /**
   * Start period
   * @example 2022-03
   */
  @Optional()
  fromPeriod?: string;

  /**
   * End period
   * @example 2022-07
   */
  @Optional()
  toPeriod?: string;
}

CREATE FUNCTION billing_file_types_regenerate_files_trigger()
<PERSON><PERSON><PERSON><PERSON> trigger AS
$$
DECLARE
  r record;
BEGIN
  FOR r IN ( SELECT id, period FROM billing_files WHERE type_id = NEW.id AND status = 'expected' ) LOOP
    DELETE FROM billing_files WHERE id = r.id;
    INSERT INTO jobs.job (name, data) VALUES ('Billing:Files:BillingFileGenerator.generateFiles', json_build_object(
      'period', r.period,
      'typeId', NEW.id
    ));
  END LOOP;

  RETURN NEW;
END;
$$
LANGUAGE plpgsql;

CREATE TRIGGER billing_file_types_regenerate_files_trigger
AFTER UPDATE ON billing_file_types
FOR EACH ROW EXECUTE FUNCTION billing_file_types_regenerate_files_trigger();

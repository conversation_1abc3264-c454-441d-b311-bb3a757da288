import {
  ManyToOne,
  OneToMany,
  Optional,
  PrimaryGenerated,
  Required,
  Table,
  type Relation
} from '~/common/database/entity';
import { CycleEntity } from '~/common/periods/cycle-entity';
import { Vendor } from '~/common/vendors/vendor';
import { VendorAccount } from '~/common/vendors/vendor-account';
import { BillingDocumentItem } from './billing-document-item';
import { BillingDocumentType, billingDocumentTypes } from './billing-document-type';
import { BillingFile } from './billing-file';

declare module '~/common/services/service-settings' {
  interface ServiceSettingsValue {
    /** @deprecated */
    billingDocuments?: {
      withConnections: boolean;
    };
  }
}

@Table('billing_documents', { history: true, events: true })
export class BillingDocument implements CycleEntity {
  /**
   * Document ID
   * @example 9e054556-73d7-434f-aad1-b2a4866ebb20
   */
  @PrimaryGenerated('uuid')
  id: string;

  /**
   * Vendor ID
   * @example ecfcca0d-5e1c-49ce-be5a-8aaf337114aa
   */
  @Required()
  vendorId: string;

  /**
   * Vendor
   */
  @ManyToOne(() => Vendor, 'vendorId')
  vendor?: Relation<Vendor>;

  /**
   * Account ID
   * @example dca70d90-1f38-498d-bebf-dfc629bc0e8e
   */
  @Optional()
  accountId?: string;

  /**
   * Account
   */
  @ManyToOne(() => VendorAccount, 'accountId')
  account?: Relation<VendorAccount>;

  /**
   * Reference ID (eg. invoice number)
   * @example **********
   */
  @Optional()
  refId?: string;

  /**
   * Type
   * @example invoice
   */
  @Required({ list: billingDocumentTypes })
  type: BillingDocumentType;

  /**
   * Period
   * @example 2020-02
   */
  @Required()
  period: string;

  /**
   * Cycle start date
   * @example 2020-02-01
   */
  @Required()
  cycleStartDate: Date;

  /**
   * Cycle end date
   * @example 2020-02-29
   */
  @Required()
  cycleEndDate: Date;

  /**
   * Issue date
   * @example 2020-03-01
   */
  @Optional()
  issueDate?: Date;

  /**
   * Purchase order reference ID (aka PO number)
   */
  @Optional()
  poRefId?: string;

  /**
   * Mobile connections count
   * @example 10000
   */
  @Optional()
  connectionsCount?: number;

  /**
   * Currency
   * @example CZK
   */
  @Required()
  currency: string;

  /**
   * Net amount
   * @example 100000
   */
  @Optional()
  netAmount?: number;

  /**
   * Net amount in USD
   */
  @Optional({ isVirtual: true })
  netAmountUsd?: number;

  /**
   * Tax amount
   * @example 21000
   */
  @Optional()
  taxAmount?: number;

  /**
   * Other amount
   * @example 1000
   */
  @Optional()
  otherAmount?: number;

  /**
   * Total amount
   * @example 122000
   */
  @Optional()
  totalAmount?: number;

  /**
   * Total amount in USD
   */
  @Optional({ isVirtual: true })
  totalAmountUsd?: number;

  /**
   * USD rate
   * @example 25.5
   */
  @Optional({ isVirtual: true })
  usdRate?: number;

  /**
   * Created date
   */
  @Required()
  createdDate: Date;

  /**
   * Items
   */
  @OneToMany(() => BillingDocumentItem, 'document')
  items?: Relation<BillingDocumentItem>[];

  /**
   * Files
   */
  @OneToMany(() => BillingFile, 'document')
  files?: Relation<BillingFile>[];
}

<page-header />
<div class="container" [loader]="loader">
  <ng-template let-data>
    <form nz-form [formGroup]="form" class="mb-4">
      <h3>General</h3>
      <nz-card [nzBordered]="false" class="mb-4">
        <nz-form-item>
          <nz-form-label [nzSm]="3" [nzXs]="24" nzRequired>Vendor</nz-form-label>
          <nz-form-control [nzSm]="21" [nzXs]="24">
            <vendor-select formControlName="vendorId" (vendor)="onVendorChange($event, data)" />
          </nz-form-control>
        </nz-form-item>
        <nz-form-item [hidden]="form.controls.accountId.disabled">
          <nz-form-label [nzSm]="3" [nzXs]="24">Account</nz-form-label>
          <nz-form-control [nzSm]="21" [nzXs]="24">
            <vendor-account-select [vendorId]="form.value.vendorId" formControlName="accountId" />
          </nz-form-control>
        </nz-form-item>
        <nz-form-item>
          <nz-form-label [nzSm]="3" [nzXs]="24" nzRequired>Type</nz-form-label>
          <nz-form-control [nzSm]="21" [nzXs]="24">
            <nz-select formControlName="type" nzPlaceHolder="Select type" style="width: 9rem">
              @for (type of types; track type.id) {
                <nz-option [nzLabel]="type.name" [nzValue]="type.id" />
              }
            </nz-select>
          </nz-form-control>
        </nz-form-item>
        <nz-form-item>
          <nz-form-label [nzSm]="3" [nzXs]="24">Document ID</nz-form-label>
          <nz-form-control [nzSm]="21" [nzXs]="24">
            <input nz-input formControlName="refId" style="width: 16rem" />
          </nz-form-control>
        </nz-form-item>
        <nz-form-item>
          <nz-form-label [nzSm]="3" [nzXs]="24">PO number</nz-form-label>
          <nz-form-control [nzSm]="21" [nzXs]="24">
            <input nz-input formControlName="poRefId" style="width: 16rem" />
          </nz-form-control>
        </nz-form-item>
        <nz-divider />
        <nz-form-item>
          <nz-form-label [nzSm]="3" [nzXs]="24" nzRequired>Billing cycle</nz-form-label>
          <nz-form-control [nzSm]="21" [nzXs]="24">
            <nz-range-picker formControlName="cycle" nzFormat="mediumDate" />
          </nz-form-control>
        </nz-form-item>
        <nz-form-item>
          <nz-form-label [nzSm]="3" [nzXs]="24" nzRequired>Issue date</nz-form-label>
          <nz-form-control [nzSm]="21" [nzXs]="24">
            <nz-date-picker formControlName="issueDate" nzFormat="mediumDate" />
          </nz-form-control>
        </nz-form-item>
        @if (form.controls.connectionsCount.enabled) {
          <nz-divider />
          <nz-form-item>
            <nz-form-label [nzSm]="3" [nzXs]="24">Number of connections</nz-form-label>
            <nz-form-control [nzSm]="21" [nzXs]="24">
              <input nz-input type="number" formControlName="connectionsCount" style="width: 9rem" />
            </nz-form-control>
          </nz-form-item>
        }
      </nz-card>

      <h3>Charges</h3>
      <nz-card [nzBordered]="false" class="mb-4">
        <nz-form-item>
          <nz-form-label [nzSm]="3" [nzXs]="24" nzRequired>Currency</nz-form-label>
          <nz-form-control [nzSm]="21" [nzXs]="24">
            <currency-select formControlName="currency" />
          </nz-form-control>
        </nz-form-item>
        <nz-form-item>
          <nz-form-label [nzSm]="3" [nzXs]="24">Net amount</nz-form-label>
          <nz-form-control [nzSm]="21" [nzXs]="24">
            <nz-input-group [nzSuffix]="form.value.currency" style="width: 9rem">
              <input nz-input type="number" formControlName="netAmount" />
            </nz-input-group>
          </nz-form-control>
        </nz-form-item>
        <nz-form-item>
          <nz-form-label [nzSm]="3" [nzXs]="24">Tax amount</nz-form-label>
          <nz-form-control [nzSm]="21" [nzXs]="24">
            <nz-input-group [nzSuffix]="form.value.currency" style="width: 9rem">
              <input nz-input type="number" formControlName="taxAmount" />
            </nz-input-group>
          </nz-form-control>
        </nz-form-item>
        <nz-form-item>
          <nz-form-label [nzSm]="3" [nzXs]="24">Other charges</nz-form-label>
          <nz-form-control [nzSm]="21" [nzXs]="24">
            <nz-input-group [nzSuffix]="form.value.currency" style="width: 9rem">
              <input nz-input type="number" formControlName="otherAmount" />
            </nz-input-group>
          </nz-form-control>
        </nz-form-item>
        <nz-form-item>
          <nz-form-label [nzSm]="3" [nzXs]="24">Total</nz-form-label>
          <nz-form-control [nzSm]="21" [nzXs]="24">
            <nz-input-group [nzSuffix]="form.value.currency" style="width: 9rem">
              <input nz-input type="number" formControlName="totalAmount" />
            </nz-input-group>
          </nz-form-control>
        </nz-form-item>
      </nz-card>
      <h3>Files</h3>
      <nz-card [nzBordered]="false" class="mb-4">
        <billing-document-files-form [control]="form.controls.files" [value]="data.files" />
      </nz-card>
      <action-buttons [actions]="actions" />
    </form>
  </ng-template>
</div>

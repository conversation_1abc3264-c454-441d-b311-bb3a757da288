import { CommonModule } from '@angular/common';
import { ChangeDetectionStrategy, Component, Inject } from '@angular/core';
import { FormGroup, ReactiveFormsModule, UntypedFormControl, UntypedFormGroup } from '@angular/forms';
import { ActivatedRoute } from '@angular/router';
import { NZ_MODAL_DATA, NzModalService } from 'ng-zorro-antd/modal';
import { map } from 'rxjs';
import { DataSourceComponent } from '~/common/data-sources/data-source-component';
import { dataSourceTypes, isWebDataSourceType } from '~/common/data-sources/data-source-type';
import { create } from '~/common/database/entity';
import { PersonNamePipe } from '~/common/directory/person-name-pipe';
import { FileNameComponent } from '~/common/files/file-name-component';
import { ListItemComponent } from '~/common/lists/list-item-component';
import { ListSelectComponent } from '~/common/lists/list-select-component';
import { cycleDurations } from '~/common/periods/cycle-duration';
import { PeriodNameComponent } from '~/common/periods/period-name-component';
import { PeriodSelectComponent } from '~/common/periods/period-select-component';
import { getEntityPeriodCycle } from '~/common/periods/period-utils';
import { Action, ActionButtonsComponent } from '~/common/ui/actions';
import { CollectionColumn, CollectionPageComponent } from '~/common/ui/collections';
import { BooleanIconComponent } from '~/common/ui/components/boolean';
import { createFormControl, FormRouteSyncDirective, formRouteSyncWithSwitch, formValue$ } from '~/common/ui/forms';
import { createLoaderFrom, LoaderComponent } from '~/common/ui/loader';
import { NgZorroModule } from '~/common/ui/ng-zorro';
import { isDefined } from '~/common/utils/value';
import { VendorAccount } from '~/common/vendors/vendor-account';
import { VendorAccountNameComponent } from '~/common/vendors/vendor-account-name-component';
import { VendorNameComponent } from '~/common/vendors/vendor-name-component';
import { VendorSelectComponent } from '~/common/vendors/vendor-select-component';
import { ContactInfoComponent } from '~/modules/contacts/contact-info-component';
import { billingDocumentTypes } from './billing-document-type';
import { BillingFileWrite } from './billing-file-permissions';
import { BillingFileStatus } from './billing-file-status';
import { BillingFileType } from './billing-file-type';
import { billingFileTypeExpectedPeriodOffsets } from './billing-file-type-expected-period-offsets';
import { openBillingFileTypeForm } from './billing-file-type-form-component';
import { BillingFileTypeWrite } from './billing-file-type-permissions';
import { getBillingFileTypeExpectedDate, isBillingFileTypeValid } from './billing-file-type-utils';
import { BillingFileTypesApiClient } from './billing-file-types-api-client';
import { BillingFilesApiClient } from './billing-files-api-client';

@Component({
  selector: 'billing-file-types',
  imports: [
    CommonModule,
    ReactiveFormsModule,
    NgZorroModule,
    ActionButtonsComponent,
    BooleanIconComponent,
    CollectionPageComponent,
    ContactInfoComponent,
    DataSourceComponent,
    FileNameComponent,
    FormRouteSyncDirective,
    ListItemComponent,
    ListSelectComponent,
    PeriodNameComponent,
    PersonNamePipe,
    VendorAccountNameComponent,
    VendorNameComponent,
    VendorSelectComponent
  ],
  templateUrl: './billing-file-types-component.html',
  styleUrl: './billing-file-types-component.less',
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class BillingFileTypesComponent {
  loader = createLoaderFrom(
    this.route.queryParams,
    (params, { limit }) =>
      this.fileTypesRepository.getFileTypes({
        ...params,
        limit,
        expand: ['contacts', 'source', 'vendorAccounts']
      }),
    { limit: 20 }
  );

  /**
   * Filter
   */
  filter = new UntypedFormGroup({
    vendorId: new UntypedFormControl(),
    sourceType: new UntypedFormControl(),
    hasParser: new UntypedFormControl(false),
    hasDownloader: new UntypedFormControl(false)
  });

  filterRouteSync = formRouteSyncWithSwitch(['hasParser', 'hasDownloader']);

  /**
   * Global actions
   */
  globalActions: Action[] = [
    {
      icon: 'plus',
      label: 'Create',
      tooltip: 'Create file type',
      type: 'primary',
      authorize: BillingFileWrite,
      execute: () => openBillingFileTypeForm(this.modalService)
    }
  ];

  /**
   * File type actions
   */
  fileTypeActions: Action<BillingFileType>[] = [
    {
      icon: 'edit',
      tooltip: 'Edit file type',
      authorize: BillingFileTypeWrite,
      execute: (fileType) => openBillingFileTypeForm(this.modalService, { fileTypeId: fileType.id })
    },
    {
      icon: 'copy',
      menu: true,
      label: 'Duplicate',
      authorize: BillingFileTypeWrite,
      execute: (fileType) => openBillingFileTypeForm(this.modalService, { fileTypeId: fileType.id, clone: true })
    },
    {
      icon: 'file-search',
      menu: true,
      label: 'Parse existing documents',
      authorize: BillingFileTypeWrite,
      confirm: (fileType) => ({
        title: 'Are you sure you want to parse existing documents?',
        content: BillingFileTypeParseDialogComponent,
        data: { typeId: fileType.id }
      }),
      allowed: (fileType) => Boolean(fileType.documentType && fileType.parserId),
      execute: (fileType, dialog: BillingFileTypeParseDialogComponent) =>
        this.fileTypesRepository.parseFileTypeDocuments(fileType.id, dialog.form.value),
      success: () => ({
        message: 'Parsing has been initiated'
      })
    },
    {
      icon: 'delete',
      menu: true,
      label: 'Delete',
      confirm: () => ({
        title: 'Are you sure you want to delete this file type?'
      }),
      authorize: BillingFileTypeWrite,
      execute: (fileType) => this.fileTypesRepository.deleteFileType(fileType.id),
      success: () => ({
        message: 'File type deleted successfully'
      }),
      error: (error) => ({
        message: error.message
      })
    }
  ];

  // lookups
  cycleDurations = cycleDurations;
  sourceTypes = dataSourceTypes;
  documentTypes = billingDocumentTypes;
  expectedPeriodOffsets = billingFileTypeExpectedPeriodOffsets;

  columns: CollectionColumn[] = [
    { title: 'Vendor', width: '12rem' },
    { title: 'File name' },
    { title: 'Cycle start day', width: '6rem', align: 'center' },
    { title: 'Expected', width: '7rem', align: 'center' },
    { title: 'Source type', width: '10%', align: 'center' },
    { title: 'Parser', width: '5rem', align: 'center' },
    { title: 'Downloader', width: '5rem', align: 'center' },
    { width: '3rem' }
  ];

  constructor(
    private route: ActivatedRoute,
    private modalService: NzModalService,
    private fileTypesRepository: BillingFileTypesApiClient
  ) {}

  isDefined = isDefined;

  /**
   * Return list of accounts
   */
  getAccounts({ vendor, accounts }: BillingFileType) {
    return (accounts ?? []).map(
      (refId) => vendor!.accounts?.find((a) => a.refId === refId) ?? create(VendorAccount, { refId })
    );
  }

  isFileTypeValid = isBillingFileTypeValid;

  getFileTypeCycle(fileType: BillingFileType) {
    return getEntityPeriodCycle([fileType, fileType.vendor!]);
  }

  getFileTypeExpectedDate(fileType: BillingFileType) {
    return getBillingFileTypeExpectedDate(fileType, this.getFileTypeCycle(fileType));
  }

  isWebDataSourceType = isWebDataSourceType;
}

@Component({
  imports: [CommonModule, ReactiveFormsModule, NgZorroModule, LoaderComponent, PeriodSelectComponent],
  template: `
    <div [loader]="loader" class="py-3">
      <ng-template let-count>
        <nz-alert nzType="warning" [nzDescription]="warning">
          <ng-template #warning>
            This is a bulk operation that will be executed on <strong>{{ count }}</strong> documents.
          </ng-template>
        </nz-alert>
      </ng-template>
    </div>

    <div nz-form [formGroup]="form">
      Select period range if possible.
      <div class="mt-2">
        <period-select formControlName="periods" type="range" />
      </div>
    </div>
  `,
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class BillingFileTypeParseDialogComponent {
  form = new FormGroup({
    periods: createFormControl<string[]>()
  });

  loader = createLoaderFrom(formValue$(this.form), ({ periods }) =>
    this.filesApiClient
      .getFiles({ typeId: this.data.typeId, periods, status: BillingFileStatus.Processed })
      .pipe(map((files) => files.length))
  );

  constructor(
    @Inject(NZ_MODAL_DATA) private data: { typeId: string },
    private filesApiClient: BillingFilesApiClient
  ) {}
}

import { List } from '~/common/lists';

export enum BillingModelBuildStatus {
  Pending = 'pending',
  Started = 'started',
  Finished = 'finished',
  Error = 'error'
}

export const billingModelBuildStatuses = new List<BillingModelBuildStatus>({
  [BillingModelBuildStatus.Pending]: {
    name: 'Pending',
    icon: 'clock-circle'
  },
  [BillingModelBuildStatus.Started]: {
    name: 'Started',
    icon: 'loading',
    color: 'blue'
  },
  [BillingModelBuildStatus.Finished]: {
    name: 'Finished',
    icon: 'check-circle',
    color: 'green'
  },
  [BillingModelBuildStatus.Error]: {
    name: 'Error',
    icon: 'exclamation-circle',
    color: 'red'
  }
});

import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>Null, create } from '~/common/database';
import { EventBus } from '~/common/events';
import { Provider } from '~/common/providers';
import { BillingFile } from './billing-file';
import { BillingFileCheck } from './billing-file-check';
import { BillingFileDataError } from './billing-file-data-error';
import { BillingFileImport } from './billing-file-import';
import { BillingFileStageHandler } from './billing-file-stage-handler';
import { BillingFileStageName } from './billing-file-stage-name';
import { BillingFileType } from './billing-file-type';
import { BillingFileTypeThreshold } from './billing-file-type-threshold';

@Provider()
export class BillingFileChecker {
  constructor(
    private entityManager: EntityManager,
    private eventBus: EventBus
  ) {}

  @BillingFileStageHandler(BillingFileStageName.Check)
  async checkFile(fileId: string) {
    const file = await this.entityManager.findOneByOrFail(BillingFile, { id: fileId });

    // skip when no type is defined
    if (!file.typeId) {
      this.eventBus.verbose('skipped', 'Checking file skipped, no type defined', file);
      return;
    }

    this.eventBus.verbose('checking', 'Checking file', file);

    // load thresholds + imported records
    const [type, thresholds, imports] = await Promise.all([
      this.entityManager.findOneByOrFail(BillingFileType, { id: file.typeId }),
      this.entityManager.findBy(BillingFileTypeThreshold, {
        typeId: file.typeId,
        accountId: file.accountId ?? IsNull()
      }),
      this.entityManager.findBy(BillingFileImport, { fileId: file.id })
    ]);

    // delete previous checks
    this.entityManager.delete(BillingFileCheck, { fileId: file.id });

    for (const threshold of thresholds) {
      const datasets = type.datasets ?? [];
      const { dataset, minRecordsCount, maxRecordsCount } = threshold;
      const { recordsCount } = imports.find((i) => i.dataset === dataset) ?? { recordsCount: 0 };

      const toError = (message: string, expected: number) => {
        return new BillingFileDataError(
          `${message}${datasets.length > 1 ? ` for dataset "${dataset.toUpperCase()}"` : ''}`,
          { expected, received: recordsCount, dataset },
          {
            code: 'THRESHOLDS',
            hints: [
              'If this is expected, update the thresholds accordingly and try again.',
              'Otherwise contact the vendor or TEM team for further assistance.'
            ]
          }
        );
      };
      const error = (() => {
        if (minRecordsCount && recordsCount < minRecordsCount) {
          return toError('Minimum number of records not reached', minRecordsCount);
        } else if (maxRecordsCount && recordsCount > maxRecordsCount) {
          return toError('Maximum number of records exceeded', maxRecordsCount);
        } else {
          return null;
        }
      })();

      await this.entityManager.save(
        create(BillingFileCheck, {
          fileId: file.id,
          dataset,
          recordsCount,
          minRecordsCount,
          maxRecordsCount,
          passed: Boolean(error)
        })
      );

      if (error) {
        throw error;
      }
    }
  }
}

import { List } from '~/common/lists';

export enum BillingFileStatus {
  Expected = 'expected',
  Processing = 'processing',
  Processed = 'processed',
  Failed = 'failed'
}

export const billingFileStatuses = new List<BillingFileStatus>({
  [BillingFileStatus.Expected]: {
    name: 'Expected',
    icon: 'clock-circle'
  },
  [BillingFileStatus.Processing]: {
    name: 'Processing',
    icon: 'loading',
    color: 'blue'
  },
  [BillingFileStatus.Processed]: {
    name: 'Processed',
    icon: 'check-circle',
    color: 'green'
  },
  [BillingFileStatus.Failed]: {
    name: 'Failed',
    icon: 'exclamation-circle',
    color: 'red'
  }
});

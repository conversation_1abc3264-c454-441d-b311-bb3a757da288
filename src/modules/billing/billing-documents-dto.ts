import { Optional, PartialType, PickType, Required, UnionType } from '~/common/schema';
import { BillingDocument } from './billing-document';
import { type BillingDocumentParserConfig } from './billing-document-parser-config';
import { BillingFile } from './billing-file';

// document base properties
class BillingDocumentBaseCreateDto extends PickType(BillingDocument, [
  'vendorId',
  'accountId',
  'type',
  'refId',
  'poRefId',
  'issueDate',
  'cycleStartDate',
  'cycleEndDate',
  'connectionsCount',
  'currency',
  'netAmount',
  'taxAmount',
  'otherAmount',
  'totalAmount'
]) {}

// document files
class BillingDocumentFileCreateDto extends UnionType(
  PickType(BillingFile, ['name', 'description']),
  PartialType(PickType(BillingFile, ['id']))
) {
  /**
   * Upload ID
   * @example c5b264ec95c45bc9d4b54ca3839e0d1b
   */
  @Optional()
  uploadId: string;
}
class BillingDocumentFileUpdateDto extends PartialType(BillingDocumentFileCreateDto) {}

/***********/

export class BillingDocumentCreateDto extends BillingDocumentBaseCreateDto {
  @Optional({ type: BillingDocumentFileCreateDto })
  files?: BillingDocumentFileCreateDto[];
}

export class BillingDocumentUpdateDto extends PartialType(BillingDocumentBaseCreateDto) {
  @Optional({ type: BillingDocumentFileUpdateDto })
  files?: BillingDocumentFileUpdateDto[];
}

export class BillingDocumentParseDto {
  @Required()
  vendorId: string;

  @Optional()
  uploadId?: string;

  @Optional()
  fileId?: string;

  @Optional({ type: Object })
  config?: BillingDocumentParserConfig;
}

export class BillingDocumentDeleteDto {
  @Optional()
  withFiles?: boolean;
}

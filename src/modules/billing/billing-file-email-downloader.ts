import { omit } from 'lodash';
import { AIClient, functionTool } from '~/common/ai';
import { extractFormats } from '~/common/compression';
import { DataSource, DataSourceCredential, DataSourceType } from '~/common/data-sources';
import { EntityManager, EntityOperation, EntityTrigger } from '~/common/database';
import { type EmailAttachmentMetadata, EmailClient } from '~/common/email';
import { EventBus } from '~/common/events';
import { Provider } from '~/common/providers';
import { object, optional, string } from '~/common/schema';
import { uniq } from '~/common/utils/array';
import { max } from '~/common/utils/math';
import { toUpperCase } from '~/common/utils/string';
import { isDefined } from '~/common/utils/value';
import { BillingFileDownload } from './billing-file-download';
import { type BillingFileDownloadContext } from './billing-file-download-context';
import { type BillingFileDownloadResult } from './billing-file-download-result';
import { BillingFileDownloadStatus } from './billing-file-download-status';
import { BillingFileDownloader } from './billing-file-downloader';
import { BillingFileStatus } from './billing-file-status';
import { BillingFileType } from './billing-file-type';

const downloaderId = 'email';

type EmailSource = { mailbox: string; userId: string };

@Provider()
@BillingFileDownloader(downloaderId)
export class BillingFileEmailDownloader implements BillingFileDownloader<EmailSource> {
  constructor(
    private entityManager: EntityManager,
    private emailClient: EmailClient,
    private ai: AIClient,
    private eventBus: EventBus
  ) {}

  async initialize(credentials: DataSourceCredential, source: DataSource) {
    // use global email client without specific credentials
    return { mailbox: source.url, userId: credentials.username };
  }

  async downloadFile(
    { mailbox, userId }: EmailSource,
    { file, targetDir }: BillingFileDownloadContext
  ): Promise<BillingFileDownloadResult<EmailAttachmentMetadata>> {
    const skipped = (message: string) => ({ status: BillingFileDownloadStatus.Skipped, message });

    if (!file.typeId) {
      return skipped('No file type');
    }

    // find previous successful downloads from e-mail of a given file type
    const downloads = await this.getPreviousDownloads(file.typeId);
    if (!downloads.length) {
      return skipped('No previous downloads available');
    }

    // get new messages
    const messages = await this.emailClient.getMessages(userId, mailbox, {
      from: uniq(downloads.map((p) => p.from.address).filter(isDefined)),
      sentDate: max(downloads.map((p) => new Date(p.sendDate))),
      hasAttachments: true
    });
    if (!messages.length) {
      return skipped('No messages available');
    }

    // use AI agent to decide which attachment to download (or extract)
    const agent = this.createAgent(userId, mailbox);
    const response = await agent.execute([
      { title: 'Previous downloads', data: downloads.map((item) => omit(item, 'url')) },
      { title: 'New messages', data: messages.map((item) => omit(item, 'url')) },
      { title: 'Expected file name', data: file.name }
    ]);
    if (!response) {
      return { status: BillingFileDownloadStatus.Skipped, message: 'No attachment found' };
    }

    const { name, path, metadata } = await this.emailClient.downloadAttachment(
      targetDir,
      userId,
      mailbox,
      response.messageId,
      response.attachmentId,
      { extracted: response.extracted }
    );
    return { name, path, data: metadata };
  }

  private async getPreviousDownloads(typeId: string, accountId?: string) {
    const downloads = await this.entityManager.find(BillingFileDownload<EmailAttachmentMetadata>, {
      relations: ['file'],
      where: {
        file: {
          typeId,
          status: BillingFileStatus.Processed
        },
        sourceType: DataSourceType.Email,
        status: BillingFileDownloadStatus.Finished
      },
      order: {
        createdDate: 'DESC'
      }
    });
    return downloads
      .filter(({ file }) => (accountId ? file?.accountId === accountId || !file?.accountId : true))
      .map(({ data }) => data)
      .filter(isDefined);
  }

  private createAgent(userId: string, mailbox: string) {
    return this.ai.createAgent({
      model: 'gpt-4o',
      eventBus: this.eventBus.span('Agent'),
      instructions: [
        'You are operations analyst with a given task.',
        {
          title: 'Objective',
          text: 'Identify which email attachment should be downloaded based on past patterns.'
        },
        {
          title: 'General rules',
          items: [
            `Ensure the attachment's file format matches the expected type.`,
            `Extract all archives (${extractFormats.map(toUpperCase).join(', ')}) to identify the desired file.`
          ]
        },
        {
          title: 'Selection Criteria',
          items: [
            '*Recency:* Prioritize recent attachments.',
            '*Name Matching:* Compare with previous attachment names ("extracted" or "attachment").',
            '*Additional Hints:* Consider sender and subject similarity.',
            '*Match Certainty:* Choose attachments with names closest to past successful downloads.'
          ]
        },
        {
          title: 'Output Format',
          items: ['Respond using provided JSON structure', 'If unsure, output "null"']
        }
      ],
      output: object({
        messageId: string({ description: 'Message ID' }),
        attachmentId: string({ description: 'Attachment ID' }),
        attachmentName: string({ description: 'Attachment name' }),
        extracted: optional(
          string({
            description: 'Extracted file from attachment. Provide only when the file is from the extracted archive.'
          })
        )
      }),
      functions: [
        functionTool({
          name: 'extractAttachment',
          description: `Extract attachment from archive. Returns a list of extracted items.`,
          parameters: {
            messageId: string({ description: 'Message ID' }),
            attachmentId: string({ description: 'Attachment ID' })
          },
          execute: (a) => this.emailClient.extractAttachment(userId, mailbox, a.messageId, a.attachmentId)
        })
      ]
    });
  }

  /**
   * Set/reset downloader for email file type with linked email source
   */
  @EntityTrigger({
    entity: BillingFileType,
    before: [EntityOperation.Update],
    toArgs: ({ entity: fileType }) => [fileType]
  })
  setDownloader(fileType: BillingFileType) {
    if (fileType.sourceType === DataSourceType.Email) {
      fileType.downloaderId = fileType.sourceId ? (fileType.downloaderId ?? downloaderId) : null!;
    }
  }
}

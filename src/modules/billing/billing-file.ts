import {
  ManyTo<PERSON>ne,
  OneToMany,
  OneToOne,
  Optional,
  PrimaryGenerated,
  Required,
  Table,
  type Relation
} from '~/common/database/entity';
import { type ErrorPayload } from '~/common/errors';
import { CycleEntity } from '~/common/periods/cycle-entity';
import { Vendor } from '~/common/vendors/vendor';
import { VendorAccount } from '~/common/vendors/vendor-account';
import { BillingDocument } from './billing-document';
import { BillingFileCheck } from './billing-file-check';
import { BillingFileDownload } from './billing-file-download';
import { BillingFileStage } from './billing-file-stage';
import { BillingFileStatus, billingFileStatuses } from './billing-file-status';
import { BillingFileType } from './billing-file-type';

@Table('billing_files', { history: true, events: true })
export class BillingFile implements CycleEntity {
  /**
   * File ID
   * @example 89b11058-d4fe-42d5-ba2f-b02af4ff462c
   */
  @PrimaryGenerated('uuid')
  id: string;

  /**
   * Vendor ID
   * @example ecfcca0d-5e1c-49ce-be5a-8aaf337114aa
   */
  @Required()
  vendorId: string;

  /**
   * Vendor
   */
  @ManyToOne(() => Vendor, 'vendorId')
  vendor?: Relation<Vendor>;

  /**
   * Account ID
   * @example dca70d90-1f38-498d-bebf-dfc629bc0e8e
   */
  @Optional()
  accountId?: string;

  /**
   * Account
   */
  @ManyToOne(() => VendorAccount, 'accountId')
  account?: Relation<VendorAccount>;

  /**
   * Period
   * @example 2020-02
   */
  @Required()
  period: string;

  /**
   * Type ID
   * @example 3e85be98-c987-40e2-882a-ab69739420c6
   */
  @Optional()
  typeId?: string;

  /**
   * Type
   */
  @ManyToOne(() => BillingFileType, 'typeId')
  type?: Relation<BillingFileType>;

  /**
   * File name
   * @example vfV2InvDIR2_8377_XXXXXXXXXX_202002.xml
   */
  @Required()
  name: string;

  /**
   * Description
   * @example 'Combined invoice detail XML file'
   */
  @Optional()
  description?: string;

  /**
   * File size
   * @example 123456
   */
  @Optional()
  size?: number;

  /**
   * Status
   * @example processed
   */
  @Required({ list: billingFileStatuses })
  status: BillingFileStatus = BillingFileStatus.Expected;

  /**
   * Process error message
   * @example { "type": "BillingFileDataError", "message": "Expected period is September 2022, but received August 2022" }
   */
  @Optional({ type: Object })
  error?: ErrorPayload;

  /**
   * URL
   * @example https://tem.novartis.net/files/billing/89b11058-d4fe-42d5-ba2f-b02af4ff462c
   */
  @Optional()
  url?: string;

  /**
   * Created date
   * @example 2020-02-10
   */
  @Optional()
  createdDate?: Date;

  /**
   * Expected date
   * @example 2020-03-15
   */
  @Optional()
  expectedDate?: Date;

  /**
   * Cycle start date
   * @example 2020-02-01
   */
  @Optional()
  cycleStartDate?: Date;

  /**
   * Cycle end date
   * @example 2020-02-29
   */
  @Optional()
  cycleEndDate?: Date;

  /**
   * Document ID
   * @example 9e054556-73d7-434f-aad1-b2a4866ebb20
   */
  @Optional()
  documentId?: string;

  /**
   * Document
   */
  @ManyToOne(() => BillingDocument, 'documentId')
  document?: Relation<BillingDocument>;

  /**
   * Is file compressed in archive?
   * @example false
   * @deprecated This flag is going to be removed. All files are compressed in archive and this is just an implementation detail.
   */
  @Required({ default: false })
  compressed: boolean = false;

  /**
   * Download
   */
  @OneToOne(() => BillingFileDownload, 'file')
  download?: Relation<BillingFileDownload>;

  /**
   * Pipeline stages
   */
  @OneToMany(() => BillingFileStage, 'file')
  stages?: Relation<BillingFileStage>[];

  /**
   * Pipeline stages
   */
  @OneToMany(() => BillingFileCheck, 'file')
  checks?: Relation<BillingFileCheck>[];
}

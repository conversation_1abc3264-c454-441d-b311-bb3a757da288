<div [loader]="typeLoader">
  <ng-template let-type>
    <nz-tabset class="pt-0" [class.hide-tabs]="form.controls.length === 1" [nzSelectedIndex]="selectedIndex">
      @for (threshold of form.controls; track threshold) {
        <nz-tab [nzTitle]="threshold.value.dataset | uppercase">
          <form nz-form [formGroup]="threshold">
            <nz-form-item>
              <nz-form-label [nzSm]="10" [nzXs]="24">Minimum number of records</nz-form-label>
              <nz-form-control [nzSm]="14" [nzXs]="24">
                <input nz-input formControlName="minRecordsCount" type="number" style="width: 6rem" />
                <ng-container
                  [ngTemplateOutlet]="importedRecordsIcon"
                  [ngTemplateOutletContext]="{ $implicit: threshold.value.dataset }"
                />
              </nz-form-control>
            </nz-form-item>
            <nz-form-item>
              <nz-form-label [nzSm]="10" [nzXs]="24">Maximum number of records</nz-form-label>
              <nz-form-control [nzSm]="14" [nzXs]="24">
                <input nz-input formControlName="maxRecordsCount" type="number" style="width: 6rem" />
                <ng-container
                  [ngTemplateOutlet]="importedRecordsIcon"
                  [ngTemplateOutletContext]="{ $implicit: threshold.value.dataset }"
                />
              </nz-form-control>
            </nz-form-item>
          </form>
        </nz-tab>
      }
    </nz-tabset>

    <div class="text-center mt-4">
      <action-button [action]="submitAction" [context]="type" />
    </div>
  </ng-template>
</div>

<ng-template #importedRecordsIcon let-dataset>
  <span
    nz-icon
    nzType="file-search"
    nzTheme="outline"
    class="cursor-help ml-2"
    nz-popover
    nzPopoverTitle="Imported records"
    [nzPopoverContent]="importedRecordsList"
    nzPopoverPlacement="right"
    (nzPopoverVisibleChange)="$event && importsLoader.load(dataset)"
  ></span>
</ng-template>

<ng-template #importedRecordsList>
  <div [loader]="importsLoader">
    <ng-template let-imports>
      @if (imports?.length) {
        <nz-table [nzFrontPagination]="false" [nzShowPagination]="false" nzTemplateMode class="size-xs">
          <thead>
            <tr>
              <th nzWidth="8rem">Period</th>
              <th nzWidth="6rem">Records</th>
            </tr>
          </thead>
          <tbody>
            @for (i of imports; track i.file.period) {
              <tr>
                <td><period-name [period]="i.file.period" /></td>
                <td>{{ i.recordsCount | number }}</td>
              </tr>
            }
          </tbody>
        </nz-table>
      } @else {
        <p class="text-disabled">No data has been imported yet for this dataset.</p>
      }
    </ng-template>
  </div>
</ng-template>

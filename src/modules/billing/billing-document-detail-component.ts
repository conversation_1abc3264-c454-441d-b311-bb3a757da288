import { CommonModule } from '@angular/common';
import { ChangeDetectionStrategy, Component } from '@angular/core';
import { FormGroup, ReactiveFormsModule } from '@angular/forms';
import { BehaviorSubject, map, of, switchMap } from 'rxjs';
import { InvalidArgumentError } from '~/common/errors';
import { FileNameComponent } from '~/common/files/file-name-component';
import { CyclePipe } from '~/common/periods/cycle-pipe';
import { PeriodNameComponent } from '~/common/periods/period-name-component';
import { Action } from '~/common/ui/actions';
import { Drawer, DrawerService } from '~/common/ui/drawer';
import { createFormControl } from '~/common/ui/forms';
import { createLoader, LoaderComponent } from '~/common/ui/loader';
import { NgZorroModule } from '~/common/ui/ng-zorro';
import { getObjectMetadata, hasObjectMetadata } from '~/common/utils/object';
import { EntriesPipe } from '~/common/utils/object/entries-pipe';
import { TypeOfPipe } from '~/common/utils/value/typeof-pipe';
import { VendorAccountNameComponent } from '~/common/vendors/vendor-account-name-component';
import { VendorNameComponent } from '~/common/vendors/vendor-name-component';
import { BillingDocument } from './billing-document';
import { BillingDocumentItemsComponent } from './billing-document-items-component';
import { BillingDocumentWrite } from './billing-document-permissions';
import { billingDocumentTypes } from './billing-document-type';
import { BillingDocumentsApiClient } from './billing-documents-api-client';
import { BillingDocumentParseDto } from './billing-documents-dto';

interface BillingDocumentDetailData {
  documentId?: string;
  parse?: BillingDocumentParseDto;
}

export function openBillingDocument(
  drawerService: DrawerService,
  data: BillingDocumentDetailData,
  options: { title?: string } = {}
) {
  drawerService.create(options.title ?? 'Invoice', BillingDocumentDetailComponent, data, { key: data.documentId });
}

@Component({
  selector: 'billing-document-detail',
  imports: [
    CommonModule,
    NgZorroModule,
    BillingDocumentItemsComponent,
    CyclePipe,
    EntriesPipe,
    FileNameComponent,
    LoaderComponent,
    PeriodNameComponent,
    TypeOfPipe,
    VendorAccountNameComponent,
    VendorNameComponent
  ],
  templateUrl: './billing-document-detail-component.html',
  styles: [
    `
      :host ::ng-deep .ant-spin-spinning {
        margin-top: 2rem;
      }
    `
  ],
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class BillingDocumentDetailComponent {
  loader = createLoader<BillingDocument, BillingDocumentDetailData>(
    ({ documentId, parse }) => {
      if (documentId) {
        return this.getDocument(documentId);
      } else if (parse) {
        return this.documentsApiClient
          .parseDocument(parse)
          .pipe(switchMap((document) => (document.id ? this.getDocument(document.id) : of(document))));
      } else {
        throw new InvalidArgumentError('Missing document ID');
      }
    },
    {
      from: of(this.drawer.data),
      onData: (document) => {
        this.drawer.setTitle(billingDocumentTypes.get(document.type).name);

        // execute re-parse after document is loaded, so the original document is visible
        const { documentId, parse } = this.drawer.data;
        if (documentId && parse) {
          this.loader.load({ parse });
          delete this.drawer.data.parse;
        }
      }
    }
  );

  fields$ = this.loader.data$.pipe(
    map((document) => (hasObjectMetadata(document) && !document.id ? getObjectMetadata(document).fields : null))
  );

  /**
   * BillingDocument actions
   */
  actions: Action<BillingDocument>[] = [
    {
      label: 'Edit',
      icon: 'edit',
      type: 'primary',
      width: '6rem',
      authorize: BillingDocumentWrite,
      allowed: (document) => Boolean(document?.id),
      navigate: (document) => ({
        path: ['@service', 'billing/documents/edit', document.id]
      })
    },
    {
      label: 'Delete',
      icon: 'delete',
      type: 'dashed',
      danger: true,
      menu: true,
      authorize: BillingDocumentWrite,
      allowed: (document) => Boolean(document?.id),
      confirm: (document) => ({
        title: 'Are you sure you want to delete this document?',
        content: document.files?.length ? BillingDocumentDeleteComponent : undefined
      }),
      execute: (document, instance?: BillingDocumentDeleteComponent) =>
        this.documentsApiClient.deleteDocument(document.id, instance?.form.value),
      success: () => ({
        message: 'Document deleted',
        execute: () => this.drawer.close(true)
      }),
      error: () => ({
        message: 'Failed to delete document'
      })
    }
  ];

  amounts: Array<{
    field: 'netAmount' | 'taxAmount' | 'otherAmount' | 'totalAmount';
    label?: string;
    total?: boolean;
  }> = [
    { field: 'netAmount', label: 'Net amount' },
    { field: 'taxAmount', label: 'Tax amount ' },
    { field: 'otherAmount', label: 'Other charges' },
    { field: 'totalAmount', label: 'Total amount', total: true }
  ];

  items$ = new BehaviorSubject(false);

  constructor(
    readonly drawer: Drawer<BillingDocumentDetailData>,
    private documentsApiClient: BillingDocumentsApiClient
  ) {
    drawer.setActions(this.actions, this.loader.data$);
  }

  private getDocument(id: string) {
    return this.documentsApiClient.getDocument(id, { expand: ['items', 'files'] });
  }
}

@Component({
  imports: [ReactiveFormsModule, NgZorroModule],
  template: `
    <form nz-form [formGroup]="form">
      <label nz-checkbox formControlName="withFiles">Delete files</label>
    </form>
  `,
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class BillingDocumentDeleteComponent {
  form = new FormGroup({
    withFiles: createFormControl<boolean>(true)
  });
}

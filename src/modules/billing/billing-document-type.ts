import { List } from '~/common/lists';

export enum BillingDocumentType {
  Invoice = 'invoice',
  CreditNote = 'credit-note',
  OneOffCharge = 'one-off-charge'
}

export const billingDocumentTypes = new List<BillingDocumentType, { withConnections?: boolean }>({
  [BillingDocumentType.Invoice]: {
    name: 'Invoice',
    withConnections: true
  },
  [BillingDocumentType.CreditNote]: {
    name: 'Credit note'
  },
  [BillingDocumentType.OneOffCharge]: {
    name: 'One-off charge'
  }
});

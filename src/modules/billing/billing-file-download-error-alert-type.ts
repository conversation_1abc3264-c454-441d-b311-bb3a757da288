import { AlertType } from '~/modules/alerts/alert-type';

export interface BillingFileDownloadErrorAlertData {
  name: string;
  accountId?: string;
}

export const billingFileDownloadErrorAlertType: AlertType<BillingFileDownloadErrorAlertData> = {
  id: 'Billing:Download:Error',
  name: 'Download error',
  isPeriodical: true,
  details: () =>
    import('./billing-file-download-error-alert-details-component').then(
      (m) => m.BillingFileDownloadErrorAlertDetailsComponent
    )
};

import { readFile } from 'fs-extra';
import { AIClient, functionTool } from '~/common/ai';
import { type DocumentAnalyzeResult } from '~/common/azure/document-intelligence';
import { EventBus } from '~/common/events';
import { Provider } from '~/common/providers';
import { array, nullable, object, string } from '~/common/schema';
import { BillingDocumentParserField } from './billing-document-parser-config';

@Provider()
export class BillingDocumentParserAgent {
  constructor(
    private ai: AIClient,
    private eventBus: EventBus
  ) {}

  async evaluate(
    instructions: Record<BillingDocumentParserField, string>,
    context: { result: DocumentAnalyzeResult; filename?: string }
  ) {
    const { result, filename } = context;
    const agent = this.ai.createAgent({
      model: 'gpt-4o',
      eventBus: this.eventBus.span('Agent'),
      instructions: await readFile(__dirname + '/billing-document-parser-instructions.md', { encoding: 'utf-8' }),
      output: object({
        fields: array(
          object({
            field: string({ description: 'Field name provided in query' }),
            result: nullable(
              string({ description: 'Result for given field. Formatted by instructions if applicable.' }),
              { description: 'If unsure or answer is not found for a given field' }
            )
          })
        )
      }),
      functions: [
        functionTool({
          name: 'getInvoiceFilename',
          description: `Returns invoice filename`,
          execute: () => filename
        }),
        functionTool({
          name: 'getInvoiceData',
          description: `Returns parsed invoice data from Azure Document Intelligence`,
          execute: () => result
        })
      ]
    });

    return agent.execute({
      title: 'Fields',
      items: Object.entries(instructions).map(([field, instructions]) => `"${field}": "${instructions}"`)
    });
  }
}

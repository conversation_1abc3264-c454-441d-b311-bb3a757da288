import { ErrorType, GeneralErrorOptions, InvalidArgumentError } from '~/common/errors';
import { getCollectionItemMetadata, hasCollectionItemMetadata } from '~/common/utils/collection';

interface BillingFileDataErrorDetails {
  data?: any;
  field?: string;
  sheet?: string;
  row?: number;
  file?: string;
  expected?: string | number | boolean;
  received?: string | number | boolean;
  [key: string]: any;
}

@ErrorType('BillingFileDataError')
export class BillingFileDataError extends InvalidArgumentError<BillingFileDataErrorDetails> {
  static MissingField(field: string, data?: BillingFileDataErrorDetails) {
    return new BillingFileDataError(`Invalid data - missing required field "${field}"`, data);
  }

  constructor(
    message: string,
    readonly data: BillingFileDataErrorDetails = {},
    options?: GeneralErrorOptions
  ) {
    const { data: row } = data;

    if (row && hasCollectionItemMetadata(row)) {
      const metadata = getCollectionItemMetadata(row);
      data.sheet = metadata.group;
      data.row = metadata.row;
    }

    super(message, data, options);
  }
}

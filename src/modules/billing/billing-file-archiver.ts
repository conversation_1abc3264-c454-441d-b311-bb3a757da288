import { createReadStream, readFile, stat, writeFile } from 'fs-extra';
import { basename } from 'path';
import { createZip } from '~/common/compression';
import { EntityManager } from '~/common/database';
import { EventBus } from '~/common/events';
import { getFileFormat } from '~/common/files';
import { Provider } from '~/common/providers';
import { decryptXlsx } from '~/common/xlsx';
import { BillingFile } from './billing-file';
import { BillingFileStageHandler } from './billing-file-stage-handler';
import { BillingFileStageName } from './billing-file-stage-name';
import { BillingFileStorage } from './billing-file-storage';

@Provider()
export class BillingFileArchiver {
  constructor(
    private entityManager: EntityManager,
    private storage: BillingFileStorage,
    private eventBus: EventBus
  ) {}

  /**
   * Execute "archive" billing file stage
   */
  @BillingFileStageHandler(BillingFileStageName.Archive)
  async archiveFile(fileId: string, path: string, options: { password?: string } = {}) {
    const file = await this.entityManager.findOneByOrFail(BillingFile, { id: fileId });
    this.eventBus.verbose('archiving', 'Archiving file', file);

    // decrypt password protected file
    if (options.password) {
      await this.decryptFile(path, options.password);
    }

    // store into the archive as zip
    const name = basename(path);
    await this.storage.writeFile(file.id, createZip([{ source: createReadStream(path), name }]));

    // save name and url
    const stats = await stat(path);
    file.name = name;
    file.size = stats.size;
    file.url = this.storage.getFileUrl(file.id);
    file.compressed = true;
    await this.entityManager.save(file);

    this.eventBus.info('archived', 'File archived', file);
  }

  /**
   * Decrypt password protected file when saving to archive
   */
  private async decryptFile(path: string, password: string) {
    const data = await (async () => {
      switch (getFileFormat(path)) {
        case 'xlsx':
          return decryptXlsx(await readFile(path), password);

        default:
          return;
      }
    })();

    if (data) {
      await writeFile(path, data);
    }
  }
}

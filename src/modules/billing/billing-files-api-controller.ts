import { Authorize, CurrentUser, User, UuidParam, getCollection } from '~/common/api';
import { Body, Delete, Get, HttpCode, Patch, Post, Query } from '~/common/controllers';
import { EntityManager } from '~/common/database';
import { ForbiddenError, NotFoundError } from '~/common/errors';
import { JobBus } from '~/common/jobs';
import { ServiceApiController, ServiceId } from '~/common/services';
import { omit } from '~/common/utils/object';
import { BillingDocument } from './billing-document';
import { BillingFile } from './billing-file';
import { BillingFileCheck } from './billing-file-check';
import { BillingFileDownloadManager } from './billing-file-download-manager';
import { BillingFileGenerator } from './billing-file-generator';
import { BillingFileRead, BillingFileWrite } from './billing-file-permissions';
import { BillingFilePipeline } from './billing-file-pipeline';
import { createBillingFileQuery } from './billing-file-query';
import { BillingFileStage } from './billing-file-stage';
import { BillingFileStatus } from './billing-file-status';
import { canDeleteBillingFile, canResetBillingFile } from './billing-file-utils';
import { BillingFileCreateDto, BillingFileUpdateDto, BillingFilesGenerateDto } from './billing-files-dto';
import { BillingFilesQueryParams } from './billing-files-query-params';

@ServiceApiController('billing/files')
export class BillingFilesApiController {
  constructor(
    private entityManager: EntityManager,
    private pipeline: BillingFilePipeline,
    private generator: BillingFileGenerator,
    private jobBus: JobBus
  ) {}

  /**
   * Return billing files
   */
  @Get()
  @Authorize(BillingFileRead)
  async getFiles(
    @ServiceId() serviceId: string,
    @Query() params: BillingFilesQueryParams = {},
    @CurrentUser() user: User
  ): Promise<BillingFile[]> {
    const query = createBillingFileQuery(this.entityManager, { serviceId, user })
      .orderBy('file.period', 'DESC')
      .addOrderBy('vendor.countryId')
      .addOrderBy('vendor.name')
      .addOrderBy('account.refId')
      .addOrderBy('file.name')
      .addOrderBy('file.typeId');

    return getCollection(query, params, {
      search: [
        'name',
        'description',
        'vendor.name',
        'vendor.countryId',
        'country.name',
        'account.name',
        'account.refId',
        'document.refId'
      ],
      filter: {
        isOverdue: () => {
          query.andWhere('file.expectedDate < CURRENT_DATE');
          query.andWhere(`file.status != :processed`, { processed: BillingFileStatus.Processed });
        },
        isExpected: (value) => {
          query.andWhere(`file.typeId ${value ? 'IS NOT NULL' : 'IS NULL'}`);
        },
        hasDownload: () => {
          query.andWhere('(download.id IS NOT NULL OR (type.downloaderId IS NOT NULL AND file.status = :expected))', {
            expected: BillingFileStatus.Expected
          });
        },
        periods: ([fromPeriod, toPeriod]) => {
          query.andWhere('file.period BETWEEN :fromPeriod AND :toPeriod', { fromPeriod, toPeriod });
        }
      },
      load: {
        checks: async (files) => {
          const checks = await this.entityManager
            .createQueryBuilder(BillingFileCheck, 'check')
            .where('check.fileId IN (:...fileIds)', { fileIds: files.map((file) => file.id) })
            .orderBy('check.executedDate')
            .getMany();

          for (const file of files) {
            file.checks = checks.filter((check) => check.fileId === file.id) ?? [];
          }
        },
        stages: async (files) => {
          const stages = await this.entityManager
            .createQueryBuilder(BillingFileStage, 'stage')
            .where('stage.fileId IN (:...fileIds)', { fileIds: files.map((file) => file.id) })
            .orderBy('stage.createdDate')
            .getMany();

          for (const file of files) {
            file.stages = stages.filter((stage) => stage.fileId === file.id) ?? [];
          }
        }
      }
    });
  }

  /**
   * Create new file
   */
  @Post()
  @Authorize(BillingFileWrite)
  async createFile(@Body() data: BillingFileCreateDto, @CurrentUser() user: User): Promise<BillingFile> {
    return this.saveFile(new BillingFile(), data, { user });
  }

  /**
   * Generate expected files
   */
  @Post('generate')
  @Authorize(BillingFileWrite)
  @HttpCode(204)
  async generateFiles(@ServiceId() serviceId: string, @Body() data: BillingFilesGenerateDto): Promise<void> {
    await this.generator.generateFiles({ ...data, serviceId });
  }

  /**
   * Return billing file by ID
   */
  @Get(':fileId')
  @Authorize(BillingFileRead)
  async getFile(
    @ServiceId() serviceId: string,
    @UuidParam('fileId') fileId: string,
    @Query() params: BillingFilesQueryParams = {},
    @CurrentUser() user: User
  ): Promise<BillingFile> {
    const [file] = await this.getFiles(serviceId, { ...params, id: fileId }, user);
    if (file) {
      return file;
    } else {
      throw new NotFoundError('File not found');
    }
  }

  /**
   * Update billing file
   */
  @Patch(':fileId')
  @Authorize(BillingFileWrite)
  async updateFile(
    @ServiceId() serviceId: string,
    @UuidParam('fileId') fileId: string,
    @Body() data: BillingFileUpdateDto,
    @CurrentUser() user: User
  ): Promise<BillingFile> {
    const file = await this.getFile(serviceId, fileId, {}, user);
    return this.saveFile(file, data, { user });
  }

  /**
   * Delete billing file by ID
   */
  @Delete(':fileId')
  @HttpCode(204)
  @Authorize(BillingFileWrite)
  async deleteFile(
    @ServiceId() serviceId: string,
    @UuidParam('fileId') fileId: string,
    @CurrentUser() user: User
  ): Promise<void> {
    const file = await this.getFile(serviceId, fileId, {}, user);
    if (file) {
      if (canDeleteBillingFile(file)) {
        await this.entityManager.remove(file);
      } else {
        throw new ForbiddenError('Not allowed');
      }
    } else {
      throw new NotFoundError('File not found');
    }
  }

  /**
   * Return file pipeline (stages)
   */
  @Post(':fileId/download')
  @Authorize(BillingFileWrite)
  async downloadFile(
    @ServiceId() serviceId: string,
    @UuidParam('fileId') fileId: string,
    @CurrentUser() user: User
  ): Promise<void> {
    const file = await this.getFile(serviceId, fileId, {}, user);
    await this.jobBus.addJob([BillingFileDownloadManager, 'downloadFiles'], { id: file.id, userId: user.id });
  }

  /**
   * Return file pipeline (stages)
   */
  @Get(':fileId/pipeline')
  @Authorize(BillingFileRead)
  async getFilePipeline(
    @ServiceId() serviceId: string,
    @UuidParam('fileId') fileId: string,
    @CurrentUser() user: User
  ): Promise<BillingFileStage[]> {
    const { stages } = await this.getFile(serviceId, fileId, { expand: ['stages'] }, user);
    return stages!.map((stage) => ({
      ...stage,
      url: this.pipeline.getStageUrl(stage)
    }));
  }

  /**
   * Retry failed file pipeline
   */
  @Post(':fileId/pipeline/retry')
  @HttpCode(204)
  @Authorize(BillingFileRead)
  async retryFilePipeline(
    @ServiceId() serviceId: string,
    @UuidParam('fileId') fileId: string,
    @CurrentUser() user: User
  ): Promise<void> {
    const file = await this.getFile(serviceId, fileId, {}, user);
    await this.pipeline.retry(file.id);
  }

  /**
   * Reset processed file
   */
  @Post(':fileId/reset')
  @HttpCode(204)
  @Authorize(BillingFileWrite)
  async resetFile(
    @ServiceId() serviceId: string,
    @UuidParam('fileId') fileId: string,
    @CurrentUser() user: User
  ): Promise<void> {
    const file = await this.getFile(serviceId, fileId, {}, user);

    if (canResetBillingFile(file)) {
      const { period, typeId } = file;
      await this.entityManager.remove(file);
      await this.generator.generateFiles({ period, typeId });

      // delete document itself on last reset document file
      if (file.documentId) {
        const document = await this.entityManager.findOne(BillingDocument, {
          where: { id: file.documentId },
          relations: { files: true }
        });
        if (document && !document.files?.length) {
          await this.entityManager.remove(document);
        }
      }
    }
  }

  /**
   * Save file
   */
  private async saveFile(
    file: BillingFile,
    data: BillingFileCreateDto | BillingFileUpdateDto,
    context: { user: User }
  ) {
    // save file data
    // exclude upload-related fields and name for existing file
    Object.assign(file, omit(data, ['uploadId', 'password', ...(file.id ? ['name'] : [])]));

    // set "processing" status when file is uploaded
    const { uploadId } = data;
    if (uploadId) {
      file.status = BillingFileStatus.Processing;
    }

    await this.entityManager.save(file);

    // process uploaded file
    if (uploadId) {
      await this.pipeline.upload(file.id, uploadId, {
        userId: context.user.id,
        originalName: data.name,
        password: data.password
      });
    }

    return file;
  }
}

<nz-table [nzData]="control.value" [nzFrontPagination]="false" [nzShowPagination]="false" class="mb-2">
  <thead>
    <tr>
      <th nzWidth="20rem">File</th>
      <th>Description</th>
      <th nzWidth="2rem"></th>
    </tr>
  </thead>
  <tbody>
    @for (file of control.controls; track file) {
      <tr nz-form [formGroup]="file">
        <td>
          <file-name [name]="file.value.name" [url]="file.value.url" />
        </td>
        <td>
          <nz-form-item>
            <nz-form-control>
              <input nz-input formControlName="description" />
            </nz-form-control>
          </nz-form-item>
        </td>
        <td>
          <action-buttons [actions]="actions" [context]="{ index: $index, file: file.value }" />
        </td>
      </tr>
    }
  </tbody>
</nz-table>

<upload-button [withList]="false" (upload)="onUpload($event[0])" />
<action-buttons [actions]="uploadActions" class="ml-2" />

@if (availableFiles$ | async; as availableFiles) {
  @if (availableFiles.length) {
    <nz-select
      [formControl]="availableFilesSelect"
      nzPlaceHolder="+ Add existing file"
      (ngModelChange)="add($event)"
      class="ml-3"
      style="width: 25rem"
    >
      @for (file of availableFiles; track file.name) {
        <nz-option [nzLabel]="file.name" [nzValue]="file" nzCustomContent>
          <file-name [name]="file.name" />
        </nz-option>
      }
    </nz-select>
  }
}

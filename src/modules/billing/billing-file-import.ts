import { ManyToOne, PrimaryGenerated, Required, Table, type Relation } from '~/common/database/entity';
import { BillingFile } from './billing-file';

@Table('billing_file_imports')
export class BillingFileImport {
  @PrimaryGenerated('uuid')
  id: string;

  @Required()
  fileId: string;

  @ManyToOne(() => BillingFile, 'fileId')
  file?: Relation<BillingFile>;

  @Required()
  dataset: string;

  @Required()
  tableName: string;

  @Required()
  startDate: Date;

  @Required()
  finishDate: Date;

  @Required()
  recordsCount: number;
}

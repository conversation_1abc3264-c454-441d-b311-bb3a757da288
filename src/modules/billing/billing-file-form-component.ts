import { CommonModule } from '@angular/common';
import { Component, Inject } from '@angular/core';
import { ReactiveFormsModule, UntypedFormControl, UntypedFormGroup, Validators } from '@angular/forms';
import { ActivatedRoute } from '@angular/router';
import { NZ_MODAL_DATA, NzModalRef } from 'ng-zorro-antd/modal';
import { NzUploadFile } from 'ng-zorro-antd/upload';
import { BehaviorSubject, combineLatest, distinctUntilChanged, map, Observable, of, switchMap } from 'rxjs';
import { canExtractFile, extractFormats } from '~/common/compression/extract-formats';
import { DataSourceType } from '~/common/data-sources/data-source-type';
import { create } from '~/common/database/entity';
import { openEmailUpload } from '~/common/email/email-upload-component';
import { FileNameComponent } from '~/common/files/file-name-component';
import { getFileExtension, getFileFormat } from '~/common/files/file-utils';
import { SecretInputComponent } from '~/common/keyvault/secret-input-component';
import { CyclePipe } from '~/common/periods/cycle-pipe';
import { PeriodSelectComponent } from '~/common/periods/period-select-component';
import { Action, ActionButtonComponent, ActionButtonsComponent } from '~/common/ui/actions';
import { DrawerService } from '~/common/ui/drawer';
import { formControlValue$, formValid$, toggleFormControl } from '~/common/ui/forms';
import { createLoader, LoaderComponent } from '~/common/ui/loader';
import { NgZorroModule } from '~/common/ui/ng-zorro';
import { UploadButtonComponent } from '~/common/uploads/upload-button-component';
import { type UploadedFile } from '~/common/uploads/uploaded-file';
import { uniq } from '~/common/utils/array';
import { VendorAccountSelectComponent } from '~/common/vendors/vendor-account-select-component';
import { VendorSelectComponent } from '~/common/vendors/vendor-select-component';
import { billingDocumentTypes } from './billing-document-type';
import { BillingDocumentsApiClient } from './billing-documents-api-client';
import { BillingFile } from './billing-file';
import { BillingFileTypesApiClient } from './billing-file-types-api-client';
import { canUploadBillingFile, isExpectedBillingFile } from './billing-file-utils';
import { BillingFilesApiClient } from './billing-files-api-client';

export interface BillingFileFormData {
  file?: Partial<BillingFile>;
  mode?: string;
  relativeTo?: ActivatedRoute;
}

@Component({
  selector: 'billing-file-form',
  imports: [
    CommonModule,
    ReactiveFormsModule,
    NgZorroModule,
    ActionButtonComponent,
    ActionButtonsComponent,
    CyclePipe,
    FileNameComponent,
    LoaderComponent,
    PeriodSelectComponent,
    SecretInputComponent,
    UploadButtonComponent,
    VendorSelectComponent,
    VendorAccountSelectComponent
  ],
  templateUrl: './billing-file-form-component.html'
})
export class BillingFileFormComponent {
  form = new UntypedFormGroup({
    uploadId: new UntypedFormControl(null, [Validators.required]),
    vendorId: new UntypedFormControl(null, [Validators.required]),
    accountId: new UntypedFormControl(),
    period: new UntypedFormControl(null, [Validators.required]),
    name: new UntypedFormControl(null, [Validators.required]),
    description: new UntypedFormControl(),
    documentId: new UntypedFormControl(),
    password: new UntypedFormControl()
  });

  loader = createLoader(
    () => {
      const { file } = this.modalData;
      if (file) {
        if (file.id) {
          return this.filesRepository
            .getFile(file.id)
            .pipe(
              switchMap((file) =>
                this.fileTypesRepository
                  .getFileType(file.typeId!, { expand: ['password'] })
                  .pipe(map((type) => Object.assign(file, { type })))
              )
            );
        } else {
          return of(create(BillingFile, file));
        }
      } else {
        return of(new BillingFile());
      }
    },
    {
      onData: (file) => {
        this.updateForm(file);
      }
    }
  );

  accept$ = this.loader.data$.pipe(
    map((file) =>
      file.name
        ? uniq([getFileFormat(file.name), getFileExtension(file.name), ...extractFormats])
            .map((format) => `.${format}`)
            .join(',')
        : undefined
    )
  );

  compress$ = this.loader.data$.pipe(map((file) => !canExtractFile(file.name)));

  documents$ = this.loader.data$.pipe(
    switchMap((file) =>
      this.documentsApiClient
        .getDocuments({
          vendorId: file.vendorId,
          accountId: file.accountId,
          type: file.type?.documentType,
          limit: 20
        })
        .pipe(
          map((documents) =>
            documents.filter((document) => {
              return document.cycleStartDate < file.cycleEndDate! && document.cycleEndDate > file.cycleStartDate!;
            })
          )
        )
    )
  );

  files$ = new BehaviorSubject<NzUploadFile[]>([]);

  uploading$ = new BehaviorSubject(false);

  uploadActions: Action<BillingFile>[] = [
    {
      label: 'Upload from e-mail',
      icon: 'mail',
      type: 'dashed',
      allowed: (file) => file.type?.sourceType === DataSourceType.Email,
      execute: () => openEmailUpload(this.drawerService).afterClose,
      success: (file: UploadedFile) => ({
        execute: () => {
          if (file) {
            this.onUpload(file);
            this.files$.next([{ uid: file.id, name: file.name }]);
          }
        }
      })
    }
  ];

  uploadActions$: Observable<Action<BillingFile>[] | null> = combineLatest([
    formControlValue$(this.form.controls.uploadId),
    this.uploading$
  ]).pipe(
    // show other upload actions only when not already uploaded or uploading
    map(([uploadId, uploading]) => (uploadId || uploading ? null : this.uploadActions)),
    distinctUntilChanged()
  );

  constructor(
    private modalRef: NzModalRef,
    @Inject(NZ_MODAL_DATA) private modalData: BillingFileFormData,
    private drawerService: DrawerService,
    private filesRepository: BillingFilesApiClient,
    private fileTypesRepository: BillingFileTypesApiClient,
    private documentsApiClient: BillingDocumentsApiClient
  ) {}

  submitAction: Action<BillingFile> = {
    id: 'submit',
    label: 'Submit',
    type: 'primary',
    icon: 'save',
    enabled: formValid$(this.form),
    execute: (file) => {
      const data = this.form.value;
      if (file.id) {
        return this.filesRepository.updateFile(file.id, data);
      } else {
        return this.filesRepository.createFile(data);
      }
    },
    success: ({ id }, file) => ({
      execute: () => {
        this.modalRef.close(true);
      },
      navigate:
        this.requiresDocument(file) && !this.form.value.documentId
          ? { path: ['@billing/documents/create'], queryParams: { fileId: id } }
          : undefined
    })
  };

  onUpload(upload?: UploadedFile) {
    this.form.patchValue({ uploadId: upload?.id ?? null, name: upload?.name ?? null });
  }

  /**
   * Return document type name by ID
   */
  getDocumentType = billingDocumentTypes.extractor('name');

  /**
   * File requires document?
   */
  requiresDocument(file: BillingFile) {
    return Boolean(file.type?.documentType && !file.type?.parserId);
  }

  private updateForm(file: BillingFile) {
    const { mode } = this.modalData;

    // enable upload in upload mode and when allowed and no new document is required
    toggleFormControl(this.form.controls.uploadId, mode === 'upload' && canUploadBillingFile(file));

    // enable password field when expected file type is password protected and upload is enabled
    const withPassword = file.type?.isPasswordProtected ? this.form.controls.uploadId.enabled : false;
    toggleFormControl(this.form.controls.password, withPassword);

    // readonly params for expected files
    ['vendorId', 'accountId', 'period'].forEach((field) => {
      toggleFormControl(this.form.controls[field], !isExpectedBillingFile(file));
    });

    // enable document select when requires a document
    toggleFormControl(this.form.controls.documentId, this.requiresDocument(file));

    this.form.patchValue({
      ...file,
      // pre-fill password from file type
      password: withPassword ? file.type?.password : null
    });
  }
}

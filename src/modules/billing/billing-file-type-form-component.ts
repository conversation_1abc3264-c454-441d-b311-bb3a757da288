import { CommonModule } from '@angular/common';
import { Component, Inject, OnInit } from '@angular/core';
import {
  FormArray,
  FormControl,
  FormGroup,
  ReactiveFormsModule,
  UntypedFormArray,
  UntypedFormControl,
  UntypedFormGroup,
  Validators
} from '@angular/forms';
import { NZ_MODAL_DATA, NzModalRef, NzModalService } from 'ng-zorro-antd/modal';
import { Observable, ReplaySubject, combineLatest, map, of, switchMap, tap } from 'rxjs';
import { DataSourceType, dataSourceTypes } from '~/common/data-sources/data-source-type';
import { create } from '~/common/database/entity';
import { PersonNamePipe } from '~/common/directory/person-name-pipe';
import { getErrorMessage } from '~/common/errors/error-utils';
import { SecretInputComponent } from '~/common/keyvault/secret-input-component';
import { Cycle } from '~/common/periods/cycle';
import { CycleDurationInputComponent } from '~/common/periods/cycle-duration-input-component';
import { CyclePipe } from '~/common/periods/cycle-pipe';
import { CycleStartDayInputComponent } from '~/common/periods/cycle-start-day-input-component';
import { PeriodSelectComponent } from '~/common/periods/period-select-component';
import { getCurrentPeriod, getEntityPeriodCycle } from '~/common/periods/period-utils';
import { Action, ActionButtonComponent } from '~/common/ui/actions';
import { HelpIconComponent } from '~/common/ui/components/help-icon';
import { formControlValue$, formValid$, toggleFormControl } from '~/common/ui/forms';
import { LoaderComponent, createLoader } from '~/common/ui/loader';
import { NgZorroModule } from '~/common/ui/ng-zorro';
import { uniqBy } from '~/common/utils/array';
import { omit, omitBy } from '~/common/utils/object';
import { isDefined, isEmpty } from '~/common/utils/value';
import { Vendor } from '~/common/vendors/vendor';
import { VendorAccount } from '~/common/vendors/vendor-account';
import { VendorAccountSelectComponent } from '~/common/vendors/vendor-account-select-component';
import { isVendorAccountValid } from '~/common/vendors/vendor-account-utils';
import { VendorSelectComponent } from '~/common/vendors/vendor-select-component';
import { ContactsApiClient } from '~/modules/contacts/contacts-api-client';
import {
  BillingDocumentParserFormComponent,
  createBillingDocumentParserForm
} from './billing-document-parser-form-component';
import { billingDocumentTypes } from './billing-document-type';
import { BillingFileType } from './billing-file-type';
import { BillingFileTypeContact } from './billing-file-type-contact';
import { billingFileTypeExpectedPeriodOffsets } from './billing-file-type-expected-period-offsets';
import { getBillingFileTypeExpectedDate } from './billing-file-type-utils';
import { BillingFileTypesApiClient } from './billing-file-types-api-client';
import { createBillingFileName } from './billing-file-utils';

export interface BillingFileTypeFormData {
  fileTypeId?: string;
  clone?: boolean;
}

/**
 * Open file type form dialog
 */
export function openBillingFileTypeForm(modalService: NzModalService, data: BillingFileTypeFormData = {}) {
  const modal = modalService.create<BillingFileTypeFormComponent, BillingFileTypeFormData>({
    nzTitle: data.fileTypeId && !data.clone ? 'Edit file type' : 'Create file type',
    nzWidth: 650,
    nzContent: BillingFileTypeFormComponent,
    nzData: data,
    nzFooter: null,
    nzCentered: true
  });
  return modal.afterClose;
}

const documentFileParserId = 'document';

type CycleSource = 'inherit' | 'custom';

@Component({
  selector: 'billing-file-type-form',
  imports: [
    CommonModule,
    ReactiveFormsModule,
    NgZorroModule,
    ActionButtonComponent,
    CycleDurationInputComponent,
    CyclePipe,
    CycleStartDayInputComponent,
    HelpIconComponent,
    LoaderComponent,
    PeriodSelectComponent,
    PersonNamePipe,
    SecretInputComponent,
    VendorSelectComponent,
    VendorAccountSelectComponent
  ],
  templateUrl: './billing-file-type-form-component.html',
  styleUrl: './billing-file-type-form-component.less'
})
export class BillingFileTypeFormComponent implements OnInit {
  form = new FormGroup({
    id: new UntypedFormControl(),
    vendorId: new UntypedFormControl(null, [Validators.required]),
    name: new UntypedFormControl(null, [Validators.required]),
    description: new UntypedFormControl(),
    perAccount: new UntypedFormControl(),
    accounts: new UntypedFormControl(null, [Validators.required]),
    cycleSource: new FormControl<CycleSource>('inherit'),
    cycleStartDay: new UntypedFormControl(),
    cycleDuration: new UntypedFormControl(),
    expectedOffset: new UntypedFormControl(1, [Validators.required]),
    expectedBase: new UntypedFormControl('end', [Validators.required]),
    validFromPeriod: new UntypedFormControl(),
    validToPeriod: new UntypedFormControl(),
    sourceType: new UntypedFormControl(),
    sourceId: new UntypedFormControl(),
    contacts: new FormArray<FormGroup>([]),
    documentType: new UntypedFormControl(),
    parseDocument: new UntypedFormControl(null),
    documentParserConfig: createBillingDocumentParserForm(),
    isPasswordProtected: new UntypedFormControl(),
    password: new UntypedFormControl()
  });

  loader = createLoader(
    () =>
      this.modalData.fileTypeId
        ? this.fileTypesRepository.getFileType(this.modalData.fileTypeId, { expand: ['contacts', 'password'] }).pipe(
            map((fileType) => {
              if (this.modalData.clone) {
                fileType.id = null!;
              }
              return fileType;
            })
          )
        : of(create(BillingFileType, { contacts: [] })),
    {
      onData: (fileType) => {
        const parseDocument = fileType.parserId === documentFileParserId;
        this.form.patchValue({
          ...fileType,
          cycleSource: fileType.cycleStartDay ? 'custom' : 'inherit',
          perAccount: fileType.perAccount ? 'all' : fileType.accounts?.length ? 'selected' : 'no',
          parseDocument,
          documentParserConfig: parseDocument ? fileType.parserConfig : null
        });
      }
    }
  );

  vendor$ = new ReplaySubject<Vendor | undefined>(1);

  // example streams
  examples = {
    name: this.createExample$((value, { vendor }) => {
      const period = getCurrentPeriod();
      const [account] = vendor?.accounts ?? [];
      const name = value.name ? createBillingFileName(value.name, { period, account }) : undefined;
      return value.name !== name ? name : undefined;
    }),
    cycle: this.createExample$((value, { cycle }) => cycle),
    expectedDate: this.createExample$((value, { cycle }) =>
      cycle ? getBillingFileTypeExpectedDate(value, cycle) : undefined
    )
  };

  documentTypes = billingDocumentTypes;
  expectedPeriodOffsets = billingFileTypeExpectedPeriodOffsets;
  sourceTypes = dataSourceTypes;

  contacts$ = combineLatest([
    this.vendor$.pipe(
      switchMap((vendor) => (vendor ? this.contactsRepository.getContacts({ vendorId: vendor.id }) : of([])))
    ),
    this.loader.data$
  ]).pipe(
    tap(([vendorContacts, fileType]) => {
      const selectedContacts = (fileType.contacts ?? []).filter((vc) =>
        vendorContacts.some(({ id }) => id === vc.contactId)
      );
      this.contactsControl.clear();
      for (const contact of selectedContacts) {
        this.addContact(contact);
      }
    }),
    map(([vendorContacts]) => vendorContacts)
  );

  dataSources$ = combineLatest([this.vendor$, this.loader.data$]).pipe(map(([vendor]) => vendor?.dataSources));

  constructor(
    private modalRef: NzModalRef,
    @Inject(NZ_MODAL_DATA) private modalData: BillingFileTypeFormData,
    private modalService: NzModalService,
    private fileTypesRepository: BillingFileTypesApiClient,
    private contactsRepository: ContactsApiClient
  ) {}

  ngOnInit() {
    const { controls } = this.form;

    // "Per account" only when vendor has accounts
    this.vendor$.subscribe((vendor) => {
      toggleFormControl(controls.perAccount, (vendor?.accounts ?? []).length > 0);
    });

    // selected accounts only when "Per account" is "selected"
    formControlValue$(controls.perAccount).subscribe((perVendor) => {
      toggleFormControl(controls.accounts, perVendor === 'selected');
    });

    // selected accounts only when "Per account" is "selected"
    formControlValue$(controls.cycleSource).subscribe((cycleSource) => {
      const custom = cycleSource === 'custom';
      toggleFormControl(controls.cycleStartDay, custom);
      toggleFormControl(controls.cycleDuration, custom);
    });

    // contacts can be specified only for email source
    formControlValue$(controls.sourceType).subscribe((sourceType) => {
      toggleFormControl(this.contactsControl, sourceType === DataSourceType.Email);
    });

    // show password field when "Password protected" is checked
    formControlValue$(controls.isPasswordProtected).subscribe((isPasswordProtected) => {
      toggleFormControl(controls.password, isPasswordProtected);
    });

    // document parser only for selected document type
    formControlValue$(controls.documentType).subscribe((documentType) => {
      toggleFormControl(controls.parseDocument, Boolean(documentType));
    });
    formControlValue$(controls.parseDocument).subscribe((parseDocument) => {
      toggleFormControl(controls.documentParserConfig, Boolean(parseDocument));
    });
  }

  submitAction: Action = {
    id: 'submit',
    label: 'Submit',
    type: 'primary',
    icon: 'save',
    enabled: formValid$(this.form),
    execute: () => {
      const values = this.form.value;

      // remove empty values from document parser config
      if (values.documentParserConfig) {
        const config = omitBy(values.documentParserConfig, isEmpty);
        if (config.fields) {
          config.fields = omitBy(config.fields, isEmpty);
        }
        values.documentParserConfig = config;
      }

      const data = {
        ...omit(values, ['cycleSource', 'parseDocument', 'documentParserConfig']),
        perAccount: values.perAccount === 'all',
        accounts: values.perAccount === 'selected' ? values.accounts : null,
        cycleStartDay: values.cycleStartDay ?? null,
        cycleDuration: values.cycleDuration ?? null,
        parserId: values.documentType ? (values.parseDocument ? documentFileParserId : null) : undefined,
        parserConfig: values.documentType ? (values.parseDocument ? values.documentParserConfig : null) : undefined,
        password: values.isPasswordProtected ? values.password : null,
        contacts: values.contacts ? uniqBy(values.contacts, 'contactId') : null
      };
      if (values.id) {
        return this.fileTypesRepository.updateFileType(values.id, data as any);
      } else {
        return this.fileTypesRepository.createFileType(data as any);
      }
    },
    success: () => ({
      execute: () => {
        this.modalRef.close(true);
      }
    })
  };

  parserConfigAction: Action = {
    tooltip: 'Document parser config',
    icon: 'setting',
    size: 'small',
    execute: () => this.vendor$,
    success: (vendor) => ({
      execute: () => {
        this.modalService.create({
          nzTitle: 'Document parser config',
          nzContent: BillingDocumentParserFormComponent,
          nzData: { control: this.form.controls.documentParserConfig, vendor },
          nzWidth: 600
        });
      }
    })
  };

  addContactAction: Action = {
    label: 'Add contact',
    icon: 'plus',
    size: 'small',
    type: 'link',
    execute: () => {
      this.addContact();
    }
  };

  removeContactAction: Action<number> = {
    tooltip: 'Remove',
    icon: 'delete',
    size: 'small',
    type: 'link',
    confirm: (index) => {
      if (this.contactsControl.at(index).value.number?.length) {
        return { title: 'Are you sure you want to remove this contact?' };
      }
    },
    execute: (index) => {
      this.contactsControl.removeAt(index);
    }
  };

  get contactsControl() {
    return this.form.controls.contacts as UntypedFormArray;
  }

  private addContact(existing?: BillingFileTypeContact) {
    const control = this.contactsControl;
    control.insert(
      control.controls.length,
      new UntypedFormGroup({
        id: new UntypedFormControl(existing?.id),
        contactId: new UntypedFormControl(existing?.contactId, [Validators.required])
      })
    );
  }

  private createExample$(
    mapFn: (data: any, context: { vendor?: Vendor; account?: VendorAccount; cycle?: Cycle }) => any
  ): Observable<{ value: any; error?: true }> {
    return combineLatest([formControlValue$(this.form), this.vendor$]).pipe(
      map(([data, vendor]) => {
        try {
          const account = (vendor?.accounts ?? []).find(({ refId }) => data.accounts?.[0] === refId);
          const value = mapFn(data, {
            vendor,
            account,
            cycle: vendor ? getEntityPeriodCycle([data, account, vendor].filter(isDefined)) : undefined
          });
          return { value };
        } catch (e) {
          return { value: getErrorMessage(e), error: true };
        }
      })
    );
  }

  isVendorAccountValid = isVendorAccountValid;
}

import { <PERSON><PERSON>and<PERSON> } from '~/common/actions';
import { Provider } from '~/common/providers';
import { Static } from '~/common/schema';
import { setClassName } from '~/common/utils/class';
import { type Promisable } from '~/common/utils/promise';
import { camelCase, upperFirst } from '~/common/utils/string';
import { ParseBillingFile } from './billing-file-actions';
import { BillingFileData } from './billing-file-data';

export type BillingFileParsePayload = Static<typeof ParseBillingFile>;

/**
 * Forward parsed file to another file type parser
 */
export interface BillingFileForward {
  /**
   * Type code
   * @example summary
   */
  type: string;

  /**
   * Path
   */
  path: string;
}

export type BillingFileParseResult = Array<BillingFileData | BillingFileForward>;

export function BillingFileParser(parserId: string) {
  return ActionHandler(ParseBillingFile, { when: (p) => p.parserId === parserId, groupArgs: true });
}

/**
 * Create file parser provider from function
 */
export function createBillingFileParser(
  id: string,
  parser: (payload: BillingFileParsePayload) => Promisable<BillingFileParseResult>
) {
  @Provider()
  class Parser {
    @BillingFileParser(id)
    parseFile(payload: BillingFileParsePayload) {
      return parser(payload);
    }
  }

  setClassName(Parser, upperFirst(`${camelCase(id)}Parser`));
  return Parser;
}

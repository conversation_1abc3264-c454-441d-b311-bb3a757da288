<div [loader]="loader">
  <ng-template let-file>
    <form nz-form [formGroup]="form">
      <nz-form-item>
        <nz-form-label [nzSm]="6" [nzXs]="24" nzRequired>Vendor</nz-form-label>
        <nz-form-control [nzSm]="18" [nzXs]="24">
          <vendor-select formControlName="vendorId" />
        </nz-form-control>
      </nz-form-item>
      <nz-form-item [hidden]="form.controls.accountId.disabled">
        <nz-form-label [nzSm]="6" [nzXs]="24">Account</nz-form-label>
        <nz-form-control [nzSm]="18" [nzXs]="24">
          <vendor-account-select [vendorId]="form.value.vendorId" formControlName="accountId" />
        </nz-form-control>
      </nz-form-item>
      <nz-form-item>
        <nz-form-label [nzSm]="6" [nzXs]="24" nzRequired>Period</nz-form-label>
        <nz-form-control [nzSm]="18" [nzXs]="24">
          <period-select formControlName="period" />
        </nz-form-control>
      </nz-form-item>
      @if (form.controls.description.enabled) {
        <nz-form-item>
          <nz-form-label [nzSm]="6" [nzXs]="24">Description</nz-form-label>
          <nz-form-control [nzSm]="17" [nzXs]="24">
            <input nz-input formControlName="description" placeholder="Enter file description" />
          </nz-form-control>
        </nz-form-item>
      }
      @if (form.controls.documentId.enabled) {
        <nz-form-item>
          <nz-form-label [nzSm]="6" [nzXs]="24">Document</nz-form-label>
          <nz-form-control [nzSm]="18" [nzXs]="24">
            <nz-select
              formControlName="documentId"
              style="width: 23rem"
              nzPlaceHolder="New document"
              [nzAllowClear]="true"
            >
              @for (document of documents$ | async; track document) {
                <nz-option
                  [nzLabel]="getDocumentType(document.type) + ' ' + document.refId + ' (' + (document | cycle) + ')'"
                  [nzValue]="document.id"
                />
              }
            </nz-select>
          </nz-form-control>
        </nz-form-item>
      }
      @if (form.controls.uploadId.enabled) {
        <nz-form-item>
          <nz-form-label [nzSm]="6" [nzXs]="24" nzRequired>File</nz-form-label>
          <nz-form-control [nzSm]="17" [nzXs]="24">
            @if (file.name) {
              <div class="mt-1 mb-3">
                <file-name [name]="file.name" [description]="file.description" [url]="file.url" />
              </div>
            }
            <div [class.d-flex]="uploadActions$ | async">
              <upload-button
                [accept]="(accept$ | async) ?? undefined"
                [compress]="(compress$ | async) ?? false"
                [files]="(files$ | async) ?? []"
                (upload)="onUpload($event[0])"
                (uploading)="uploading$.next($event)"
              />
              @if (uploadActions$ | async; as actions) {
                <action-buttons [actions]="actions" [context]="file" class="ml-2" />
              }
            </div>
          </nz-form-control>
        </nz-form-item>
      }
      @if (form.controls.password.enabled) {
        <nz-form-item>
          <nz-form-label [nzSm]="6" [nzXs]="24">Password</nz-form-label>
          <nz-form-control [nzSm]="17" [nzXs]="24">
            <secret-input formControlName="password" />
          </nz-form-control>
        </nz-form-item>
      }
    </form>

    <div class="text-center mt-4">
      <action-button [action]="submitAction" [context]="file" />
    </div>
  </ng-template>
</div>

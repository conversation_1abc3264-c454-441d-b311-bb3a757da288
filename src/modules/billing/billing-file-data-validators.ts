import { formatPeriod } from '~/common/periods';
import { castArray } from '~/common/utils/array';
import { BillingFileDataError } from './billing-file-data-error';

type ValidationResult = string | boolean | undefined;
type ValidatorResult = { result: ValidationResult; field?: string; hints?: string[] };
type Validator<T> = (row: T) => ValidationResult | ValidatorResult;

export interface BillingDataValidators<T> {
  /**
   * Type validator
   */
  type?: Validator<T>;

  /**
   * Required field(s)
   */
  field?: keyof T | Array<keyof T>;

  /**
   * Period validator
   */
  period?: Validator<T>;

  /**
   * Vendor validator
   */
  vendorId?: Validator<T>;

  /**
   * Account validator
   */
  accountId?: Validator<T>;
}

function validate<T>(validator: Validator<T>, data: T): ValidatorResult {
  const result = validator(data);
  return typeof result === 'object' ? result : { result };
}

/**
 * Validate file data against given validators
 */
export function createDataValidator<T extends object>(
  validation: BillingDataValidators<T>,
  expected: { type?: string; period?: string; vendorId?: string; accountId?: string | string[] }
) {
  const fields = castArray(validation.field ?? []);
  return (data: T) => {
    // check file type
    if (validation.type && expected.type) {
      const { result, field, hints } = validate(validation.type, data);
      const type = typeof result === 'string' ? result : undefined;
      if (result === false || type !== expected.type) {
        throw new BillingFileDataError(
          'Invalid type',
          { expected: expected.type, received: type, field, data },
          { hints }
        );
      }
    }

    // check required field
    for (const field of fields) {
      if (!(field in data)) {
        throw BillingFileDataError.MissingField(String(field), { data });
      }
    }

    // check period
    if (validation.period && expected.period) {
      const { result, field, hints } = validate(validation.period, data);
      const period = typeof result === 'string' ? result : undefined;
      if (result === false || (period && period !== expected.period)) {
        throw new BillingFileDataError(
          'Invalid period',
          {
            expected: formatPeriod(expected.period),
            received: period ? formatPeriod(period) : undefined,
            field,
            data
          },
          { hints }
        );
      }
    }

    // check account
    if (validation.accountId && expected.accountId) {
      const { result, field, hints } = validate(validation.accountId, data);
      const accountId = typeof result === 'string' ? result : undefined;
      const expectedAccountIds = typeof expected.accountId === 'string' ? [expected.accountId] : expected.accountId;
      if (result === false || (accountId && !expectedAccountIds.includes(accountId))) {
        throw new BillingFileDataError(
          'Invalid account',
          {
            expected: expectedAccountIds.sort().join(', '),
            received: accountId,
            field,
            data
          },
          { hints }
        );
      }
    }

    return true;
  };
}

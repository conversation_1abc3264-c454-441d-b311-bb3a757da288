import { DataSource } from '~/common/data-sources/data-source';
import { DataSourceType, dataSourceTypes } from '~/common/data-sources/data-source-type';
import {
  ManyToOne,
  OneToMany,
  Optional,
  PrimaryGenerated,
  Required,
  Table,
  type Relation
} from '~/common/database/entity';
import { CycleConfig } from '~/common/periods/cycle-config';
import { ValidInPeriod } from '~/common/periods/valid-in-period';
import { Vendor } from '~/common/vendors/vendor';
import { BillingDocumentType, billingDocumentTypes } from './billing-document-type';
import { BillingFileTypeContact } from './billing-file-type-contact';
import { BillingFileTypeThreshold } from './billing-file-type-threshold';

@Table('billing_file_types', { history: true, events: true })
export class BillingFileType implements CycleConfig, ValidInPeriod {
  /**
   * Type ID
   * @example 3e85be98-c987-40e2-882a-ab69739420c6
   */
  @PrimaryGenerated('uuid')
  id: string;

  /**
   * Vendor ID
   * @example ecfcca0d-5e1c-49ce-be5a-8aaf337114aa
   */
  @Required()
  vendorId: string;

  /**
   * Vendor
   */
  @ManyToOne(() => Vendor, 'vendorId')
  vendor?: Relation<Vendor>;

  /**
   * File name mask
   * @example 'vfV2InvDIR2_{ACCOUNT}_XXXXXXXXXX_{PERIOD:YYYYMM}.xml'
   */
  @Required()
  name: string;

  /**
   * Reference ID
   * @example detail
   */
  @Optional()
  refId?: string;

  /**
   * Description
   * @example 'Combined invoice detail XML file'
   */
  @Optional()
  description?: string;

  /**
   * Generate file of this type for all accounts
   */
  @Required()
  perAccount: boolean = false;

  /**
   * Generate file of this type for listed accounts
   */
  @Optional()
  accounts?: string[];

  /**
   * Period since a type is valid from
   * @example 2019-07
   */
  @Optional()
  validFromPeriod?: string;

  /**
   * Period until a type is valid to
   */
  @Optional()
  validToPeriod?: string;

  /**
   * Day of the month to expect file of this type
   * @example 10
   * @deprecated
   */
  @Optional()
  expectedDay?: number;

  /**
   * Custom period offset for expected day
   * @example 1
   * @deprecated
   */
  @Optional()
  expectedPeriodOffset?: number;

  /**
   * Expected base to period
   * @example start - after cycle start
   * @example end - after cycle end
   */
  @Required({ type: String })
  expectedBase: 'start' | 'end';

  /**
   * Expected offset (in days) relative to cycle (see expectedBase)
   */
  @Required()
  expectedOffset: number;

  /**
   * Cycle start day
   * @example 1
   */
  @Optional()
  cycleStartDay?: number;

  /**
   * Cycle duration in months
   * @example 1
   */
  @Optional()
  cycleDuration?: number;

  /**
   * Parser ID
   * @example mobile:cz-vodafone:detail
   */
  @Optional()
  parserId?: string;

  /**
   * Parser configuration
   */
  @Optional({ type: Object })
  parserConfig?: Record<string, unknown>;

  /**
   * Downloader ID
   * @example cz-vodafone:portal
   */
  @Optional()
  downloaderId?: string;

  /**
   * Source type
   * @example portal
   */
  @Optional({ list: dataSourceTypes })
  sourceType?: DataSourceType;

  /**
   * Source ID
   */
  @Optional()
  sourceId?: string;

  /**
   * Source
   */
  @ManyToOne(() => DataSource, 'sourceId')
  source?: DataSource;

  /**
   * Billing document type
   * @example invoice
   */
  @Optional({ list: billingDocumentTypes })
  documentType?: BillingDocumentType;

  /**
   * Is file password protected? (XLSX/ZIP)
   * @example false
   */
  @Required()
  isPasswordProtected: boolean = false;

  /**
   * Default/last password
   */
  @Optional({ isSecret: true })
  password?: string;

  /**
   * Datasets that file type produces
   */
  @Optional()
  datasets?: string[];

  /**
   * Thresholds
   */
  @OneToMany(() => BillingFileTypeThreshold, 'type')
  thresholds?: Relation<BillingFileTypeThreshold[]>;

  /**
   * Contacts
   */
  @OneToMany(() => BillingFileTypeContact, 'type')
  contacts?: Relation<BillingFileTypeContact[]>;
}

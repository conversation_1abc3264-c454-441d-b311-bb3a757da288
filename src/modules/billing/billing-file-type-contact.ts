import { ManyToOne, PrimaryGenerated, Required, Table, type Relation } from '~/common/database/entity';
import { Contact } from '~/modules/contacts/contact';
import { BillingFileType } from './billing-file-type';

@Table('billing_file_type_contacts', { history: true, events: true })
export class BillingFileTypeContact {
  /**
   * ID
   * @example 05c7db02-1014-42ac-9ad4-e8e4a7ea3488
   */
  @PrimaryGenerated('uuid')
  id: string;

  /**
   * File type ID
   * @example 3e85be98-c987-40e2-882a-ab69739420c6
   */
  @Required()
  typeId: string;

  /**
   * File type
   */
  @ManyToOne(() => BillingFileType, 'typeId')
  type?: Relation<BillingFileType>;

  /**
   * Contact ID
   * @example fe37d004-e972-4df8-805f-c37586d465a6
   */
  @Required()
  contactId: string;

  /**
   * Contact
   */
  @ManyToOne(() => Contact, 'contactId')
  contact?: Relation<Contact>;
}

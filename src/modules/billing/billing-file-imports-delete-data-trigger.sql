CREATE FUNCTION billing_file_imports_delete_data_trigger()
R<PERSON>UR<PERSON> trigger AS
$$
BEGIN
  -- delete data from imported table
  EXECUTE 'DELETE FROM ' || OLD.table_name || ' WHERE file_id = $1 ' USING OLD.file_id;
  RETURN OLD;
END;
$$
LANGUAGE plpgsql;

CREATE TRIGGER billing_file_imports_delete_data_trigger
AFTER DELETE ON billing_file_imports
FOR EACH ROW EXECUTE FUNCTION billing_file_imports_delete_data_trigger();

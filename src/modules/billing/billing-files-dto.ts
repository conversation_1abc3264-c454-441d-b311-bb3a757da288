import { Optional, PartialType, PickType, Required } from '~/common/schema';
import { BillingFile } from './billing-file';

export class BillingFileCreateDto extends PickType(BillingFile, [
  'vendorId',
  'accountId',
  'period',
  'name',
  'description',
  'documentId'
]) {
  /**
   * Upload ID
   * @example c5b264ec95c45bc9d4b54ca3839e0d1b
   */
  @Required()
  uploadId?: string;

  /**
   * Password
   * @example 12345
   */
  @Optional()
  password?: string;
}

export class BillingFileUpdateDto extends PartialType(BillingFileCreateDto) {}

export class BillingFilesGenerateDto {
  /**
   * Period
   * @example 2020-10
   */
  @Required()
  period: string;

  /**
   * Vendor ID
   */
  @Optional()
  vendorId?: string;
}

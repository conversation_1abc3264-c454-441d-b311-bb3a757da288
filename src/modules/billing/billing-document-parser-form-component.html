<form nz-form [formGroup]="control">
  <nz-form-item formGroupName="fields">
    <h4>Field mapping</h4>
    <nz-alert nzType="warning" [nzMessage]="content" class="mb-3"></nz-alert>
    <ng-template #content>
      <p>
        Use this <strong>only for fine-tuning</strong> when a field is not extracted correctly (e.g., missing or
        incorrect value). This helps improve accuracy in case the automated extraction does not return the expected
        result. Define field name that you see in the invoice, use
        <a nz-popover nzPopoverTitle="Using formulas" [nzPopoverContent]="formulas">formulas</a> or
        <a nz-popover nzPopoverTitle="Using AI" [nzPopoverContent]="ai">AI instructions</a>. You can also
        <a nz-popover nzPopoverTitle="Skip the field" [nzPopoverContent]="skip">skip the field</a> if needed.
      </p>
      <ng-template #skip>
        Sometimes the invoice does not contain a Document ID, but the parser may still extract some value. To ignore
        such value, you can set the field mapping to <strong>"-"</strong> (a dash).
      </ng-template>
      <ng-template #formulas>
        <p>
          For amount fields, you can combine values from multiple fields using simple formulas. To reference another
          field, wrap its name in curly brackets: <code>&lcub;Field name&rcub;</code>
        </p>
        <div class="example">
          <h4>Example:</h4>
          <div class="text-info">&lcub;Monthly subscription fees&rcub; + &lcub;Monthly usage fees&rcub;</div>
          <p>This will calculate the result by adding values from both fields.</p>
        </div>
      </ng-template>
      <ng-template #ai>
        <p>You can write advanced instructions beyond fields or formulas that AI can evaluate.</p>
        <div class="example">
          <h4>Example (Net amount):</h4>
          <div class="text-info">Sum all subtotals</div>
        </div>
        <div class="example">
          <h4>Example (Account ID):</h4>
          <div class="text-info">Middle fragment from the filename</div>
          <p>E.g. for filename <strong>INV-8325-210583.pdf</strong>, it returns <strong>8325</strong>.</p>
        </div>
      </ng-template>
    </ng-template>
    @if (fieldControls.length) {
      <table class="fields mb-3 w-100">
        <tbody>
          @for (field of fieldControls.controls; track field) {
            <tr>
              <td style="width: 9rem">
                <list-select [list]="fields" [formControl]="field" placeholder="Select field" />
              </td>
              <td>
                @if (field.value) {
                  <textarea nz-input [formControlName]="field.value" nzAutosize></textarea>
                }
              </td>
              <td style="width: 2rem">
                @if (field.value) {
                  <action-button icon="delete" type="dashed" (execute)="removeField($index)" />
                }
              </td>
            </tr>
          }
        </tbody>
      </table>
    }
    <action-button icon="plus" label="Add mapping" type="dashed" (execute)="addField()" />
  </nz-form-item>
  <nz-divider />
  <h4 class="mb-3">Additional settings</h4>
  <nz-form-item>
    <nz-form-label [nzSm]="4" [nzXs]="24">Currency</nz-form-label>
    <nz-form-control [nzSm]="20" [nzXs]="24">
      <currency-select formControlName="currency" placeholder="" [allowClear]="true" />
      <help-icon
        class="ml-2"
        content="Custom document currency. Use this only for fine-tuning when the currency is not extracted properly."
      />
    </nz-form-control>
  </nz-form-item>
  <nz-form-item>
    <nz-form-label [nzSm]="4" [nzXs]="24">Parse items</nz-form-label>
    <nz-form-control [nzSm]="20" [nzXs]="24">
      <label formControlName="items" nz-checkbox></label>
      <help-icon class="ml-2" content="Parse document items, e.g. all invoice charges with amount and quantity." />
    </nz-form-control>
  </nz-form-item>
  <nz-form-item>
    <nz-form-label [nzSm]="4" [nzXs]="24">Pages</nz-form-label>
    <nz-form-control [nzSm]="20" [nzXs]="24">
      <input nz-input formControlName="pages" style="width: 6rem" />
      <help-icon class="ml-2" [content]="info">
        <ng-template #info>
          <p>Define pages to parse as:</p>
          <ul>
            <li>Custom page range (<strong>2-3</strong>)</li>
            <li>Selected pages (<strong>1,3</strong>)</li>
            <li>Last X pages (<strong>last</strong>, <strong>last-3</strong>)</li>
            <li>Combination of these (<strong>1-3,last-2</strong>)</li>
          </ul>
          <p>Default is <strong>first two pages (1-2)</strong>.</p>
        </ng-template>
      </help-icon>
    </nz-form-control>
  </nz-form-item>
  <nz-divider />
  <div>
    <h4>Test settings</h4>
    <upload-button [withList]="false" (upload)="parseUpload($event[0].id)" accept=".pdf" [withSuccessMessage]="false" />
  </div>
</form>

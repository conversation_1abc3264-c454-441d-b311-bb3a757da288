import { CommonModule } from '@angular/common';
import { Component } from '@angular/core';
import { FormControl, FormGroup, ReactiveFormsModule } from '@angular/forms';
import { ActivatedRoute } from '@angular/router';
import { combineLatest, firstValueFrom, map, shareReplay } from 'rxjs';
import { FileIconComponent } from '~/common/files/file-icon-component';
import { FileNameComponent } from '~/common/files/file-name-component';
import { ListSelectComponent } from '~/common/lists/list-select-component';
import { CycleComponent } from '~/common/periods/cycle-component';
import { PeriodNameComponent } from '~/common/periods/period-name-component';
import { PeriodSelectComponent } from '~/common/periods/period-select-component';
import { EditablePropertyComponent } from '~/common/schema/editable-property-component';
import { PropertyValueComponent } from '~/common/schema/property-value-component';
import { SearchInputComponent } from '~/common/search/search-input-component';
import { CurrentService } from '~/common/services/current-service';
import { Action } from '~/common/ui/actions';
import { CollectionPageComponent, defineCollectionColumns } from '~/common/ui/collections';
import { HelpIconComponent } from '~/common/ui/components/help-icon';
import { DrawerService } from '~/common/ui/drawer';
import { formControlValue$, FormRouteSyncConfig, FormRouteSyncDirective } from '~/common/ui/forms';
import { Loader } from '~/common/ui/loader';
import { NgZorroModule } from '~/common/ui/ng-zorro';
import { writeXlsxFile } from '~/common/ui/xlsx';
import { omit } from '~/common/utils/object';
import { IsEmptyPipe } from '~/common/utils/value/is-empty-pipe';
import { VendorAccountNameComponent } from '~/common/vendors/vendor-account-name-component';
import { VendorAccountSelectComponent } from '~/common/vendors/vendor-account-select-component';
import { getVendorAccountName } from '~/common/vendors/vendor-account-utils';
import { VendorNameComponent } from '~/common/vendors/vendor-name-component';
import { VendorSelectComponent } from '~/common/vendors/vendor-select-component';
import { getVendorFullName } from '~/common/vendors/vendors-utils';
import { BillingDocument } from './billing-document';
import { openBillingDocument } from './billing-document-detail-component';
import { BillingDocumentWrite } from './billing-document-permissions';
import { billingDocumentTypes } from './billing-document-type';
import { BillingDocumentsApiClient } from './billing-documents-api-client';
import { BillingDocumentsQueryParams } from './billing-documents-query-params';

@Component({
  selector: 'billing-documents',
  imports: [
    CommonModule,
    ReactiveFormsModule,
    NgZorroModule,
    CycleComponent,
    CollectionPageComponent,
    EditablePropertyComponent,
    FileIconComponent,
    FileNameComponent,
    FormRouteSyncDirective,
    HelpIconComponent,
    IsEmptyPipe,
    ListSelectComponent,
    PeriodNameComponent,
    PeriodSelectComponent,
    PropertyValueComponent,
    SearchInputComponent,
    VendorNameComponent,
    VendorSelectComponent,
    VendorAccountNameComponent,
    VendorAccountSelectComponent
  ],
  templateUrl: './billing-documents-component.html',
  styleUrl: './billing-documents-component.less'
})
export class BillingDocumentsComponent {
  /**
   * BillingDocument filter
   */
  filter = new FormGroup({
    vendorId: new FormControl(),
    accountId: new FormControl(),
    period: new FormControl(),
    type: new FormControl(),
    search: new FormControl(),
    fromExpected: new FormControl(),
    usd: new FormControl(),
    sort: new FormControl()
  });

  filterRouteSync: FormRouteSyncConfig = {
    fromQuery: (params) => {
      return {
        ...params,
        usd: params.usd === '1'
      };
    },
    toQuery: (filter) => ({
      ...omit(filter, 'search'),
      usd: filter.usd ? '1' : undefined
    })
  };

  search$ = formControlValue$(this.filter.controls.search, { search: true }).pipe(shareReplay(1));
  params$ = combineLatest([this.route.queryParams, this.search$]).pipe(
    map(([params, search]) => ({ ...params, search }))
  );

  loader = new Loader(
    (params, { limit }) =>
      this.getDocuments(
        {
          ...params,
          expand: ['files'],
          sum: ['netAmount', 'netAmountUsd', 'totalAmount', 'totalAmountUsd'],
          groupBy: ['currency'],
          limit
        },
        { entityEvents: true }
      ),
    { from: this.params$, limit: 25 }
  );

  types = billingDocumentTypes;

  globalActions: Action[] = [
    {
      label: 'Create',
      icon: 'plus',
      type: 'primary',
      authorize: BillingDocumentWrite,
      navigate: () => ({
        path: ['create'],
        queryParams: this.route.snapshot.queryParams
      })
    },
    {
      icon: 'file-excel',
      label: 'Export',
      execute: () => this.exportDocuments(),
      error: (e) => ({
        message: e.message ?? 'Failed to export documents'
      })
    }
  ];

  columns = defineCollectionColumns<BillingDocument>([
    { name: 'vendor', title: 'Vendor / Account', sort: 'asc', width: 170 },
    { name: 'refId', title: 'Document ID' },
    { name: 'period', title: 'Period', sort: 'desc', width: 190 },
    { name: 'issueDate', title: 'Issue date', sort: 'desc', type: Date, width: 90 },
    { title: 'Files', width: 50 },
    {
      name: 'connectionsCount',
      title: 'Connections',
      sort: 'desc',
      type: Number,
      width: 90,
      when: () => this.service.settings.billingDocuments?.withConnections ?? false
    },
    { name: 'netAmount', title: 'Net amount', type: Number },
    { name: 'totalAmount', title: 'Total amount', type: Number }
  ]);

  constructor(
    public route: ActivatedRoute,
    private drawerService: DrawerService,
    public service: CurrentService,
    private documentsApiClient: BillingDocumentsApiClient
  ) {}

  getDocuments(params: BillingDocumentsQueryParams, options: { entityEvents?: boolean } = {}) {
    return this.documentsApiClient.getDocuments(
      { ...omit(params, ['usd']) },
      {
        onAction: true,
        onEntityEvents: options.entityEvents ? [BillingDocument] : undefined
      }
    );
  }

  /**
   * Open document details
   * @param document
   */
  openDocument(document: BillingDocument) {
    openBillingDocument(
      this.drawerService,
      { documentId: document.id },
      { title: this.getDocumentTypeName(document.type) }
    );
  }

  /**
   * Return document type name
   */
  getDocumentTypeName = billingDocumentTypes.extractor('name');

  /**
   * Export documents to XLSX
   * @todo Implement more universal export from table
   */
  async exportDocuments() {
    const documents = await firstValueFrom(this.getDocuments(this.route.snapshot.queryParams));
    await writeXlsxFile(
      'billing-documents.xlsx',
      [
        'Vendor',
        'Account',
        'Type',
        'Document ID',
        'PO number',
        'Issue date',
        'Cycle start date',
        'Cycle end date',
        'Connections',
        'Net amount',
        'Total amount'
      ],
      documents.map((document) => [
        getVendorFullName(document.vendor!),
        document.account ? getVendorAccountName(document.account, { service: this.service.get() }) : undefined,
        this.getDocumentTypeName(document.type),
        document.refId,
        document.poRefId,
        document.issueDate,
        document.cycleStartDate,
        document.cycleEndDate,
        document.connectionsCount,
        {
          value: this.filter.value.usd ? document.netAmountUsd : document.netAmount,
          currency: this.filter.value.usd ? 'USD' : document.currency
        },
        {
          value: this.filter.value.usd ? document.totalAmountUsd : document.totalAmount,
          currency: this.filter.value.usd ? 'USD' : document.currency
        }
      ]),
      {
        skipEmptyColumns: true
      }
    );
  }

  /**
   * Update document function factory
   */
  updateDocument = (documentId: string) => {
    return (data: Partial<BillingDocument>) => this.documentsApiClient.updateDocument(documentId, data);
  };
}

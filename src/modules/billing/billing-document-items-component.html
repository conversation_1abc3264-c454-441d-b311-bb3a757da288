<nz-table nzTemplateMode class="size-xs mt-2">
  <thead>
    <tr>
      <th>Description</th>
      @if (withQuantity()) {
        <th class="text-center" nzWidth="5rem">Qty</th>
      }
      <th class="text-right">Amount</th>
    </tr>
  </thead>
  <tbody>
    @for (item of items(); track item.id) {
      <tr>
        <td>{{ item.description }}</td>
        @if (withQuantity()) {
          <td class="text-center">{{ item.quantity }}</td>
        }
        <td class="text-right">
          <ng-template #info>
            <table class="info">
              <tr>
                <td>Quantity:</td>
                <td>{{ item.quantity }}</td>
              </tr>
              <tr>
                <td>Unit price:</td>
                <td>{{ item.unitPrice | currency: item.unitName : '' }} {{ currency() }}</td>
              </tr>
            </table>
          </ng-template>
          <span nz-popover [nzPopoverContent]="item.quantity ? info : undefined" [class.cursor-help]="item.quantity">
            {{ item.totalAmount | currency: currency() : '' }} {{ currency() }}
          </span>
        </td>
      </tr>
    }
  </tbody>
</nz-table>

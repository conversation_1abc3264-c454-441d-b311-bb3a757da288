import { ChangeDetectionStrategy, Component, Input } from '@angular/core';
import { ListItemComponent } from '~/common/lists/list-item-component';
import { BillingFileStatus, billingFileStatuses } from './billing-file-status';

@Component({
  selector: 'billing-file-status',
  imports: [ListItemComponent],
  template: `<list-item [list]="statuses" [value]="status" format="tag" />`,
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class BillingFileStatusComponent {
  @Input()
  status: BillingFileStatus;

  statuses = billingFileStatuses;
}

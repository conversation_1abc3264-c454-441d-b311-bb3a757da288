import { ActionBus } from '~/common/actions';
import {
  create,
  EntityManager,
  Entity<PERSON><PERSON><PERSON>,
  Entity<PERSON>rigger,
  IsNotNull,
  <PERSON>T<PERSON>,
  toEntityEventFilter
} from '~/common/database';
import { toErrorPayload } from '~/common/errors';
import { EventBus, type Event } from '~/common/events';
import { everyMinutes, Worker } from '~/common/jobs';
import { NotificationBuilder, NotificationData } from '~/common/notifications';
import { Provider } from '~/common/providers';
import { addHours } from '~/common/utils/date';
import { getVendorFullName, Vendor } from '~/common/vendors';
import { BillingFile } from './billing-file';
import { BillingFileStatus } from './billing-file-status';
import { isDataBillingFileType } from './billing-file-type-utils';
import { isExpectedBillingFile } from './billing-file-utils';
import { BuildModel } from './billing-model-actions';
import { BillingModelBuild } from './billing-model-build';
import { BillingModelBuildStatus } from './billing-model-build-status';

// retry interval for failed builds (in hours)
const retryHours = 12;

@Provider()
export class BillingModelBuilder {
  constructor(
    private entityManager: EntityManager,
    private actionBus: ActionBus,
    private eventBus: EventBus
  ) {}

  @Worker({
    description: 'Run pending billing model builds',
    parallel: false,
    schedule: everyMinutes(60),
    fork: true,
    events: toEntityEventFilter(BillingModelBuild, { operations: [EntityOperation.Insert] }),
    toArgs: () => []
  })
  async runBuilds() {
    while (true) {
      const build = await this.entityManager.findOneBy(BillingModelBuild, {
        status: BillingModelBuildStatus.Pending,
        createdDate: LessThan(new Date())
      });
      if (build) {
        await this.runBuild(build.id);
      } else {
        break;
      }
    }
  }

  /**
   * Run billing model build - compute billing data for BI
   */
  async runBuild(id: string) {
    const build = await this.entityManager.findOneOrFail(BillingModelBuild, {
      where: { id },
      relations: { vendor: true }
    });

    const { serviceId, vendorId, period } = build;
    const eventBus = this.eventBus.span('Build', { id, vendorId, period });

    try {
      const start = Date.now();
      eventBus.verbose('building', 'Building vendor model');

      const isPartial = await this.isPartial(vendorId, period);
      await this.entityManager.save(
        Object.assign(build, {
          status: BillingModelBuildStatus.Started,
          startDate: new Date(),
          finishDate: null,
          error: null,
          isPartial
        })
      );

      // run related model builder action
      await this.actionBus.execute(BuildModel, { serviceId, vendorId, period, isPartial }, { fork: true });

      // success!
      await this.entityManager.save(
        Object.assign(build, {
          status: BillingModelBuildStatus.Finished,
          finishDate: new Date()
        })
      );

      eventBus.info('finished', 'Vendor model build finished', { duration: (Date.now() - start) / 1000 });
    } catch (e) {
      await this.entityManager.save(
        Object.assign(build, {
          status: BillingModelBuildStatus.Error,
          finishDate: new Date(),
          error: toErrorPayload(e)
        })
      );

      // retry in 12 hours.. schedule a new pending build
      await this.addBuild(vendorId, period, addHours(new Date(), retryHours));

      eventBus.error('error', e);
    }
  }

  @EntityTrigger({
    description: 'Build model when expected file is processed or deleted',
    after: [EntityOperation.Update, EntityOperation.Delete],
    entity: BillingFile,
    changed: ['status'],
    filter: ({ entity: file }) => {
      console.log(
        'build',
        file.typeId,
        file.status,
        isExpectedBillingFile(file) && file.status === BillingFileStatus.Processed
      );
      return isExpectedBillingFile(file) && file.status === BillingFileStatus.Processed;
    },
    toArgs: ({ entity: file }) => [file.vendorId, file.period]
  })
  @Worker<{ vendorId: string; period: string }>({
    description: 'Add billing model build for vendor and period',
    toArgs: ({ vendorId, period }) => [vendorId, period]
  })
  async addBuild(vendorId: string, period: string, createdDate?: Date) {
    // already pending
    const build = await this.entityManager.findOneBy(BillingModelBuild, {
      vendorId,
      period,
      status: BillingModelBuildStatus.Pending
    });
    if (build) {
      return build;
    } else {
      const vendor = await this.entityManager.findOneByOrFail(Vendor, { id: vendorId });
      const build = create(BillingModelBuild, {
        serviceId: vendor.serviceId,
        vendorId,
        period,
        isPartial: await this.isPartial(vendorId, period),
        createdDate
      });
      return this.entityManager.save(build);
    }
  }

  private async isPartial(vendorId: string, period: string) {
    const files = await this.entityManager.find(BillingFile, {
      where: { vendorId, period, typeId: IsNotNull() },
      relations: ['type']
    });
    return files.some((file) => isDataBillingFileType(file.type!) && file.status !== BillingFileStatus.Processed);
  }

  /**
   * Create notification for build error
   */
  @NotificationBuilder({
    event: [BillingModelBuilder, 'error'],
    subject: 'Failed to process billing data'
  })
  async createFailedNotification({ data }: Event): Promise<NotificationData> {
    const vendor = await this.entityManager.findOneByOrFail(Vendor, { id: data.vendorId });
    return {
      service: vendor.serviceId,
      vendor: getVendorFullName(vendor)
    };
  }
}

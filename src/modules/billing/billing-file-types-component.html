<collection-page
  [header]="header"
  [actions]="globalActions"
  [loader]="loader"
  [columns]="columns"
  [content]="content"
/>

<ng-template #header>
  <form nz-form nzLayout="inline" [formGroup]="filter" [formRouteSync]="filterRouteSync">
    <nz-form-item>
      <nz-form-label>Vendor</nz-form-label>
      <nz-form-control>
        <vendor-select formControlName="vendorId" placeholder="All vendors" />
      </nz-form-control>
    </nz-form-item>
    <nz-form-item>
      <nz-form-label>Source type</nz-form-label>
      <nz-form-control>
        <list-select [list]="sourceTypes" formControlName="sourceType" [allowClear]="true" placeholder="All sources" />
      </nz-form-control>
    </nz-form-item>
    <nz-form-item>
      <nz-form-label>Has parser</nz-form-label>
      <nz-form-control>
        <nz-switch formControlName="hasParser" />
      </nz-form-control>
    </nz-form-item>
    <nz-form-item>
      <nz-form-label>Has downloader</nz-form-label>
      <nz-form-control>
        <nz-switch formControlName="hasDownloader" />
      </nz-form-control>
    </nz-form-item>
  </form>
</ng-template>

<ng-template #content let-fileTypes>
  @for (fileType of fileTypes; track fileType.id) {
    <tr>
      <td>
        <vendor-name [vendor]="fileType.vendor" />
      </td>
      <td class="filename">
        <file-name [name]="fileType.name" [class.text-invalid]="!isFileTypeValid(fileType)" />
        @if (fileType.description) {
          <p class="detail">{{ fileType.description }}</p>
        }
        @if (fileType.perAccount) {
          <p class="detail text-info">Per account</p>
        }
        @if (fileType.documentType) {
          <p class="detail text-info">
            {{ fileType.parserId ? 'Creates' : 'Requires' }} document (<list-item
              [list]="documentTypes"
              [value]="fileType.documentType"
            />)
          </p>
        }
        @if (fileType.accounts?.length) {
          <ul class="detail list-unstyled text-info">
            @for (account of getAccounts(fileType); track account) {
              <li>Account: <vendor-account-name [account]="account" /></li>
            }
          </ul>
        }
        @if (fileType.isPasswordProtected) {
          <p class="detail text-warning">Password protected</p>
        }
        @if (fileType.validFromPeriod) {
          <p class="detail text-success">Valid from <period-name [period]="fileType.validFromPeriod" /></p>
        }
        @if (fileType.validToPeriod) {
          <p class="detail text-danger">Valid to <period-name [period]="fileType.validToPeriod" /></p>
        }
      </td>
      <td class="text-center">
        <ng-template #cycleExampleTpl>
          @if (getFileTypeCycle(fileType); as cycle) {
            {{ cycle.startDate | date }} - {{ cycle.endDate | date }}
          }
        </ng-template>
        <div class="cursor-help" nz-popover nzPopoverTitle="Example" [nzPopoverContent]="cycleExampleTpl">
          <span>{{ fileType.cycleStartDay ?? fileType.vendor!.cycleStartDay }}.</span>
          @if (fileType.cycleDuration ?? fileType.vendor!.cycleDuration; as cycleDuration) {
            <p class="detail"><list-item [list]="cycleDurations" [value]="cycleDuration" /></p>
          }
        </div>
      </td>
      <td class="text-center">
        <ng-template #expectedDateExampleTpl>
          {{ getFileTypeExpectedDate(fileType) | date }}
        </ng-template>
        <div class="cursor-help" nz-popover nzPopoverTitle="Example" [nzPopoverContent]="expectedDateExampleTpl">
          <span>{{ fileType.expectedOffset }} {{ fileType.expectedOffset === 1 ? 'day' : 'days' }}</span>
          <p class="detail">after cycle {{ fileType.expectedBase }}</p>
        </div>
      </td>
      <td class="text-center">
        @if (fileType.sourceType) {
          <list-item [list]="sourceTypes" [value]="fileType.sourceType" />
          <p class="detail text-center">
            @if (fileType.source) {
              @if (isWebDataSourceType(fileType.sourceType)) {
                <ng-template #source>
                  <data-source [context]="{ vendorId: fileType.vendorId, sourceId: fileType.source.id }" />
                </ng-template>
                <span
                  nz-popover
                  [nzPopoverTitle]="fileType.source.name"
                  [nzPopoverContent]="source"
                  nzPopoverPlacement="topRight"
                  class="cursor-pointer"
                >
                  {{ fileType.source.name }}
                </span>
              } @else {
                {{ fileType.source.name }}
              }
            } @else if (fileType.contacts?.length) {
              @for (tc of fileType.contacts ?? []; track tc) {
                <div>
                  <ng-template #contactInfo>
                    <contact-info [info]="tc.contact" />
                  </ng-template>
                  <span
                    class="cursor-help"
                    nz-popover
                    [nzPopoverTitle]="tc.contact | personName"
                    [nzPopoverContent]="contactInfo"
                  >
                    {{ tc.contact | personName }}
                  </span>
                </div>
              }
            }
          </p>
        } @else {
          <span class="text-disabled">-</span>
        }
      </td>
      <td class="text-center">
        <boolean-icon [value]="!!fileType.parserId" nz-tooltip [nzTooltipTitle]="fileType.parserId ?? '-'" />
      </td>
      <td class="text-center">
        <boolean-icon [value]="!!fileType.downloaderId" nz-tooltip [nzTooltipTitle]="fileType.downloaderId ?? '-'" />
      </td>
      <td class="text-right">
        <action-buttons [actions]="fileTypeActions" [context]="fileType" size="small" />
      </td>
    </tr>
  }
</ng-template>

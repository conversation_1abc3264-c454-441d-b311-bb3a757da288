/**
 * Parse data from string using defined regexp parsers
 * @param content
 * @param parsers
 * @param initialValue
 */
export function parseDataWithRegExp<T extends object>(
  content: string,
  parsers: Array<{
    match: RegExp;
    data: (...args: string[]) => Partial<T>;
  }>,
  initialValue: T
): T {
  return parsers.reduce((result, parser) => {
    const match = content.match(parser.match);
    if (match) {
      Object.assign(result, parser.data(...match.slice(1)));
    }
    return result;
  }, initialValue);
}

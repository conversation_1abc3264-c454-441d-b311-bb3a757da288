import { CommonModule } from '@angular/common';
import { Component, Input } from '@angular/core';
import { FormArray, FormControl, FormGroup, ReactiveFormsModule, UntypedFormControl, Validators } from '@angular/forms';
import { defer, map, of, switchMap } from 'rxjs';
import { openEmailUpload } from '~/common/email/email-upload-component';
import { FileNameComponent } from '~/common/files/file-name-component';
import { Action, ActionButtonsComponent } from '~/common/ui/actions';
import { DrawerService } from '~/common/ui/drawer';
import { formValue$ } from '~/common/ui/forms';
import { NgZorroModule } from '~/common/ui/ng-zorro';
import { UploadButtonComponent } from '~/common/uploads/upload-button-component';
import { type UploadedFile } from '~/common/uploads/uploaded-file';
import { BillingFile } from './billing-file';
import { BillingFileStatus } from './billing-file-status';
import { BillingFilesApiClient } from './billing-files-api-client';

@Component({
  selector: 'billing-document-files-form',
  imports: [
    CommonModule,
    ReactiveFormsModule,
    NgZorroModule,
    ActionButtonsComponent,
    FileNameComponent,
    UploadButtonComponent
  ],
  templateUrl: './billing-document-files-form-component.html'
})
export class BillingDocumentFilesFormComponent {
  @Input()
  control: FormArray<FormGroup>;

  @Input()
  set value(files: BillingFile[]) {
    this.control.clear();
    for (const file of files) {
      this.add(file);
    }
  }

  get form() {
    return this.control.root! as FormGroup;
  }

  actions: Action<{ index: number; file: Record<string, any> }>[] = [
    {
      label: 'Delete',
      icon: 'delete',
      before: ({ index }) => {
        this.control.removeAt(index);
      }
    }
  ];

  // available files that can be linked with a document
  availableFiles$ = defer(() => formValue$(this.form, { fields: ['vendorId', 'accountId', 'cycle', 'files'] })).pipe(
    switchMap(({ vendorId, accountId, cycle, files }) => {
      if (vendorId && cycle) {
        const [cycleStartDate, cycleEndDate] = cycle;
        return this.filesApiClient
          .getFiles({
            vendorId,
            accountId,
            cycleStartDate: cycleStartDate?.toISOString(),
            cycleEndDate: cycleEndDate?.toISOString(),
            status: BillingFileStatus.Processed
          })
          .pipe(
            map((availableFiles) =>
              // exclude existing files
              availableFiles.filter(({ id }) => !files.some((file: BillingFile) => file.id === id))
            )
          );
      } else {
        return of([]);
      }
    })
  );

  availableFilesSelect = new UntypedFormControl();

  uploadActions: Action[] = [
    {
      label: 'Upload from e-mail',
      icon: 'mail',
      type: 'dashed',
      execute: () => openEmailUpload(this.drawerService).afterClose,
      success: (file: UploadedFile) => ({
        execute: () => {
          if (file) {
            this.onUpload(file);
          }
        }
      })
    }
  ];

  constructor(
    private drawerService: DrawerService,
    private filesApiClient: BillingFilesApiClient
  ) {}

  /**
   * Add file controls
   * @param file
   */
  add(data: { id?: string; name?: string; description?: string; url?: string; uploadId?: string }) {
    this.control.push(
      new FormGroup({
        id: new FormControl(data.id),
        name: new FormControl(data.name, [Validators.required]),
        description: new FormControl(data.description),
        url: new FormControl(data.url),
        uploadId: new FormControl(data.uploadId)
      })
    );
  }

  onUpload({ id, name }: UploadedFile) {
    this.add({ name, uploadId: id });
  }
}

import { Route } from '~/common/ui/routing';
import { BillingDocumentRead, BillingDocumentWrite } from './billing-document-permissions';

export const billingDocumentsRoute: Route = {
  path: 'documents',
  data: {
    title: 'Documents',
    authorize: BillingDocumentRead
  },
  children: [
    {
      path: '',
      pathMatch: 'full',
      loadComponent: () => import('./billing-documents-component').then((c) => c.BillingDocumentsComponent)
    },
    {
      path: 'create',
      loadComponent: () => import('./billing-document-form-component').then((c) => c.BillingDocumentFormComponent),
      data: {
        title: 'Create document',
        authorize: BillingDocumentWrite
      }
    },
    {
      path: 'edit/:documentId',
      loadComponent: () => import('./billing-document-form-component').then((c) => c.BillingDocumentFormComponent),
      data: {
        title: 'Edit document',
        authorize: BillingDocumentWrite
      }
    }
  ]
};

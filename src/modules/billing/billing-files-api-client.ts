import { Injectable } from '@angular/core';
import { map } from 'rxjs';
import { type ApiRequestOptions } from '~/common/api/api-client';
import { type EmailFilter } from '~/common/email/email-filter';
import { ServiceApiClient } from '~/common/services/service-api-client';
import { orderBy } from '~/common/utils/collection';
import { plainToClass } from '~/common/utils/class';
import { BillingFile } from './billing-file';
import { BillingFileStage } from './billing-file-stage';
import { BillingFileCreateDto, BillingFileUpdateDto, BillingFilesGenerateDto } from './billing-files-dto';
import { BillingFilesQueryParams } from './billing-files-query-params';

const baseUrl = 'billing/files';

@Injectable({ providedIn: 'root' })
export class BillingFilesApiClient {
  constructor(private apiClient: ServiceApiClient) {}

  getFiles(params: BillingFilesQueryParams = {}, options: ApiRequestOptions = {}) {
    return this.apiClient
      .get<BillingFile[]>(baseUrl, params, options)
      .pipe(map((files) => files.map((file) => ({ ...plainToClass(BillingFile, file), error: file.error }))));
  }

  getFile(id: string, params: BillingFilesQueryParams = {}) {
    return this.apiClient.get<BillingFile>(`${baseUrl}/${id}`, params, {
      responseType: BillingFile
    });
  }

  generateFiles(period: string, vendorId: string) {
    return this.apiClient.post(`${baseUrl}/generate`, { period, vendorId }, { bodyType: BillingFilesGenerateDto });
  }

  createFile(data: BillingFileCreateDto) {
    return this.apiClient.post(baseUrl, data, {
      bodyType: BillingFileCreateDto,
      responseType: BillingFile
    });
  }

  updateFile(id: string, data: BillingFileUpdateDto) {
    return this.apiClient.patch(`${baseUrl}/${id}`, data, {
      bodyType: BillingFileUpdateDto,
      responseType: BillingFile
    });
  }

  resetFile(id: string) {
    return this.apiClient.post(`${baseUrl}/${id}/reset`);
  }

  deleteFile(id: string) {
    return this.apiClient.delete(`${baseUrl}/${id}`);
  }

  getPipelineFileUrl(id: string) {
    return this.apiClient.get<BillingFileStage[]>(`${baseUrl}/${id}/pipeline`).pipe(
      map((stages) => {
        const url = orderBy(stages, ['createdDate'], ['desc']).find((stage) => Boolean(stage.url))?.url;
        if (url) {
          return url;
        } else {
          throw new Error('Unexpected error');
        }
      })
    );
  }

  retryFilePipeline(id: string) {
    return this.apiClient.post(`${baseUrl}/${id}/pipeline/retry`);
  }

  file(fileId: string) {
    const fileUrl = `${baseUrl}/${fileId}`;
    return {
      download: () => this.apiClient.post(`${fileUrl}/download`),
      email: (mailbox: string) => {
        const emailUrl = `${fileUrl}/email/${mailbox}`;
        return {
          messages: (filter: EmailFilter = {}) => this.apiClient.get<any[]>(emailUrl, filter),
          upload: (messageId: string, attachmentId: string, extracted?: string) =>
            this.apiClient.post(`${emailUrl}/upload`, { messageId, attachmentId, extracted }),
          extract: (messageId: string, attachmentId: string, password?: string) =>
            this.apiClient.post(`${emailUrl}/extract`, { messageId, attachmentId, password })
        };
      }
    };
  }
}

import { ActionHandler } from '~/common/actions';
import { ExecuteBillingFileStage } from './billing-file-actions';
import { BillingFileStageName } from './billing-file-stage-name';

export const BillingFileStageHandler = (name: BillingFileStageName) =>
  ActionHandler(ExecuteBillingFileStage, {
    when: ({ context }) => context.name === name,
    toArgs: ({ fileId, path, data }) => [fileId, path, data]
  });

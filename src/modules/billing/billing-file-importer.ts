import { asyncScheduler, filter, first, firstValueFrom, map, mergeMap, scheduled } from 'rxjs';
import { eachValueFrom } from 'rxjs-for-await';
import { BulkImporter, create, EntityManager, getEntityTable } from '~/common/database';
import { getErrorMessage, InvalidArgumentError, InvalidStateError, MissingArgumentError } from '~/common/errors';
import { EventBus } from '~/common/events';
import { Translator } from '~/common/i18n';
import { formatPeriod } from '~/common/periods';
import { Provider } from '~/common/providers';
import { isArray } from '~/common/utils/array';
import { lowerCaseKeys, mapKeys, mapValues } from '~/common/utils/object';
import { snakeCase } from '~/common/utils/string';
import { isVendorAccountValid, Vendor } from '~/common/vendors';
import { BillingFile } from './billing-file';
import { BillingFileData, BillingFileDataTranslateValue } from './billing-file-data';
import { BillingFileDataError } from './billing-file-data-error';
import { createDataValidator } from './billing-file-data-validators';
import { BillingFileImport } from './billing-file-import';

@Provider()
export class BillingFileImporter {
  constructor(
    private entityManager: EntityManager,
    private bulkImporter: BulkImporter,
    private translator: Translator,
    private eventBus: EventBus
  ) {}

  /**
   * Import billing data into database
   * @todo Refactor - split into smaller functions
   */
  async importData(file: BillingFile, config: BillingFileData) {
    const vendorId = file.vendorId;
    const vendor = await this.entityManager.findOne(Vendor, {
      where: { id: vendorId },
      relations: { country: true }
    });
    if (!vendor) {
      throw new InvalidArgumentError(`Invalid vendor "${vendorId}"`);
    }

    const { source, type } = config;
    const isRawType = typeof type === 'string';
    const tableName = await this.getTableName(vendorId, type);
    const context = { sheet: config.sheet };
    const meta = { vendorId, tableName };
    const aliases = config.aliases ? lowerCaseKeys(config.aliases) : null; // case-insensitive aliases

    function withDataContext(fn: (data: any) => any) {
      return (data: any) => {
        try {
          return fn(data);
        } catch (cause) {
          if (cause instanceof BillingFileDataError) {
            // add context to BillingFileDataError
            Object.assign(cause.data, context);
          } else if (cause instanceof InvalidArgumentError) {
            // transform any generic InvalidArgumentError (eg. from utils like "toDate") to BillingFileDataError as invalid data
            throw new BillingFileDataError(`Invalid data - ${getErrorMessage(cause)}`, { data, ...context }, { cause });
          }

          throw cause;
        }
      };
    }

    const dataProxy = (() => {
      if (isRawType) {
        // @hack ignore "toJSON" calls for JSON.stringify, "then" calls from firstValueFrom, "length" from mapValues etc.
        // https://javascript.info/json#custom-tojson
        const internalProps = ['toJSON', 'then', 'length'];
        const handler: ProxyHandler<any> = {
          get(data, field) {
            if (field in data) {
              return data[field];
            } else if (internalProps.includes(String(field))) {
              return undefined;
            } else {
              throw BillingFileDataError.MissingField(String(field), { data });
            }
          }
        };
        return (data: any) => new Proxy(data, handler);
      } else {
        return (data: any) => data;
      }
    })();

    const dataFilter = (() => {
      const { filter } = config;
      if (filter) {
        return withDataContext((data) => Boolean(typeof filter === 'string' ? data[filter] : filter(data)));
      } else {
        return () => true;
      }
    })();

    const dataTransformer = (() => {
      const { transform } = config;
      if (typeof transform === 'function') {
        return withDataContext(transform);
      } else if (typeof transform === 'object') {
        return withDataContext((row: any) =>
          mapValues(row, (value, key) => (transform[key] ? transform[key](value) : value))
        );
      } else {
        return (row: any) => row;
      }
    })();

    const source$ = (isArray(source) ? scheduled(source, asyncScheduler) : source).pipe(
      // apply aliases and rename fields if required
      map((row: object) => (aliases ? mapKeys(row, (value, key) => aliases[key.toLowerCase()] ?? key) : row)),
      // wrap data into proxy to catch missing fields
      map(dataProxy),
      // apply data filter
      filter(dataFilter),
      // additional data transformation
      map(dataTransformer)
    );

    const startDate = new Date();
    this.eventBus.verbose('importing', 'Importing data', meta);

    // take first row from data stream to analyze keys
    const firstRow = await firstValueFrom(source$.pipe(first(null, null)));
    if (!firstRow) {
      if (config.allowEmpty) {
        return;
      } else {
        if (config.filter) {
          const period = `"${formatPeriod(file.period)}"`;
          throw new BillingFileDataError(`No data found for ${period}`, context, {
            hints: [`This file type can contain historical data and no data was found for ${period}.`]
          });
        } else {
          throw new BillingFileDataError('No data found', context);
        }
      }
    }

    interface Field {
      column: string;
      value?: string;
      translate?: BillingFileDataTranslateValue;
    }

    const fields: Record<string, Field> = {};

    function addField(field: string, meta: Partial<Field>) {
      let column = meta.column ?? field;

      if (isRawType) {
        // snake_case column for raw data
        column = snakeCase(column);

        // prefix columns starting with a number
        if (column.match(/^[0-9]/)) {
          column = '_' + column;
        }

        // suffix duplicate columns
        while (Object.values(fields).some((field) => field.column === column)) {
          column += '_';
        }
      }

      fields[field] = { ...meta, column };
    }

    for (const field of config.fields ?? Object.keys(firstRow)) {
      let column = field;

      // translate when requested (eg. non-latin characters like Japanese etc.)
      if (config.translateKeys) {
        column = config.translateKeys[field];
        if (!column) {
          throw new MissingArgumentError(`Translation for field "${field}" is not defined`);
        }
      }

      addField(field, { column: column });
    }

    // add additional translated fields when requested
    if (config.translateValues) {
      for (const tv of config.translateValues) {
        const translate: BillingFileDataTranslateValue = typeof tv === 'string' ? { field: tv } : tv;
        const field = translate.field;
        if (fields[field]) {
          addField(field + ':English', { column: fields[field].column + '_en', translate });
        } else {
          throw BillingFileDataError.MissingField(field, { ...context, data: firstRow });
        }
      }
    }

    // include file id
    if (isRawType) {
      addField('fileId', { value: file.id });
    }

    const dataValidator = (() => {
      const { validate } = config;
      if (validate) {
        return withDataContext(
          createDataValidator(validate, {
            type: file.type?.refId,
            period: file.period,
            accountId:
              file.account?.refId ??
              file.vendor!.accounts?.filter((account) => isVendorAccountValid(account)).map(({ refId }) => refId)
          })
        );
      } else {
        return () => true;
      }
    })();

    const dataMapping = async (row: any) => {
      const item = isRawType ? ({} as any) : row;

      for (const [field, { column, value, translate }] of Object.entries(fields)) {
        if (value) {
          // same value for all records (file_id)
          item[column] = value;
        } else if (translate) {
          const { field, when, transform } = translate;
          const value = row[field];

          // translate value when requested
          const canTranslate = when ? when(value, row) : true;
          if (canTranslate) {
            item[column] = await this.translator.translate(
              transform ? transform(value) : value,
              vendor.country!.languageId,
              'en'
            );
          } else {
            item[column] = value;
          }
        } else {
          // value from data
          item[column] = row[field];
        }
      }

      return item;
    };

    const columns = Object.values(fields).map((field) => field.column);
    const import$ = source$.pipe(
      // validate data
      filter(dataValidator),
      // final mapping, translate values etc.
      mergeMap(dataMapping)
    );

    // delete previous import (deletes previous data from target table)
    await this.entityManager.delete(BillingFileImport, { fileId: file.id, tableName });

    // run the import
    try {
      let recordsCount: number;
      if (isRawType) {
        recordsCount = await this.bulkImporter.importData(tableName, import$, { columns });
      } else {
        recordsCount = 0;
        await this.entityManager.transaction(async (entityManager) => {
          for await (const item of eachValueFrom(import$)) {
            await entityManager.insert(type, item);
            recordsCount++;
          }
        });
      }

      // save real cycle (if parsed)
      if (config.cycle) {
        [file.cycleStartDate, file.cycleEndDate] = config.cycle;
        await this.entityManager.save(file);
      }

      // custom callback after import
      if (config.after) {
        await config.after(file);
      }

      // save import
      if (isRawType) {
        await this.entityManager.save(
          create(BillingFileImport, {
            fileId: file.id!,
            tableName,
            startDate,
            finishDate: new Date(),
            recordsCount,
            dataset: snakeCase(type)
          })
        );
      }

      const duration = (Date.now() - startDate.getTime()) / 1000;
      this.eventBus.info('imported', 'Data imported', { ...meta, recordsCount, duration });
    } catch (cause) {
      const message = getErrorMessage(cause);

      // transform low level SQL errors into user-friendly errors with hints
      // 1) "column does not exist"
      const noColumn = message.match(/column "(.+)" of relation ".+" does not exist/i);
      if (noColumn) {
        const field = Object.keys(fields).find((field) => fields[field].column === noColumn[1]);
        throw new BillingFileDataError(
          `Unexpected field "${field}" found in the data`,
          { data: firstRow, field, ...context },
          { cause, hints: config.sheet ? ['If the field is not important, remove it and try again.'] : [] }
        );
      }
      // 2) "relation does not exist"
      const noTable = message.match(/relation "(.+)" does not exist/i);
      if (noTable) {
        const table = noTable[1];
        const sql = `CREATE TABLE ${table} (\n${columns
          .map((column) => `  ${column} ${column === 'file_id' ? 'uuid' : 'text'}`)
          .join(',\n')}\n);`;
        throw new InvalidStateError(`Table "${table}" does not exists`, {}, { cause, hints: [`{code}${sql}{/code}`] });
      }

      throw cause;
    }
  }

  /**
   * Get table name for a given vendor and data type
   * @param vendorId
   * @param type
   */
  private async getTableName(vendorId: string, type: BillingFileData['type']): Promise<string> {
    if (typeof type === 'string') {
      const { name, schema } = await this.entityManager.findOneByOrFail(Vendor, { id: vendorId });
      if (schema) {
        return `${schema}.raw_${snakeCase(type)}`;
      } else {
        throw new Error(`Schema not defined for vendor "${name}"`);
      }
    } else {
      const tableName = getEntityTable(type)?.name;
      if (tableName) {
        return tableName;
      } else {
        throw new Error(`Entity not defined for class "${type.name}"`);
      }
    }
  }
}

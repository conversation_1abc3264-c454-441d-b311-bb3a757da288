import { CommonModule } from '@angular/common';
import { Component, Inject } from '@angular/core';
import { FormArray, FormControl, FormGroup, ReactiveFormsModule } from '@angular/forms';
import { NZ_MODAL_DATA, NzModalRef } from 'ng-zorro-antd/modal';
import { combineLatest, of } from 'rxjs';
import { PeriodNameComponent } from '~/common/periods/period-name-component';
import { getCurrentPeriod, getPreviousPeriod } from '~/common/periods/period-utils';
import { Action, ActionButtonComponent } from '~/common/ui/actions';
import { formValid$ } from '~/common/ui/forms';
import { LoaderComponent, createLoader } from '~/common/ui/loader';
import { NgZorroModule } from '~/common/ui/ng-zorro';
import { BillingFile } from './billing-file';
import { BillingFileType } from './billing-file-type';
import { BillingFileTypesApiClient } from './billing-file-types-api-client';

export interface BillingFileTypeThresholdsFormData {
  typeId: string;
  accountId?: string;
  dataset?: string;
}

@Component({
  selector: 'billing-file-type-thresholds-form',
  imports: [
    CommonModule,
    ReactiveFormsModule,
    NgZorroModule,
    ActionButtonComponent,
    LoaderComponent,
    PeriodNameComponent
  ],
  templateUrl: './billing-file-type-thresholds-form-component.html'
})
export class BillingFileTypeThresholdsFormComponent {
  form = new FormArray<FormGroup>([]);

  typeLoader = createLoader<BillingFileType>(
    () => this.fileTypeRepository.getFileType(this.modalData.typeId, { expand: ['thresholds'] }),
    {
      onData: (type) => {
        this.updateForm(type);
      }
    }
  );

  importsLoader = createLoader(
    (dataset) =>
      this.fileTypeRepository.getFileTypeImports(this.modalData.typeId, {
        accountId: this.modalData.accountId,
        dataset,
        fromPeriod: getPreviousPeriod(getCurrentPeriod(), 12),
        sort: 'period:desc' as any
      }),
    {
      lazy: true
    }
  );

  get selectedIndex() {
    const dataset = this.modalData.dataset?.toUpperCase();
    for (const [index, { value }] of this.form.controls.entries()) {
      if (value.dataset.toUpperCase() === dataset) {
        return index;
      }
    }
    return 0;
  }

  constructor(
    private modalRef: NzModalRef,
    @Inject(NZ_MODAL_DATA) private modalData: BillingFileTypeThresholdsFormData,
    private fileTypeRepository: BillingFileTypesApiClient
  ) {}

  submitAction: Action<BillingFile> = {
    id: 'submit',
    label: 'Submit',
    type: 'primary',
    icon: 'save',
    enabled: formValid$(this.form),
    execute: (type) => {
      const thresholds = this.form.getRawValue();
      return combineLatest(
        thresholds.map((threshold) => {
          const isEmpty = threshold.minRecordsCount === null && threshold.maxRecordsCount === null;
          if (isEmpty) {
            if (threshold.id) {
              return this.fileTypeRepository.deleteFileTypeThreshold(type.id, threshold.id);
            } else {
              return of(null);
            }
          } else if (threshold.id) {
            return this.fileTypeRepository.updateFileTypeThreshold(type.id, threshold.id, threshold);
          } else {
            return this.fileTypeRepository.createFileTypeThreshold(type.id, <any>{
              ...threshold,
              accountId: this.modalData.accountId
            });
          }
        })
      );
    },
    success: () => ({
      message: 'Thresholds have been updated',
      execute: () => {
        this.modalRef.close(true);
      }
    })
  };

  private updateForm(type: BillingFileType) {
    // build thresholds form controls for each dataset
    this.form.clear();
    for (const dataset of type.datasets ?? []) {
      const { accountId } = this.modalData;
      const threshold = type.thresholds?.find(
        (t) => t.dataset === dataset && (accountId ? t.accountId === accountId : !t.accountId)
      );
      const control = new FormGroup({
        id: new FormControl(threshold?.id),
        dataset: new FormControl(dataset, { nonNullable: true }),
        minRecordsCount: new FormControl(threshold?.minRecordsCount),
        maxRecordsCount: new FormControl(threshold?.maxRecordsCount)
      });
      this.form.push(control);
    }
  }
}

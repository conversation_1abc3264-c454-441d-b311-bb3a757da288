import { CommonModule } from '@angular/common';
import { Component } from '@angular/core';
import { ReactiveFormsModule, UntypedFormControl, UntypedFormGroup } from '@angular/forms';
import { ActivatedRoute } from '@angular/router';
import { NzImageService } from 'ng-zorro-antd/image';
import { NzModalService } from 'ng-zorro-antd/modal';
import { combineLatest, firstValueFrom, map, of, shareReplay, switchMap } from 'rxjs';
import { DataSourceType, dataSourceTypes } from '~/common/data-sources/data-source-type';
import { FileNameComponent } from '~/common/files/file-name-component';
import { ListSelectComponent } from '~/common/lists/list-select-component';
import { CycleComponent } from '~/common/periods/cycle-component';
import { PeriodNameComponent } from '~/common/periods/period-name-component';
import { PeriodSelectComponent } from '~/common/periods/period-select-component';
import { SearchInputComponent } from '~/common/search/search-input-component';
import { CurrentService } from '~/common/services/current-service';
import { Action, ActionButtonsComponent } from '~/common/ui/actions';
import { CollectionColumn, CollectionPageComponent } from '~/common/ui/collections';
import { HelpIconComponent } from '~/common/ui/components/help-icon';
import { DrawerService } from '~/common/ui/drawer';
import { formControlValue$, FormRouteSyncConfig, FormRouteSyncDirective } from '~/common/ui/forms';
import { CurrentUser } from '~/common/ui/identity';
import { Loader } from '~/common/ui/loader';
import { NgZorroModule } from '~/common/ui/ng-zorro';
import { openWindow } from '~/common/ui/utils';
import { writeXlsxFile } from '~/common/ui/xlsx';
import { toISODate } from '~/common/utils/date';
import { omit, pick } from '~/common/utils/object';
import { upperFirst } from '~/common/utils/string';
import { VendorAccountNameComponent } from '~/common/vendors/vendor-account-name-component';
import { VendorAccountSelectComponent } from '~/common/vendors/vendor-account-select-component';
import { getVendorAccountName } from '~/common/vendors/vendor-account-utils';
import { VendorNameComponent } from '~/common/vendors/vendor-name-component';
import { VendorSelectComponent } from '~/common/vendors/vendor-select-component';
import { getVendorFullName } from '~/common/vendors/vendors-utils';
import { openBillingDocument } from './billing-document-detail-component';
import { BillingDocumentRead, BillingDocumentWrite } from './billing-document-permissions';
import { billingDocumentTypes } from './billing-document-type';
import { BillingFile } from './billing-file';
import { BillingFileCheck } from './billing-file-check';
import { BillingFileDownload } from './billing-file-download';
import { BillingFileDownloadStatus, billingFileDownloadStatuses } from './billing-file-download-status';
import { BillingFileErrorComponent } from './billing-file-error-component';
import { BillingFileFormComponent, BillingFileFormData } from './billing-file-form-component';
import { BillingFileWrite } from './billing-file-permissions';
import { BillingFileStatus, billingFileStatuses } from './billing-file-status';
import { BillingFileStatusComponent } from './billing-file-status-component';
import { BillingFileTypeWrite } from './billing-file-type-permissions';
import {
  BillingFileTypeThresholdsFormComponent,
  BillingFileTypeThresholdsFormData
} from './billing-file-type-thresholds-form-component';
import { isDataBillingFileType } from './billing-file-type-utils';
import { canDeleteBillingFile, canResetBillingFile, canUploadBillingFile } from './billing-file-utils';
import { BillingFilesApiClient } from './billing-files-api-client';
import { BillingFilesGenerateFormComponent } from './billing-files-generate-form-component';
import { BillingFilesQueryParams } from './billing-files-query-params';

interface BillingFileListItem extends BillingFile {
  isOverdue: boolean;
}

@Component({
  selector: 'billing-files',
  imports: [
    CommonModule,
    ReactiveFormsModule,
    NgZorroModule,
    ActionButtonsComponent,
    CollectionPageComponent,
    CycleComponent,
    FileNameComponent,
    FormRouteSyncDirective,
    HelpIconComponent,
    ListSelectComponent,
    PeriodNameComponent,
    PeriodSelectComponent,
    SearchInputComponent,
    VendorNameComponent,
    VendorSelectComponent,
    VendorAccountNameComponent,
    VendorAccountSelectComponent,
    BillingFileErrorComponent,
    BillingFileStatusComponent
  ],
  templateUrl: './billing-files-component.html',
  styleUrl: './billing-files-component.less'
})
export class BillingFilesComponent {
  /**
   * Filter by vendor and period
   */
  filter = new UntypedFormGroup({
    search: new UntypedFormControl(),
    vendorId: new UntypedFormControl(),
    accountId: new UntypedFormControl(),
    period: new UntypedFormControl(),
    status: new UntypedFormControl(),
    isOverdue: new UntypedFormControl(),
    isExpected: new UntypedFormControl(),
    hasDownload: new UntypedFormControl()
  });

  filterRouteSync: FormRouteSyncConfig = {
    fromQuery: (params) => ({
      ...params,
      isOverdue: params.isOverdue === 'true',
      hasDownload: params.hasDownload === 'true'
    }),
    toQuery: (filter) => ({
      ...omit(filter, 'search'),
      accountId: filter.vendorId ? filter.accountId : undefined,
      isOverdue: filter.isOverdue ? 'true' : undefined,
      hasDownload: filter.hasDownload ? 'true' : undefined
    })
  };

  search$ = formControlValue$(this.filter.controls.search, { search: true }).pipe(shareReplay(1));
  params$ = combineLatest([this.route.queryParams, this.search$]).pipe(
    map(([params, search]) => ({ ...params, search }))
  );

  /**
   * Files loader
   */
  loader = new Loader<BillingFileListItem[]>(
    (params, { limit }) => this.getFiles({ ...params, limit }, { entityEvents: true }),
    { from: this.params$, limit: 15 }
  );

  statuses = billingFileStatuses;

  /**
   * Global actions
   */
  globalActions: Action[] = [
    {
      icon: 'file-excel',
      label: 'Export',
      execute: () => this.exportFiles(),
      error: (e) => ({
        message: e.message ?? 'Failed to export files'
      })
    },
    {
      icon: 'field-time',
      tooltip: 'Generate expected files',
      authorize: BillingFileWrite,
      execute: () => this.openFilesGenerateForm(),
      success: (submitted) => ({
        message: submitted ? 'Expected files generated successfully.' : undefined
      })
    }
  ];

  /**
   * File actions
   */
  fileActions: Action<BillingFile>[] = [
    {
      id: 'upload',
      tooltip: 'Upload file',
      icon: 'upload',
      authorize: BillingFileWrite,
      allowed: canUploadBillingFile,
      execute: (file) => this.openFileForm('upload', file)
    },
    {
      label: 'Download',
      icon: 'cloud-download',
      menu: true,
      authorize: BillingFileWrite,
      allowed: ({ type, status }) =>
        status === BillingFileStatus.Expected && Boolean(type?.sourceType === DataSourceType.Portal && type?.sourceId),
      execute: async (file) => {
        const { DataSourceComponent } = await import('~/common/data-sources/data-source-component');
        const modal = this.modalService.create({
          nzTitle: 'Portal download details',
          nzWidth: 550,
          nzFooter: null,
          nzContent: DataSourceComponent,
          nzData: { vendorId: file.vendorId, sourceId: file.type?.sourceId, accountId: file.accountId }
        });
        return modal.afterClose;
      }
    },
    {
      label: 'Download',
      icon: 'cloud-download',
      menu: true,
      authorize: BillingFileWrite,
      allowed: ({ type, status }) =>
        status === BillingFileStatus.Expected && Boolean(type?.sourceType !== DataSourceType.Portal && type?.sourceId),
      execute: (file) => this.filesRepository.file(file.id).download(),
      success: () => ({
        message: 'Download initiated'
      }),
      error: () => ({
        message: 'Failed to initiate download'
      })
    },
    {
      label: 'Edit',
      icon: 'edit',
      menu: true,
      authorize: BillingFileWrite,
      allowed: (file) => file.status === BillingFileStatus.Processed,
      execute: (file) => this.openFileForm('edit', file)
    },
    {
      label: 'Retry',
      icon: 'reload',
      menu: true,
      authorize: BillingFileWrite,
      allowed: (file) => file.status === BillingFileStatus.Failed,
      execute: (file) => this.filesRepository.retryFilePipeline(file.id)
    },
    {
      label: 'Reset',
      icon: 'undo',
      menu: true,
      authorize: BillingFileWrite,
      allowed: canResetBillingFile,
      confirm: (file) => {
        const relations: string[] = [];
        if (file.documentId) {
          relations.push('the associated document');
        }
        if (file.type && isDataBillingFileType(file.type)) {
          relations.push('loaded data');
        }
        return {
          title: 'Are you sure you want to reset this file?',
          description:
            'File will be set to expected state.' +
            (relations.length ? ` This action also <strong>deletes ${relations.join(' and ')}</strong>.` : '')
        };
      },
      execute: (file) => this.filesRepository.resetFile(file.id),
      success: () => ({
        message: 'File reset successfully'
      }),
      error: () => ({
        message: 'Failed to reset file'
      })
    },
    {
      id: 'thresholds',
      label: 'Thresholds',
      icon: 'tool',
      menu: true,
      allowed: ({ type }) => (type ? isDataBillingFileType(type) : false),
      authorize: BillingFileTypeWrite,
      execute: (file) => this.openFileTypeThresholdForm(file)
    },
    {
      label: 'Configure type',
      icon: 'setting',
      menu: true,
      authorize: BillingFileTypeWrite,
      allowed: (file) => Boolean(file.typeId),
      execute: async (file) => {
        const { openBillingFileTypeForm } = await import('./billing-file-type-form-component');
        return openBillingFileTypeForm(this.modalService, { fileTypeId: file.typeId });
      }
    },
    {
      label: 'Parse document',
      icon: 'file-search',
      menu: true,
      authorize: BillingDocumentWrite,
      allowed: (file) =>
        file.status === BillingFileStatus.Processed && Boolean(file.type?.documentType && file.type.parserId),
      execute: ({ id, vendorId, documentId }) =>
        openBillingDocument(this.drawerService, { documentId, parse: { fileId: id, vendorId } })
    },
    {
      icon: 'delete',
      label: 'Delete',
      menu: true,
      authorize: BillingFileWrite,
      allowed: canDeleteBillingFile,
      confirm: () => ({
        title: 'Are you sure you want to delete this file?'
      }),
      execute: (file) => this.filesRepository.deleteFile(file.id),
      success: () => ({
        message: 'File deleted successfully'
      }),
      error: () => ({
        message: 'Failed to delete file'
      })
    }
  ];

  columns: CollectionColumn[] = [
    { title: 'Vendor / Account', width: '12rem' },
    { title: 'Period', width: '13rem' },
    { title: 'Name' },
    { title: 'Status', width: '12rem' },
    { width: '6rem' }
  ];

  constructor(
    private route: ActivatedRoute,
    private drawerService: DrawerService,
    private modalService: NzModalService,
    private imageService: NzImageService,
    public service: CurrentService,
    private filesRepository: BillingFilesApiClient,
    private user: CurrentUser
  ) {}

  getFiles(params: BillingFilesQueryParams, options: { entityEvents?: boolean } = {}) {
    return this.filesRepository
      .getFiles(
        {
          ...omit(params, ['r']),
          expand: ['checks', 'document', 'download', 'type']
        },
        {
          onAction: true,
          onEntityEvents: options.entityEvents ? [BillingFile, BillingFileDownload, BillingFileCheck] : undefined
        }
      )
      .pipe(
        map((files) => {
          const currentDay = toISODate();
          return files.map((file) => ({
            ...file,
            isOverdue: file.expectedDate
              ? currentDay > file.expectedDate.toISOString() && file.status !== BillingFileStatus.Processed
              : false
          }));
        })
      );
  }

  /**
   * Open file form dialog
   */
  openFileForm(mode: 'upload' | 'edit', file?: BillingFile) {
    this.modalService.create<BillingFileFormComponent, BillingFileFormData>({
      nzTitle: `${upperFirst(mode)} file`,
      nzWidth: 550,
      nzFooter: null,
      nzContent: BillingFileFormComponent,
      nzData: {
        file: file ?? pick(this.filter.value, ['vendorId', 'accountId', 'period']),
        mode,
        relativeTo: this.route
      }
    });
  }

  /**
   * Open file generate form dialog
   */
  openFilesGenerateForm() {
    this.modalService.create({
      nzTitle: 'Generate expected files',
      nzWidth: 550,
      nzContent: BillingFilesGenerateFormComponent,
      nzData: {
        period: this.filter.value.period,
        vendorId: this.filter.value.vendorId
      },
      nzFooter: null
    });
  }

  /**
   * Open file generate form dialog
   */
  openFileTypeThresholdForm(file: BillingFile) {
    const modal = this.modalService.create<BillingFileTypeThresholdsFormComponent, BillingFileTypeThresholdsFormData>({
      nzTitle:
        'File type thresholds' +
        (file.account ? ` (${getVendorAccountName(file.account, { service: this.service.get() })})` : ''),
      nzWidth: 550,
      nzFooter: null,
      nzContent: BillingFileTypeThresholdsFormComponent,
      nzData: {
        typeId: file.typeId!,
        accountId: file.accountId,
        dataset: file.error?.data?.dataset
      }
    });
    return modal.afterClose.pipe(
      switchMap((submitted) => {
        if (submitted && file.status === BillingFileStatus.Failed) {
          // retry failed file check when threshold updated
          return this.filesRepository.retryFilePipeline(file.id);
        } else {
          return of(null);
        }
      })
    );
  }

  /**
   * Can user open a document
   */
  canOpenDocument() {
    return this.user.can(BillingDocumentRead);
  }

  /**
   * Open a document where file is attached
   */
  async openDocument(documentId: string) {
    openBillingDocument(this.drawerService, { documentId });
  }

  /**
   * Return document type name by ID
   * @param type
   */
  getDocumentType = billingDocumentTypes.extractor('name');

  /**
   * Return download details
   */
  getDownload(file: BillingFile) {
    if (!file.download) {
      return;
    }

    const { download, status } = file;
    if (status === BillingFileStatus.Processed && download.status !== BillingFileDownloadStatus.Finished) {
      return;
    }

    return {
      data: download,
      color: billingFileDownloadStatuses.get(download.status).color,
      icon: dataSourceTypes.get(download.sourceType).icon!,
      status: (() => {
        const { sourceType, message, status, userId } = download;
        if (status === BillingFileDownloadStatus.Finished) {
          const sourceName = dataSourceTypes.get(sourceType).name;
          let message = `Downloaded from ${sourceName.match(/[a-z]/) ? sourceName.toLowerCase() : sourceName}`;
          if (userId) {
            message += ` by ${userId}`;
          }
          return message;
        } else {
          return `Download ${status}${message ? ` - "${message}"` : ''}`;
        }
      })(),
      open: () => {
        if (download.screenshotUrl) {
          this.imageService.preview([{ src: download.screenshotUrl }]);
        } else if (download.sourceUrl) {
          openWindow(
            download.sourceUrl,
            download.sourceType === DataSourceType.Email ? { width: 800, height: 600 } : { target: '_blank' }
          );
        }
      }
    };
  }

  /**
   * Export files to XLSX
   * @todo Implement more universal export from table
   */
  async exportFiles() {
    const params = await firstValueFrom(this.params$);
    const files = await firstValueFrom(this.getFiles(params));
    await writeXlsxFile(
      'billing-files.xlsx',
      [
        'Vendor',
        'Account',
        'Period',
        'Cycle start date',
        'Cycle end date',
        'Expected date',
        'Name',
        'Description',
        'Document ID',
        'Document type',
        'Status',
        'Error message'
      ],
      files.map((file) => [
        getVendorFullName(file.vendor!),
        file.account ? getVendorAccountName(file.account, { service: this.service.get() }) : undefined,
        file.period,
        file.cycleStartDate,
        file.cycleEndDate,
        file.expectedDate,
        file.name,
        file.description,
        file.document?.refId,
        file.document?.type ? this.getDocumentType(file.document.type) : undefined,
        billingFileStatuses.get(file.status).name,
        file.error?.message
      ])
    );
  }
}

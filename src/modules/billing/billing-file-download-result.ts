import { BillingFileDownloadStatus } from './billing-file-download-status';

export interface BillingFileDownloadResult<T extends Record<string, any> = any> {
  /**
   * Original file name
   */
  name?: string;

  /**
   * Downloaded file path
   */
  path?: string;

  /**
   * Context data related to download, e.g. email details
   */
  data?: T;

  /**
   * Status other than finished (eg. "requested" when file has to be requested first in order to download it after some time)
   */
  status?: BillingFileDownloadStatus;

  /**
   * Download retry timeout (in seconds) sooner than next day (eg. when "requested", try in 1 hour)
   */
  retryTimeout?: number;

  /**
   * Related message to store (eg. "not found", but retry in 1 hour)
   */
  message?: string;
}

import { CommonModule } from '@angular/common';
import { Component, computed, input } from '@angular/core';
import { NgZorroModule } from '~/common/ui/ng-zorro';
import { isDefined } from '~/common/utils/value';
import { BillingDocumentItem } from './billing-document-item';

@Component({
  selector: 'billing-document-items',
  imports: [CommonModule, NgZorroModule],
  templateUrl: './billing-document-items-component.html'
})
export class BillingDocumentItemsComponent {
  items = input.required<BillingDocumentItem[]>();
  currency = input('USD');
  withQuantity = computed(() => this.items().some((item) => isDefined(item.quantity)));
}

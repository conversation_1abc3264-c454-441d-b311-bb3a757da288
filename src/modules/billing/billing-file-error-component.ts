import { CommonModule } from '@angular/common';
import { ChangeDetectionStrategy, Component, Input } from '@angular/core';
import { defer } from 'rxjs';
import { fromErrorPayload, getErrorData, getErrorHints } from '~/common/errors';
import { FileNameComponent } from '~/common/files/file-name-component';
import { HtmlPipe } from '~/common/html/html-pipe';
import { Action, ActionButtonsComponent } from '~/common/ui/actions';
import { ScrollIntoDirective } from '~/common/ui/directives/scroll-into';
import { downloadFile } from '~/common/ui/files';
import { NgZorroModule } from '~/common/ui/ng-zorro';
import { EntriesPipe } from '~/common/utils/object/entries-pipe';
import { BillingFile } from './billing-file';
import { BillingFileDataError } from './billing-file-data-error';
import { BillingFilesApiClient } from './billing-files-api-client';

@Component({
  selector: 'billing-file-error',
  imports: [
    CommonModule,
    NgZorroModule,
    ActionButtonsComponent,
    EntriesPipe,
    FileNameComponent,
    HtmlPipe,
    ScrollIntoDirective
  ],
  templateUrl: './billing-file-error-component.html',
  styleUrl: './billing-file-error-component.less',
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class BillingFileErrorComponent {
  @Input('file')
  file: BillingFile;

  @Input()
  actions: Action<BillingFile>[];

  error: Error;

  url$ = defer(() => this.filesRepository.getPipelineFileUrl(this.file.id));

  constructor(private filesRepository: BillingFilesApiClient) {}

  ngOnChanges() {
    this.error = fromErrorPayload(this.file.error!);
  }

  formatMessage(message?: string) {
    if (!message) {
      message = this.error.message;
      if (this.error instanceof BillingFileDataError && this.error.data.expected) {
        const { expected, received } = this.error.data;
        const toValue = (value: any) => `"${typeof value === 'string' ? value : JSON.stringify(value)}"`;
        message += ` - expected ${toValue(expected)}`;
        if (received) {
          message += `, but received ${toValue(received)}`;
        }
      }
    }

    return message
      .replace(/"([^"]+)"/g, '<span class="text-info">$1</span>')
      .replaceAll('{code}', '<pre>')
      .replaceAll('{/code}', '</pre>');
  }

  getData() {
    const data = getErrorData(this.error);
    return Object.keys(data).length ? data : null;
  }

  getHints() {
    return [
      ...(getErrorHints(this.error) ?? []),
      'Check if you are uploading the correct expected file.',
      'Compare the data with previous periods and contact vendor if you find any discrepancies.',
      'Otherwise contact TEM team for further assistance.'
    ];
  }

  /**
   * Return actions for the error
   */
  getActions() {
    if (this.error instanceof BillingFileDataError && this.error.code === 'THRESHOLDS') {
      return this.actions.filter((action) => action.id === 'thresholds');
    }
  }

  getFieldRef(field: string) {
    return `[data-field="${field.replace(/(["'])/g, '\\$1')}"]`;
  }

  downloadFile() {
    this.filesRepository.getPipelineFileUrl(this.file.id).subscribe(downloadFile);
  }
}

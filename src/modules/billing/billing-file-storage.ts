import { readFile, unlink } from 'fs-extra';
import { tmpdir } from 'os';
import { Readable } from 'stream';
import { extractFile } from '~/common/compression';
import { EntityManager, EntityOperation, EntityTrigger, type EntityTriggerPayload } from '~/common/database';
import { FileStorage } from '~/common/files';
import { getRequestUser } from '~/common/identity';
import { Worker } from '~/common/jobs';
import { Provider } from '~/common/providers';
import { BillingFile } from './billing-file';
import { createBillingFileQuery } from './billing-file-query';
import { BillingFileStatus } from './billing-file-status';

/**
 * Return billing file storage base path
 * @param fileId
 */
export function getBillingFileStorageBasePath(fileId: string) {
  return `billing/${fileId}`;
}

const servePath = 'billing/:fileId';

@Provider()
export class BillingFileStorage {
  constructor(
    private entityManager: EntityManager,
    private fileStorage: FileStorage
  ) {
    // serve storaged files by id with their original names + '.zip'
    fileStorage.serve(servePath, {
      directory: true,
      handler: async (req) => {
        const file = await createBillingFileQuery(this.entityManager, { user: getRequestUser(req) })
          .andWhereInIds([req.params.fileId])
          .getOne();

        if (file) {
          if (req.path === '/') {
            // requesting file content
            return {
              path: this.getFilePath(file.id),
              name: file.name,
              extract: file.compressed
            };
          } else {
            // requesting file subcontent (eg. download screenshot)
            return {
              path: getBillingFileStorageBasePath(file.id) + decodeURI(req.path)
            };
          }
        }
      }
    });
  }

  /**
   * Return file path in storage
   */
  getFilePath(fileId: string) {
    return getBillingFileStorageBasePath(fileId) + `/${fileId}.zip`;
  }

  /**
   * Return storaged file url
   */
  getFileUrl(fileId: string) {
    return this.fileStorage.createUrl(servePath.replace(':fileId', fileId));
  }

  /**
   * Read file from storage
   */
  async readFile(fileId: string) {
    const { compressed } = await this.entityManager.findOneByOrFail(BillingFile, { id: fileId });
    const path = this.fileStorage.getAbsolutePath(this.getFilePath(fileId));
    const [file] = compressed ? await extractFile(path, tmpdir()) : [path];
    const content = await readFile(file);
    if (compressed) {
      await unlink(file);
    }
    return content;
  }

  /**
   * Write file to storage
   */
  async writeFile(fileId: string, content: Readable) {
    await this.fileStorage.writeFile(this.getFilePath(fileId), content);
  }

  /**
   * Delete file from storage
   *
   * File is scheduled for deletion (job task) in AFTER DELETE trigger
   *
   * @see billing-files-storage-delete-trigger.sql
   */
  @Worker({
    description: 'Delete billing file from storage'
  })
  async deleteFile(fileId: string) {
    await this.fileStorage.delete(getBillingFileStorageBasePath(fileId));
  }

  @EntityTrigger({
    description: 'Reset URL on file reset',
    before: [EntityOperation.Update],
    entity: BillingFile,
    // status is reset => set to "expected"
    changed: ['status'],
    filter: ({ entity: file }) => file.status === BillingFileStatus.Expected
  })
  onFileReset({ entity: file }: EntityTriggerPayload<BillingFile>) {
    // reset url
    file.url = null!;
  }
}

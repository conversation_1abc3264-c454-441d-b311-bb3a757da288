import { pathExists } from 'fs-extra';
import { basename } from 'path';
import { ActionBus } from '~/common/actions';
import { DataSourceType } from '~/common/data-sources';
import { EntityManager, create } from '~/common/database';
import { InvalidArgumentError, getErrorMessage, toErrorPayload } from '~/common/errors';
import { EventBus } from '~/common/events';
import { FileStorage, sanitizeFilename } from '~/common/files';
import { JobBus, Worker } from '~/common/jobs';
import { Provider } from '~/common/providers';
import { UploadStorage } from '~/common/uploads';
import { pick } from '~/common/utils/object';
import { BillingFile } from './billing-file';
import { ExecuteBillingFileStage } from './billing-file-actions';
import { BillingFileDataError } from './billing-file-data-error';
import { BillingFileDownload } from './billing-file-download';
import { BillingFileDownloadStatus } from './billing-file-download-status';
import { BillingFileStage } from './billing-file-stage';
import { type BillingFileStageData } from './billing-file-stage-data';
import { BillingFileStageName } from './billing-file-stage-name';
import { BillingFileStageStatus } from './billing-file-stage-status';
import { BillingFileStatus } from './billing-file-status';
import { getBillingFileStorageBasePath } from './billing-file-storage';

interface ProcessOptions {
  userId?: string;
  originalName?: string;
  password?: string;
}

/**
 * Stage flow
 */
const stages: Array<{ name: BillingFileStageName; fork?: (file: BillingFile) => boolean }> = [
  // process => extract from .zip (if required), validate expected format and parse data (in forked thread)
  { name: BillingFileStageName.Process, fork: (file) => Boolean(file.type?.parserId) },
  // check data thresholds
  { name: BillingFileStageName.Check },
  // store into archive
  { name: BillingFileStageName.Archive }
];

@Provider()
export class BillingFilePipeline {
  constructor(
    private entityManager: EntityManager,
    private actionBus: ActionBus,
    private fileStorage: FileStorage,
    private uploadStorage: UploadStorage,
    private jobBus: JobBus,
    private eventBus: EventBus
  ) {}

  /**
   * Process a file with pipeline
   * @param fileId
   * @param path
   * @param options
   */
  async process(fileId: string, path: string, { originalName, password, userId }: ProcessOptions = {}) {
    const stage = this.createNextStage(fileId);
    if (stage) {
      // move to pipeline directory
      const filename = sanitizeFilename(originalName || basename(path));
      stage.path = await this.fileStorage.move(path, `${this.getStoragePath(fileId)}/${filename}`);
      stage.createdUserId = userId;
      stage.data = password ? { password } : {};
      await this.addStage(stage);
    } else {
      throw new Error('Start stage not found'); // this should not happen
    }
  }

  /**
   * Process file from upload storage
   */
  async upload(fileId: string, uploadId: string, options: ProcessOptions = {}) {
    const path = this.uploadStorage.getAbsolutePath(uploadId);
    await this.process(fileId, path, options);

    // uploaded from e-mail?
    const metadata = this.uploadStorage.getFileMetadata(uploadId);
    if (metadata.email) {
      // store as download
      const download = await this.entityManager.findOneBy(BillingFileDownload, { fileId });
      if (download) {
        await this.entityManager.remove(download);
      }
      const now = new Date();
      await this.entityManager.save(
        create(BillingFileDownload, {
          fileId,
          sourceType: DataSourceType.Email,
          sourceUrl: metadata.email.url,
          startDate: now,
          finishDate: now,
          status: BillingFileDownloadStatus.Finished,
          data: metadata.email,
          userId: metadata.userId
        })
      );
    }
  }

  /**
   * Retry failed pipeline
   * @param fileId
   */
  async retry(fileId: string) {
    const stage = await this.entityManager
      .createQueryBuilder(BillingFileStage, 'stage')
      .where('stage.fileId = :fileId', { fileId })
      .orderBy('stage.createdDate', 'DESC')
      .getOne();

    if (stage && stage.status === BillingFileStageStatus.Error) {
      await this.addStage(stage);
    } else {
      throw new InvalidArgumentError('Can not retry pipeline on this file');
    }
  }

  /**
   * Add stage to pipeline
   * @param stage
   * @todo Save in transaction
   */
  private async addStage(stage: BillingFileStage): Promise<BillingFileStage> {
    // check path
    if (stage.path) {
      const exists = await pathExists(stage.path);
      if (!exists) {
        throw new Error(`Path "${stage.path}" not found`);
      }
    } else {
      throw new Error('Stage path not defined');
    }

    stage.status = BillingFileStageStatus.Pending;
    await this.entityManager.save(stage);

    // queue execute stage job
    stage.jobId = await this.jobBus.addJob([BillingFilePipeline, 'executeStage'], pick(stage, ['id', 'data']));
    await this.entityManager.save(stage);

    return stage;
  }

  /**
   * Start stage
   * @param stage
   */
  private async startStage(stage: BillingFileStage): Promise<void> {
    stage.status = BillingFileStageStatus.Started;
    stage.startDate = new Date();
    await this.entityManager.save(stage);

    // set file status to processing
    const file = await this.getStageFile(stage);
    file.status = BillingFileStatus.Processing;
    file.error = null!;
    await this.entityManager.save(file);
  }

  /**
   * Set stage as finished
   * @param stage
   * @param options
   */
  private async finishStage(stage: BillingFileStage, options: { file?: Partial<BillingFile> } = {}): Promise<void> {
    stage.status = BillingFileStageStatus.Finished;
    stage.finishDate = new Date();
    await this.entityManager.save(stage);

    const file = await this.getStageFile(stage);

    // save file data when exported by handler
    if (options.file) {
      Object.assign(file, options.file);
      await this.entityManager.save(file);
    }

    // add next stage to pipeline from defined flow if available
    const nextStage = this.createNextStage(file.id, stage);
    if (nextStage) {
      await this.addStage(nextStage);
    } else {
      // set file status to processed
      file.status = BillingFileStatus.Processed;
      await this.entityManager.save(file);

      this.eventBus.verbose('finished', 'File processing finished', file);

      // otherwise clear file pipeline directory
      await this.fileStorage.delete(this.getStoragePath(stage.fileId));
    }
  }

  /**
   * Set stage as failed
   */
  private async failStage(stage: BillingFileStage, error: unknown): Promise<void> {
    // add file to error context
    if (error instanceof BillingFileDataError && stage.path) {
      error.data.file = basename(stage.path);
    }

    stage.status = BillingFileStageStatus.Error;
    stage.message = getErrorMessage(error);
    await this.entityManager.save(stage);

    // set file status to failed
    const file = await this.getStageFile(stage);
    file.status = BillingFileStatus.Failed;
    await this.entityManager.save(file);

    // try to save the error details .. it can fail on unicode-related issues
    try {
      file.error = toErrorPayload(error);
      await this.entityManager.save(file);
    } catch (e) {
      this.eventBus.error('failed:error', e);
    }

    this.eventBus.verbose('failed', 'File processing failed', file);
  }

  /**
   * Execute stage by ID
   */
  @Worker<{ id: string; data?: BillingFileStage['data'] }>({
    description: 'Execute billing file pipeline stage',
    retention: 7,
    toArgs: ({ id, data }) => [id, data]
  })
  async executeStage(stageId: string, data?: BillingFileStageData) {
    const stage = await this.getStage(stageId);
    const file = await this.getStageFile(stage);
    stage.data = data;

    const { fork } = stages.find(({ name }) => name === stage.name) ?? {};
    try {
      await this.startStage(stage);

      const path = await this.actionBus.execute(
        ExecuteBillingFileStage,
        { fileId: file.id, path: stage.path!, data, context: { name: stage.name } },
        { fork: fork?.(file) ?? false }
      );
      if (typeof path === 'string') {
        stage.path = path;
        await this.entityManager.save(stage);
      }

      await this.finishStage(fork ? await this.getStage(stageId) : stage);
    } catch (error) {
      await this.failStage(fork ? await this.getStage(stageId) : stage, error);
    }
  }

  /**
   * Create instance of next stage for a given file
   */
  private createNextStage(fileId: string, previousStage?: BillingFileStage): BillingFileStage | undefined {
    const stageIndex = previousStage ? stages.findIndex(({ name }) => name === previousStage.name) + 1 : 0;
    const nextStage = stages[stageIndex];
    if (nextStage) {
      return create(BillingFileStage, {
        fileId,
        name: nextStage.name,
        path: previousStage?.path,
        createdUserId: previousStage?.createdUserId,
        data: previousStage?.data
      });
    } else {
      return undefined;
    }
  }

  /**
   * Return stage instance
   */
  private async getStage(stageId: string) {
    return this.entityManager.findOneByOrFail(BillingFileStage, { id: stageId });
  }

  /**
   * Return stage file instance
   */
  private async getStageFile(stage: BillingFileStage) {
    const file = await this.entityManager.findOne(BillingFile, {
      where: { id: stage.fileId },
      relations: { type: true, vendor: { accounts: true }, account: true }
    });
    if (file) {
      return file;
    } else {
      throw new Error('Stage file not found');
    }
  }

  /**
   * Return stage file URL
   */
  getStageUrl(stage: BillingFileStage) {
    return stage.path
      ? this.fileStorage.createUrl(this.getStoragePath(stage.fileId) + '/' + basename(stage.path))
      : undefined;
  }

  /**
   * Return file pipeline storage directory
   */
  private getStoragePath(fileId: string) {
    return getBillingFileStorageBasePath(fileId) + '/pipeline';
  }
}

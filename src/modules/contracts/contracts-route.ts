import { authMatch } from '~/common/ui/identity';
import { Route } from '~/common/ui/routing';
import { ContractRead, ContractWrite } from './contract-permissions';

export const contractsRoute: Route = {
  path: 'contracts',
  canMatch: [authMatch],
  data: {
    title: 'Contracts',
    authorize: ContractRead,
    menu: {
      icon: 'like'
    }
  },
  children: [
    {
      path: '',
      pathMatch: 'full',
      loadComponent: () => import('./contracts-component').then((c) => c.ContractsComponent)
    },
    {
      path: 'create',
      loadComponent: () => import('./contract-form-component').then((c) => c.ContractFormComponent),
      data: {
        title: 'Create contract',
        authorize: ContractWrite
      }
    },
    {
      path: 'edit/:contractId',
      loadComponent: () => import('./contract-form-component').then((c) => c.ContractFormComponent),
      data: {
        title: 'Edit contract',
        authorize: ContractWrite
      }
    }
  ]
};

import { authMatch } from '~/common/ui/identity';
import { Route } from '~/common/ui/routing';
import { ReportGroupRead } from './report-group-permissions';
import { ReportsMenuConfig } from './reports-menu-config';

export const reportsRoute: Route = {
  path: 'reports',
  canMatch: [authMatch],
  data: {
    authorize: ReportGroupRead,
    menu: {
      config: ReportsMenuConfig
    }
  },
  children: [
    {
      path: '**',
      loadComponent: () => import('./report-group-component').then((c) => c.ReportGroupComponent)
    }
  ]
};

import { usersRoute } from '~/common/identity/users-route';
import { authMatch } from '~/common/ui/identity';
import { Route } from '~/common/ui/routing';
import { vendorsRoute } from '~/common/vendors/vendors-route';
import { billingFileTypesRoute } from '~/modules/billing/billing-file-types-route';
import { reportsAdminRoute } from '~/modules/reports/reports-admin-route';

const sharedRoutes = {
  billingFileTypes: billingFileTypesRoute,
  reports: reportsAdminRoute,
  users: usersRoute,
  vendors: vendorsRoute
};

export const adminRoute = (children: Array<keyof typeof sharedRoutes | Route>): Route => ({
  path: 'admin',
  canMatch: [authMatch],
  children: children.map((child) => (typeof child === 'string' ? sharedRoutes[child] : child)),
  data: {
    title: 'Administration',
    authorize: 'Insights:Manage',
    menu: {
      icon: 'setting'
    }
  }
});

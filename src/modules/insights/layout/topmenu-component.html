@if (context$ | async; as c) {
  <ul class="topmenu list-unstyled d-flex">
    @if (c.service && c.service.kbUrl && c.user.can('*:*')) {
      <li>
        <a [href]="c.service.kbUrl" nz-tooltip nzTooltipTitle="Knowledge base" target="_blank" style="color: inherit">
          <span nz-icon nzType="read"></span>
        </a>
      </li>
    }
    @if (c.services.length > 1) {
      <li>
        <span
          nz-icon
          nzType="appstore"
          nz-dropdown
          [nzDropdownMenu]="temServices"
          nzPlacement="bottomCenter"
          class="cursor-pointer"
        ></span>
        <nz-dropdown-menu #temServices="nzDropdownMenu">
          <ul nz-menu>
            <li nz-menu-item nzDisabled>TEM services</li>
            <li nz-menu-divider></li>
            @for (service of c.services; track service.id) {
              <li nz-menu-item [routerLink]="['/', service.id]">
                <span nz-icon [nzType]="service.icon ?? 'bank'" class="mr-1"></span>
                {{ service.name }}
              </li>
            }
          </ul>
        </nz-dropdown-menu>
      </li>
    }
    <li>
      <usermenu />
    </li>
  </ul>
}

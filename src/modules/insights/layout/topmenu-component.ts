import { CommonModule } from '@angular/common';
import { ChangeDetectionStrategy, Component, inject } from '@angular/core';
import { RouterLink } from '@angular/router';
import { combineLatest, map } from 'rxjs';
import { CurrentService } from '~/common/services/current-service';
import { ServicesApiClient } from '~/common/services/services-api-client';
import { CurrentUser } from '~/common/ui/identity';
import { NgZorroModule } from '~/common/ui/ng-zorro';
import { UsermenuComponent } from './usermenu-component';

@Component({
  selector: 'topmenu',
  imports: [CommonModule, NgZorroModule, RouterLink, UsermenuComponent],
  templateUrl: './topmenu-component.html',
  styleUrl: './topmenu-component.less',
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class TopmenuComponent {
  context$ = combineLatest([
    inject(CurrentUser).asObservable(),
    inject(CurrentService).asObservable(),
    inject(ServicesApiClient).getServices()
  ]).pipe(map(([user, service, services]) => ({ user, service, services })));
}

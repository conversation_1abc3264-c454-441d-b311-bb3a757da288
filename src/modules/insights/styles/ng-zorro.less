@root-entry-name: 'default';

@import 'ng-zorro-antd/style/entry.less';
@import 'ng-zorro-antd/alert/style/entry.less';
@import 'ng-zorro-antd/auto-complete/style/entry.less';
@import 'ng-zorro-antd/avatar/style/entry.less';
@import 'ng-zorro-antd/badge/style/entry.less';
@import 'ng-zorro-antd/breadcrumb/style/entry.less';
@import 'ng-zorro-antd/button/style/entry.less';
@import 'ng-zorro-antd/card/style/entry.less';
@import 'ng-zorro-antd/checkbox/style/entry.less';
@import 'ng-zorro-antd/collapse/style/entry.less';
@import 'ng-zorro-antd/date-picker/style/entry.less';
@import 'ng-zorro-antd/descriptions/style/entry.less';
@import 'ng-zorro-antd/divider/style/entry.less';
@import 'ng-zorro-antd/drawer/style/entry.less';
@import 'ng-zorro-antd/dropdown/style/entry.less';
@import 'ng-zorro-antd/empty/style/entry.less';
@import 'ng-zorro-antd/form/style/entry.less';
@import 'ng-zorro-antd/grid/style/entry.less';
@import 'ng-zorro-antd/image/style/entry.less';
@import 'ng-zorro-antd/input-number/style/entry.less';
@import 'ng-zorro-antd/layout/style/entry.less';
@import 'ng-zorro-antd/menu/style/entry.less';
@import 'ng-zorro-antd/message/style/entry.less';
@import 'ng-zorro-antd/modal/style/entry.less';
@import 'ng-zorro-antd/page-header/style/entry.less';
@import 'ng-zorro-antd/pagination/style/entry.less';
@import 'ng-zorro-antd/popover/style/entry.less';
@import 'ng-zorro-antd/progress/style/entry.less';
@import 'ng-zorro-antd/radio/style/entry.less';
@import 'ng-zorro-antd/result/style/entry.less';
@import 'ng-zorro-antd/segmented/style/entry.less';
@import 'ng-zorro-antd/select/style/entry.less';
@import 'ng-zorro-antd/spin/style/entry.less';
@import 'ng-zorro-antd/statistic/style/entry.less';
@import 'ng-zorro-antd/steps/style/entry.less';
@import 'ng-zorro-antd/switch/style/entry.less';
@import 'ng-zorro-antd/table/style/entry.less';
@import 'ng-zorro-antd/tabs/style/entry.less';
@import 'ng-zorro-antd/tag/style/entry.less';
@import 'ng-zorro-antd/timeline/style/entry.less';
@import 'ng-zorro-antd/tooltip/style/entry.less';
@import 'ng-zorro-antd/tree-select/style/entry.less';
@import 'ng-zorro-antd/typography/style/entry.less';
@import 'ng-zorro-antd/upload/style/entry.less';

// forms
.ant-form-horizontal {
  .ant-form-item {
    // smaller bottom margin
    margin-bottom: 1rem;

    // no bottom margin for last form item
    &:last-child {
      margin-bottom: 0;
    }
  }
}
// align form label to top
nz-form-label.align-start > label {
  align-items: flex-start;
}

// tags
.ant-tag {
  // inherit cursor from parent element (eg. pointer when inside a link) instead of "cursor: default" (defined by ant)
  cursor: inherit;

  &.size-small {
    line-height: 16px;
    padding: 0 4px;
    font-size: 11px;
  }
}

// timeline
.ant-timeline {
  // no bottom margin for last timeline item
  .ant-timeline-item:last-child {
    padding-bottom: 0;
  }
}
// timeline with user avatars
nz-timeline.user-timeline {
  display: block;

  .ant-timeline {
    margin-left: 12px;
  }
  .ant-timeline-item-head-custom {
    top: 20px;
  }
  .ant-timeline-item-content {
    top: 0;
    margin-left: 32px;
    padding-bottom: 0.4rem;

    .date {
      font-size: 0.75rem;
      color: @text-color-secondary;
    }
  }
}

// card
.ant-card {
  // reduce top padding when having tabs inside a card
  &.with-tabs .ant-card-body {
    padding-top: 8px;
  }
  &.p-0 .ant-card-body {
    padding: 0;
  }
}

// popover
.ant-popover {
  max-width: 500px;
}

// breadcrumbs
.ant-breadcrumb-overlay-link {
  // menu link positioning fix
  .anticon-down {
    vertical-align: -0.05em;
  }
}

// tables
.ant-table-content > table {
  background-color: @table-bg;

  > tbody > tr > td {
    vertical-align: top;
  }
}
.ant-table-wrapper {
  // additional "extra small" size
  &.size-xs {
    th,
    td {
      padding: 0 0.5rem !important;
      font-size: 0.8rem;
    }
  }
}
.ant-table-summary {
  background-color: #fafafa !important;

  td {
    font-weight: 500;
  }
}
.ant-table-custom-column .ant-table-summary {
  // fix positioning for summary row
  .ant-table-row,
  tr {
    display: flex;
    justify-content: space-between;
    width: 100%;
  }
}

// layout
.ant-layout {
  // layout with full page height instead of default "min-height: 0"
  min-height: 100vh !important;
}

// page header
.ant-page-header-content {
  // slightly smaller top padding for page header content
  padding-top: 8px !important;
}

// drawer
.ant-drawer {
  .ant-drawer-title {
    font-size: 1.4em;
    font-weight: 400;
  }
}

// tabs
.ant-tabs {
  // hide tabs when requested
  &.hide-tabs .ant-tabs-nav {
    display: none;
  }

  // remove top padding from tabs (eg. on the top of the dialog)
  &.pt-0 .ant-tabs-tab {
    padding-top: 0;
  }

  &.pb-0 .ant-tabs-nav {
    margin-bottom: 1px;
  }
}

// descriptions
.ant-descriptions {
  .ant-descriptions-item-label {
    vertical-align: top;
    width: 25%;
  }
  .ant-descriptions-item-content {
    vertical-align: top;
  }

  &.l-30 .ant-descriptions-item-label {
    width: 30%;
  }
  &.l-40 .ant-descriptions-item-label {
    width: 40%;
  }
  &.l-50 .ant-descriptions-item-label {
    width: 50%;
  }

  &.editable .ant-descriptions-item-label {
    vertical-align: inherit;
  }
}

// colors
// @hack @todo refactor
.ant-blue {
  color: #096dd9 !important;
}
.ant-green {
  color: #389e0d !important;
}

// select
.ant-select-item-option-content {
  .detail {
    line-height: 1rem;
    font-size: 0.75rem;
    color: @text-color-secondary;
    margin: 0;
  }
}

.ant-select-item-option-selected {
  .ant-select-item-option-content {
    .detail {
      font-weight: normal !important;
    }
  }
}

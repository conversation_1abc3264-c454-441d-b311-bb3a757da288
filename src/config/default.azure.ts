import { type PartialConfig } from './config';

const config: PartialConfig = {
  application: {
    url: 'https://tem.novartis.net'
  },
  database: {
    host: 'pgsqlflexweuprdtem02.postgres.database.azure.com',
    ssl: true
  },
  http: {
    server: {
      redirect: {
        '/(.*)': {
          hostname: 'waw-tem-prd-westeurope-01.azurewebsites.net',
          target: 'https://tem.novartis.net/$1'
        }
      }
    }
  },
  azure: {
    keyvault: {
      name: 'kv-tem-prd-02'
    },
    documentIntelligence: {
      url: 'https://azcsfr-tem-prd-westeurope-01.cognitiveservices.azure.com/'
    },
    translator: {
      url: 'https://trnapi-tem-prd-westeurope-01.cognitiveservices.azure.com'
    }
  },
  files: {
    // persistent storage in App Service
    // https://learn.microsoft.com/en-us/azure/app-service/configure-custom-container?tabs=debian&pivots=container-linux#use-persistent-shared-storage
    baseDir: '/home/<USER>/files'
  },
  email: {
    smtp: {
      host: 'smtp-us.ser.proofpoint.com',
      port: 25,
      auth: {
        user: 'f3da0631-31a8-4c20-bdab-464a70aad869',
        password: ''
      }
    }
  },
  sentry: {
    // @see https://sentry.tem.novartis.net/settings/novartis/projects/tem-backend/keys/
    dsn: 'https://<EMAIL>/2'
  },
  healthChecks: {
    status: {
      checkId: '62c44dc7-87ec-4475-86be-3220f87f9ca8'
    }
  },
  modules: {
    billing: {
      download: {
        enabled: true
      }
    },
    insights: {
      csp: {
        imgSrc: ['tem.novartis.net']
      },
      app: {
        identity: {
          oauth: true
        },
        sentry: {
          dsn: 'https://<EMAIL>/3'
        }
      }
    }
  },
  services: {
    mobile: {
      vendors: {
        usVerizon: {
          rto: {
            download: {
              enabled: true,
              host: '*************', // Novartis NAT IP for "mft.verizonwireless.com"
              username: 'novartis_id',
              healthCheckId: '9f89cd35-073a-41e5-9a30-4d40543f96c2'
            }
          }
        }
      }
    },
    system: {
      migration: {
        source: 'https://tem.novartis.net'
      }
    }
  }
};

export default config;

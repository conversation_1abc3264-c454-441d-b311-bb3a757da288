import { ActionsConfig } from '~/common/actions/actions-config';
import { ApplicationConfig } from '~/common/application/application-config';
import { AzureConfig } from '~/common/azure/azure-config';
import { ConfigModule } from '~/common/config/config-module';
import { CurrenciesConfig } from '~/common/currencies/currencies-config';
import { DatabaseConfig } from '~/common/database/database-config';
import { DirectoryConfig } from '~/common/directory/directory-config';
import { EmailConfig } from '~/common/email/email-config';
import { EncryptionConfig } from '~/common/encryption/encryption-config';
import { FilesConfig } from '~/common/files/files-config';
import { HealthChecksConfig } from '~/common/healthchecks/healthchecks-config';
import { HttpConfig } from '~/common/http/http-config';
import { IdentityConfig } from '~/common/identity/identity-config';
import { JobsConfig } from '~/common/jobs/jobs-config';
import { LocationConfig } from '~/common/location/location-config';
import { LoggerConfig } from '~/common/logger/logger-config';
import { PlaywrightConfig } from '~/common/playwright/playwright-config';
import { object, type Static } from '~/common/schema';
import { SentryConfig } from '~/common/sentry/sentry-config';
import { UploadsConfig } from '~/common/uploads/uploads-config';
import { type PartialDeep } from '~/common/utils/object/object-types';
import { BillingConfig } from '~/modules/billing/billing-config';
import { InsightsConfig } from '~/modules/insights/insights-config';
import { MobileConfig } from '~/services/mobile/mobile-config';
import { SystemConfig } from '~/services/system/system-config';

export const Config = object({
  application: ApplicationConfig,
  database: DatabaseConfig,
  http: HttpConfig,
  logger: LoggerConfig,
  actions: ActionsConfig,
  jobs: JobsConfig,
  directory: DirectoryConfig,
  email: EmailConfig,
  sentry: SentryConfig,
  azure: AzureConfig,
  playwright: PlaywrightConfig,
  currencies: CurrenciesConfig,
  encryption: EncryptionConfig,
  files: FilesConfig,
  uploads: UploadsConfig,
  identity: IdentityConfig,
  healthChecks: HealthChecksConfig,
  location: LocationConfig,
  modules: object({
    insights: InsightsConfig,
    billing: BillingConfig
  }),
  services: object({
    mobile: MobileConfig,
    system: SystemConfig
  })
});

export type Config = Static<typeof Config>;
export type PartialConfig = PartialDeep<Config>;

export function config(partial?: PartialConfig) {
  return ConfigModule.withConfig(__dirname, Config, partial);
}

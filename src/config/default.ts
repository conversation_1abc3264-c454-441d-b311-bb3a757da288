import { env, rootDir, srcDir } from '~/common/config';
import { getPlatformHost, isLocalPlatform } from '~/common/platform';
import { type Config } from './config';

const config: Config = {
  application: {
    environment: env('app_environment') ?? 'local',
    revision: env('app_revision') ?? 'HEAD',
    url: isLocalPlatform() ? 'http://localhost:3000' : `https://${getPlatformHost()}`,
    poweredBy: 'TEM team <<EMAIL>>'
  },
  database: {
    host: 'localhost',
    port: 5432,
    name: 'tem',
    user: 'tem_owner',
    password: 'tem',
    app: 'TEM Insights',
    backupDir: '{{ files.baseDir }}/database'
  },
  http: {
    server: {
      port: 3000
    },
    client: {
      userAgent: 'Mozilla/5.0 (compatible; Technology Expense Management; +{{ application.url }}) AppID/44197'
    }
  },
  logger: {
    level: 'verbose',
    files: {
      enabled: true,
      baseDir: '{{ files.baseDir }}/logs'
    }
  },
  actions: {
    cli: `${srcDir}/cli`
  },
  jobs: {
    scheduler: true
  },
  directory: {
    aps: {
      baseUrl: 'https://apigweu.eu.novartis.net',
      requesterId: 'SYS_44197_TEM_PROD',
      clientId: '',
      clientSecret: ''
    },
    iga: {
      account: 'nvs8prdeuwest1.eu-west-1',
      username: '<EMAIL>',
      privateKey: '',
      passphrase: '',
      logsDir: '{{ logger.files.baseDir }}/iga'
    }
  },
  email: {
    queue: {
      from: '<EMAIL>'
    },
    smtp: {
      host: 'localhost'
    }
  },
  sentry: {
    dsn: ''
  },
  azure: {
    documentIntelligence: {
      url: 'https://westeurope.api.cognitive.microsoft.com/'
    },
    identity: {
      tenantId: 'f35a6974-607f-47d4-82d7-ff31d7dc53a5',
      domain: 'novartis.net',
      clientId: '1bb4f247-1911-4988-8198-ed564ab63e7d',
      clientCertificate: {
        thumbprint: 'C2106740DB28B651153263DAD23ACE73BFA9A674',
        privateKey: ''
      }
    },
    keyvault: {
      name: ''
    },
    openai: {
      endpoint: 'https://tem.openai.azure.com',
      apiKey: 'azure-openai-api-key'
    },
    translator: {
      url: 'https://api.cognitive.microsofttranslator.com',
      key: ''
    }
  },
  playwright: {
    dataDir: '{{ files.baseDir }}/playwright',
    headless: true
  },
  currencies: {
    oxr: {
      appId: ''
    }
  },
  encryption: {
    key: 'key'
  },
  files: {
    baseDir: `${rootDir}/files`
  },
  uploads: {
    maxFileSize: ********** // max 2 GB file upload
  },
  identity: {
    jwt: {
      secret: 'secret',
      expiration: '3 hours'
    },
    oauth: {},
    sso: {},
    users: {
      cacheTimeout: 300000,
      autoregister: []
    }
  },
  healthChecks: {
    baseUrl: 'https://healthchecks.tem.novartis.net',
    status: {
      checkId: '',
      interval: 60
    }
  },
  location: {
    sites: {
      mailbox: '<EMAIL>'
    }
  },
  modules: {
    insights: {
      devServerUrl: 'http://localhost:4200',
      distDir: `${rootDir}/dist/modules/insights/app/browser`,
      csp: {
        // only internal sites for iframes (BI platforms, SSO authentication)
        frameSrc: [
          '*.novartis.com',
          '*.novartis.net',
          'login.microsoftonline.com',
          'device.login.microsoftonline.com',
          '*.powerbi.com'
        ],
        connectSrc: ['sentry.tem.novartis.net']
      },
      app: {
        title: 'TEM Insights',
        environment: '{{ application.environment }}',
        revision: '{{ application.revision }}',
        identity: {
          tokenRefreshInterval: 300
        },
        sentry: {
          dsn: ''
        },
        features: {
          events: true
        }
      }
    },
    billing: {
      download: {
        enabled: false
      }
    }
  },
  services: {
    system: {
      logs: {
        auth: {
          username: 'logs',
          password: 'logs'
        }
      },
      migration: {
        source: '{{ application.url }}',
        users: ['DANISPA2']
      }
    },
    mobile: {
      vendors: {
        usVerizon: {
          rto: {
            download: {
              enabled: false,
              host: '',
              username: '',
              privateKey: '',
              passphrase: ''
            },
            serve: {
              username: 'x',
              password: 'x'
            }
          }
        }
      }
    }
  }
};

export default config;

{"name": "tem", "version": "0.0.0", "private": true, "packageManager": "yarn@1.22.22", "scripts": {"dev": "run-p *:dev", "prebuild": "<PERSON><PERSON><PERSON> dist", "build": "run-s *:build", "backend:build": "nest build --type-check", "backend:dev": "rimraf dist && nest start app --debug --watch", "backend:i18n": "yarn cli I18N.translateDictionaries", "insights:build": "ng build insights --configuration production", "insights:dev": "rimraf .angular && ng serve insights", "insights:i18n": "ng extract-i18n --format=json --output-path src/modules/insights/i18n --outFile=en.json", "cli": "ts-node src/cli.ts", "i18n": "run-s *:i18n", "migration": "yarn cli Database.createMigration", "test": "jest", "format": "prettier --write 'src/**/*.{html,js,json,ts}'", "lint": "eslint --cache --fix 'src/**/*.ts'", "sentry:sourcemaps": "sentry-cli sourcemaps inject ./dist && sentry-cli sourcemaps upload ./dist"}, "dependencies": {"@angular/animations": "^19.2.3", "@angular/cdk": "^19.2.6", "@angular/common": "^19.2.3", "@angular/compiler": "^19.2.3", "@angular/core": "^19.2.3", "@angular/forms": "^19.2.3", "@angular/localize": "^19.2.3", "@angular/material": "^19.2.6", "@angular/platform-browser": "^19.2.3", "@angular/platform-browser-dynamic": "^19.2.3", "@angular/router": "^19.2.3", "@ant-design/colors": "^8.0.0", "@ant-design/icons-angular": "^19.0.0", "@azure-rest/ai-document-intelligence": "1.0.0-beta.3", "@azure-rest/ai-translation-text": "^1.0.1", "@azure/identity": "^4.8.0", "@azure/keyvault-secrets": "^4.9.0", "@azure/monitor-opentelemetry": "^1.9.0", "@azure/msal-node": "^2.16.2", "@casl/ability": "^6.7.3", "@golevelup/nestjs-discovery": "^4.0.3", "@microsoft/microsoft-graph-client": "^3.0.7", "@nestjs/common": "^10.4.15", "@nestjs/core": "^10.4.15", "@nestjs/platform-express": "^10.4.15", "@nestjs/platform-socket.io": "^10.4.15", "@nestjs/websockets": "^10.4.15", "@sentry/angular": "9.8.0", "@sentry/node": "9.8.0", "@sentry/tracing": "7.120.3", "@sinclair/typebox": "^0.34.31", "@typeschema/typebox": "^0.14.0", "archiver": "^7.0.1", "axios": "^1.8.4", "bytes": "^3.1.2", "chalk": "^4.1.2", "class-transformer": "0.3.1", "class-validator": "^0.14.1", "cli-highlight": "^2.1.11", "commander": "^13.1.0", "compression": "^1.8.0", "cookie": "^1.0.2", "cron-parser": "^5.0.6", "cronstrue": "^2.56.0", "cross-fetch": "^4.1.0", "crypto-js": "^4.2.0", "csv-parser": "^3.2.0", "csv-stringify": "^6.5.2", "echarts": "^5.6.0", "exceljs": "^4.4.0", "express-basic-auth": "^1.2.1", "extract-zip": "^2.0.1", "fast-glob": "^3.3.3", "flag-icons": "^7.3.2", "fs-extra": "^11.3.0", "helmet": "^7.1.0", "https-proxy-agent": "^7.0.6", "iconv-lite": "^0.6.3", "is-stream": "^2.0.1", "js-levenshtein-esm": "^2.0.0", "jsdom": "^26.0.0", "jsonwebtoken": "^9.0.2", "jszip": "^3.10.1", "lodash": "^4.17.21", "marked": "^15.0.7", "mime-types": "^2.1.35", "nanoid": "^3.2.0", "neat-csv": "^6.0.1", "ng-zorro-antd": "^19.1.0", "ngx-markdown": "^19.1.1", "node-unrar-js": "^2.0.2", "nodemailer": "^6.10.0", "openai": "^4.89.0", "passport": "~0.4.1", "passport-strategy": "^1.0.0", "pg": "^8.14.1", "pg-boss": "^10.1.6", "pg-copy-streams": "^6.0.6", "pg-listen": "^1.7.0", "playwright-chromium": "^1.51.1", "powerbi-client": "^2.23.1", "reflect-metadata": "^0.2.2", "rxjs": "^7.8.2", "rxjs-for-await": "^1.0.0", "rxjs-stream": "^5.0.0", "sanitize-html": "^2.15.0", "serve-index": "^1.9.1", "snowflake-sdk": "^1.15.0", "socket.io-client": "^4.8.1", "ssh2-sftp-client": "^11.0.0", "strip-ansi": "^6.0.1", "tinymce": "^6.8.4", "typeorm": "^0.3.20", "unzipper": "^0.12.3", "validator": "^13.12.0", "winston": "^3.17.0", "winston-daily-rotate-file": "^5.0.0", "xlsx": "^0.18.5", "xlsx-populate": "^1.21.0", "xml2js": "^0.6.2", "yaml": "^2.7.0", "zone.js": "^0.15.0"}, "devDependencies": {"@angular-devkit/build-angular": "^19.2.4", "@angular/cli": "^19.2.4", "@angular/compiler-cli": "^19.2.3", "@angular/language-service": "^19.2.3", "@microsoft/microsoft-graph-types": "^2.40.0", "@nestjs/cli": "^11.0.5", "@nestjs/schematics": "^11.0.2", "@sentry/cli": "^2.42.4", "@swc/cli": "^0.6.0", "@swc/core": "^1.11.13", "@swc/helpers": "^0.5.15", "@swc/jest": "^0.2.37", "@types/archiver": "^6.0.3", "@types/bytes": "^3.1.5", "@types/compression": "^1.7.5", "@types/cookie-parser": "^1.4.8", "@types/express": "5.0.1", "@types/fs-extra": "^11.0.4", "@types/iconv": "^3.0.4", "@types/jest": "^29.5.14", "@types/jsdom": "^21.1.7", "@types/jsonwebtoken": "9.0.9", "@types/ldap-escape": "^2.0.2", "@types/lodash": "^4.17.16", "@types/luxon": "^3.4.2", "@types/mime-types": "^2.1.4", "@types/multer": "^1.4.12", "@types/node": "^22.13.12", "@types/nodemailer": "^6.4.17", "@types/passport": "^1.0.17", "@types/passport-strategy": "^0.2.38", "@types/pg-copy-streams": "^1.2.5", "@types/sanitize-html": "^2.13.0", "@types/serve-index": "^1.9.4", "@types/snowflake-sdk": "^1.6.24", "@types/ssh2-sftp-client": "^9.0.4", "@types/unzipper": "^0.10.11", "@types/useragent": "^2.3.4", "@types/uuid": "^10.0.0", "@types/xml2js": "^0.4.14", "@typescript-eslint/eslint-plugin": "^8.27.0", "@typescript-eslint/parser": "^8.27.0", "eslint": "^9.23.0", "eslint-config-prettier": "^10.1.1", "eslint-plugin-import": "^2.31.0", "jest": "^29.7.0", "jest-junit": "^16.0.0", "npm-run-all": "^4.1.5", "prettier": "^3.5.3", "rimraf": "^6.0.1", "ts-node": "^10.9.2", "tsconfig-paths": "^4.2.0", "tslib": "^2.8.1", "type-fest": "^4.37.0", "typescript": "~5.7.3"}}